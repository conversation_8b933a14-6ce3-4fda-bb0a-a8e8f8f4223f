{"private": true, "scripts": {"dev": "npm run watch", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "build": "npm run production"}, "dependencies": {"@bugsnag/js": "^7.11.0", "@bugsnag/plugin-vue": "^7.11.0", "@fortawesome/fontawesome-free": "^5.12.1", "apexcharts": "^3.32.0", "axios": "^0.21.4", "bootstrap": "^4.6.0", "bootstrap-vue": "^2.11.0", "chart.js": "^2.9.4", "clipboard-copy": "^3.1.0", "compressorjs": "^1.0.6", "dayjs": "^1.10.7", "debounce": "^1.2.1", "highlight.js": "^9.18.1", "jquery": "^3.6.0", "nprogress": "^0.2.0", "object-path": "^0.11.5", "perfect-scrollbar": "^1.5.0", "popper.js": "^1.16.1", "quill-image-uploader": "^1.2.2", "read-excel-file": "^5.8.6", "roboto-fontface": "*", "seedrandom": "^3.0.5", "socicon": "^3.0.5", "tooltip.js": "^1.3.2", "vee-validate": "^3.2.5", "vue": "^2.6.14", "vue-apexcharts": "^1.6.2", "vue-axios": "^2.1.4", "vue-cleave-component": "^2.1.3", "vue-highlight.js": "^3.1.0", "vue-i18n": "^8.15.3", "vue-quill-editor": "^3.0.6", "vue-recaptcha-v3": "^1.9.0", "vue-router": "^3.1.5", "vue-select": "^3.10.3", "vue-sweetalert2": "^3.0.3", "vue2-datepicker": "^3.3.0", "vue2-perfect-scrollbar": "^1.2.0", "vuelayers": "^0.11.28", "vuelidate": "^0.7.5", "vuex": "^3.1.2"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/preset-env": "^7.15.6", "@vue/cli-plugin-babel": "^4.5.13", "autoprefixer": "^10.3.5", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^7.1.2", "cross-env": "^7.0", "eslint": "^7.24.0", "eslint-plugin-prettier": "^3.3.1", "laravel-mix": "^6.0.31", "laravel-mix-merge-manifest": "^2.0.0", "lodash": "^4.17.21", "postcss": "^8.2.9", "postcss-import": "^13.0.0", "postcss-loader": "^4.2.0", "postcss-rtlcss": "^3.3.0", "prettier": "^1.19.1", "sass": "1.32.12", "sass-loader": "^12.1.0", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14", "webpack": "^5.53.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions"]}