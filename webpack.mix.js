const mix = require('laravel-mix')
const config = require('./webpack.config')
require('laravel-mix-merge-manifest')

mix.setPublicPath('public').mergeManifest()

mix
  .js('resources/vue/main.js', 'dashboard/js/app.js')
  .vue()
  .options({
    postCss: [
      require('autoprefixer'),
      require('postcss-rtlcss')({
        safeBothPrefix: true,
      }),
    ],
  })
  .webpackConfig(config)

if (mix.inProduction()) {
  mix.sourceMaps().version()
}
