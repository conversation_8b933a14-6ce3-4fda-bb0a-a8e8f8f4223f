<?php

namespace Database\Seeders;

use App\Models\Nationality;
use Illuminate\Database\Seeder;

class NationalitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $nationalities = collect([
            [
                'id' => 'ly',
                'title' => [
                    'ar' => 'الليبية', 'en' => 'Libyan'],
                'country' => [
                    'ar' => 'ليبيا', 'en' => 'Libya']
            ],
            [
                'id' => 'eg',
                'title' => [
                    'ar' => 'المصرية', 'en' => 'Egyptian'],
                'country' => [
                    'ar' => 'مصر', 'en' => 'Egypt']
            ],
            [
                'id' => 'tn',
                'title' => [
                    'ar' => 'التونسية', 'en' => 'Tunisian'],
                'country' => [
                    'ar' => 'تونس', 'en' => 'Tunisia']
            ],
            [
                'id' => 'ps',
                'title' => [
                    'ar' => 'الفلسطينية', 'en' => 'Palestinian'],
                'country' => [
                    'ar' => 'فلسطين', 'en' => 'Palestine']
            ],
            [
                'id' => 'sy',
                'title' => [
                    'ar' => 'السورية', 'en' => 'Syrian'],
                'country' => [
                    'ar' => 'سوريا', 'en' => 'Syria']
            ],
            [
                'id' => 'dz',
                'title' => [
                    'ar' => 'الجزائرية', 'en' => 'Algerian'],
                'country' => [
                    'ar' => 'الجزائر', 'en' => 'Algeria']
            ],
            [
                'id' => 'dz',
                'title' => [
                    'ar' => 'المغربية', 'en' => 'Moroccan'],
                'country' => [
                    'ar' => 'المغرب', 'en' => 'Morocco']
            ],
            [
                'id' => 'SD',
                'title' => [
                    'ar' => 'السودانية', 'en' => 'Sudanese'],
                'country' => [
                    'ar' => 'السودان', 'en' => 'Sudan']
            ],
        ]);

        $nationalities->each(function ($natItem) {
            Nationality::updateOrCreate(['id' => $natItem['id']], $natItem);
        });

    }
}
