<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $languages = collect([
            ['id' => 'ar', 'title' => 'العربية'],
            ['id' => 'en', 'title' => 'English'],
        ]);

        $languages->each(function($language) {
            \App\Models\Language::updateOrCreate(['id' => $language['id']], $language);
        });
    }
}
