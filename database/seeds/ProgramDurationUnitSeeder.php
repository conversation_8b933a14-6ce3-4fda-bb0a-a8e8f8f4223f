<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProgramDurationUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $durationUnits = collect([
            [
                'id' => 1,
                'title' => [
                    'ar' => 'فصل',
                    'en' => 'Term'
                ]
            ],
            [
                'id' => 2,
                'title' => [
                    'ar' => 'سنة',
                    'en' => 'Year'
                ]
            ]
        ]);

        $durationUnits->each(function ($unit) {
            \App\Models\ProgramDurationUnit::updateOrCreate(['id' => $unit['id']], $unit);
        });
    }
}
