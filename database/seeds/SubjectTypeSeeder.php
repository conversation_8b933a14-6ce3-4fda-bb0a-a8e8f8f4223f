<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SubjectTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $subjectTypes = collect([
            [
                'id' => 1,
                'title' => [
                    'ar' => 'عامة', 'en' => 'General'
                ]
            ],
            [
                'id' => 2,
                'title' => [
                    'ar' => 'إجباري', 'en' => 'Compulsory'
                ]
            ],
            [
                'id' => 3,
                'title' => [
                    'ar' => 'إختياري', 'en' => 'Elective'
                ]
            ],
        ]);
        $subjectTypes->each(function ($subjectType) {
            \App\Models\SubjectType::updateOrCreate(['id' => $subjectType['id']], $subjectType);
        });
    }
}
