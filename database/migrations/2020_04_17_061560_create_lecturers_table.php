<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLecturersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.default'))->create('lecturers', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('qualification_id');
            $table->unsignedInteger('academic_rank_id');
            $table->date('start_date')->nullable()->default(null);
            $table->string('personal_website')->nullable()->default(null);
            $table->json('bio')->default(null)->nullable();
            $table->json('job_title')->default(null)->nullable();
            $table->json('general_major')->default(null)->nullable();
            $table->json('specialization')->default(null)->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('qualification_id')->references('id')->on('qualifications')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('academic_rank_id')->references('id')->on('academic_ranks')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.default'))->dropIfExists('lecturers');
    }
}
