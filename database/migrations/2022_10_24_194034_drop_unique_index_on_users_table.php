<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::connection(config('database.default'))->table('users', function (Blueprint $table) {
            $table->dropIndex('users_national_id_unique');
            $table->dropIndex('users_passport_number_unique');
            $table->dropIndex('users_phone_number_unique');
        });
    }
};
