<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.default'))->create('subjects', function (Blueprint $table) {
            $table->increments('id');
            $table->char('code', 10);
            $table->string('slug')->unique();
            $table->unsignedInteger('program_id');
            $table->json('title');
            $table->json('description');
            $table->tinyInteger('weekly_lecture_hours');
            $table->tinyInteger('weekly_tutorial_hours');
            $table->tinyInteger('credits');
            $table->tinyInteger('sub_semester')->default(0);
            $table->double('mid_mark');
            $table->double('final_mark');
            $table->double('pass_mark');
            $table->tinyInteger('type');
            $table->timestamps();


            $table->unique(['code', 'program_id']);
            $table->foreign('program_id')->references('id')->on('programs')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('type')->references('id')->on('subjects_types')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.default'))->dropIfExists('subjects');
    }
}
