<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectPrerequisitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.default'))->create('subject_prerequisites', function (Blueprint $table) {
            $table->unsignedInteger('subject_id');
            $table->unsignedInteger('prerequisite_subject_id');

            $table->primary(['subject_id', 'prerequisite_subject_id']);
            $table->foreign('subject_id')->references('id')->on(config('database.connections.main_db.database') . '.subjects')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('prerequisite_subject_id')->references('id')->on(config('database.connections.main_db.database') . '.subjects')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.default'))->dropIfExists('subject_prerequisites');
    }
}
