<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProgramBasedOnIdToProgramsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.default'))->table('programs', function (Blueprint $table) {
            $table->unsignedInteger('program_based_on_id')->nullable()->after('is_rewarded');

            $table->foreign('program_based_on_id')->references('id')->on('programs')->onDelete('set null')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.default'))->table('programs', function (Blueprint $table) {
            //
        });
    }
}
