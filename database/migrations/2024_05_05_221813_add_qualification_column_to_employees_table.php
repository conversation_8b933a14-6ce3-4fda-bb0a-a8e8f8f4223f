<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddQualificationColumnToEmployeesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.default'))->table('employees', function (Blueprint $table) {
            $table->unsignedInteger('qualification_id')->default(1)->after('job_level');

            $table->foreign('qualification_id')
                ->references('id')
                ->on('qualifications')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.default'))->table('employees', function (Blueprint $table) {
            $table->dropForeign(['qualification_id']);
            $table->dropColumn('qualification_id');
        });
    }
}
