<?php

namespace App\Exceptions;

use App\Models\Language;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\PostTooLargeException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param Throwable $exception
     * @return void
     *
     * @throws Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param Throwable $exception
     * @return Response
     *
     * @throws Throwable
     */
    public function render($request, Throwable $exception)
    {
//        if($request->wantsJson()) {
//            $exceptionRender = $this->handleApiExceptions($exception);
//        } else {
//            $exceptionRender = $this->handleWebExceptions($request, $exception);
//        }
//
//        if($exceptionRender) {
//            return $exceptionRender;
//        }

        return parent::render($request, $exception);
    }

    private function handleApiExceptions(Throwable $exception)
    {
        if ($exception->getCode() == 400) {
            abort(400, 'Bad Request Parameters');
        }

        if ($exception->getCode() == 403 || $exception instanceof AuthorizationException) {
            return response()->json([
                'message' => 'Unauthorized'], 403);
        }

        if ($exception->getCode() == 404 || $exception instanceof ModelNotFoundException) {
            return response()->json([
                'message' => 'Sorry, Request Not Found'], 404);
        }

        if ($exception->getCode() == 500) {
            return response()->json([
                'message' => 'Something went wrong please try again or call the manager'], 500);
        }

        if ($exception instanceof HttpException) {
            return response()->json([
                'message' => 'Something went wrong please try again or call the manager'], 500);
        }

        if ($exception instanceof PostTooLargeException) {
            return response()->json([
                'message' => 'File Size Is Too Large'], 413);
        }

        if ($exception instanceof FileNotFoundException) {
            return response()->json([
                'message' => 'File Not Found'], 500);
        }

        return null;
    }

    private function handleWebExceptions($request, Throwable $exception)
    {
        $languages = Language::get()->pluck('id')->toArray();

        in_array($request->segment(1), $languages) ?
            app()->setLocale($request->segment(1)) : app()->setLocale('ar');

        if ($exception->getCode() == 400) {
            abort(400, 'Bad Request Parameters');
        }

        if ($exception->getCode() == 404
            || $exception instanceof ModelNotFoundException
            || $exception instanceof NotFoundHttpException) {
            return response()->view('website::errors.404', [], 404);
        }

        if($exception instanceof  HttpException && $exception->getStatusCode() == 403) {
            return response()->view('website::errors.403', [], 403);
        }


        if ($exception instanceof HttpException) {
            abort(500, 'Something went wrong please try again or call the manager');
        }

        if ($exception instanceof PostTooLargeException) {
            abort(413, 'File Size Is Too Large');
        }

        if ($exception instanceof FileNotFoundException) {
            abort(500, 'File Not Found');
        }

//        if ($exception->getCode() == 500 || $exception->getCode() == 0) {
//            return response()->view('website::errors.500', [], 500);
//        }

        return null;
    }
}
