<?php


namespace App\Scopes;


use App\Helpers\EntitiesHelper;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class InCurrentEntityScope implements Scope
{

    public function apply(Builder $builder, Model $model)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $builder->where('entity_id', $currentEntityId);
    }
}
