<?php


namespace App\Scopes;


use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class InDescendingEntities implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $builder->whereIn('entity_id', $entitiesIds);
    }
}
