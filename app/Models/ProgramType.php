<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use <PERSON>yvoy\CrossDatabase\Eloquent\Model;

class ProgramType extends Model
{
    use HasTranslations;
    use Cachable;

    public const BACHELOR = 1;
    public const DOCTORATE = 2;
    public const DIPLOMA_AR = 'دبلوم الدراسات العليا';
    public const DIPLOMA_EN = 'Postgraduate Diploma';
    public $translatable = ['title'];
    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }
}
