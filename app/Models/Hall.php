<?php

namespace App\Models;

use App\Traits\HasTranslations;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class Hall extends Model implements Auditable
{
    use HasTranslations, \OwenIt\Auditing\Auditable;

    public $translatable = ['title'];
    protected $hidden = ['created_at', 'updated_at'];

    protected $casts = [
        'is_active' => 'boolean',
    ];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }
}
