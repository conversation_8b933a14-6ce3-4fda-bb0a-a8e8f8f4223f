<?php

namespace App\Models;

use App\Traits\HasTranslations;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class Building extends Model implements Auditable
{
    use HasTranslations, \OwenIt\Auditing\Auditable;

    public $translatable = ['title', 'description'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function halls()
    {
        return $this->hasMany(Hall::class);
    }
}
