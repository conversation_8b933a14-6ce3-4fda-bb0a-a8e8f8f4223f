<?php

namespace App\Models;

use App\Helpers\EntitiesHelper;
use Ben<PERSON>ber\Permission\Traits\HasPermissions;
use <PERSON>jaber\Permission\Traits\HasRoles;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Sms\Entities\Student;
use Modules\Website\Entities\Publication;
use Modules\Website\Entities\PublicationAuthor;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class User extends Model implements
    AuthenticatableContract,
    AuthorizableContract,
    MustVerifyEmail,
    HasMedia,
    Auditable
{
    use HasRoles {
        hasRole as hasRoleTrait;
    }
    use Authenticatable, Authorizable, \Illuminate\Auth\MustVerifyEmail, Notifiable, HasApiTokens, HasPermissions, InteractsWithMedia, \OwenIt\Auditing\Auditable;

    const STUDENT_TYPE = 1;
    const LECTURER_TYPE = 2;
    const EMPLOYEE_TYPE = 3;
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'created_at', 'updated_at', 'reset_password_token', 'email_verified_at', 'phone_verified_at'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_superadmin' => 'boolean'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function hasRole($roles, $entityId): bool
    {
        if ($this->is_superadmin) {
            return true;
        }
        return $this->hasRoleTrait($roles, $entityId);
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('avatar')
            ->useFallbackPath(public_path('/public/default-images/user.jpg'))
            ->useFallbackUrl(url('/default-images/user.jpg'))
            ->singleFile();
    }

    public function getTypeAttribute($type)
    {
        switch ($type) {
            case User::STUDENT_TYPE;
                return 'student';
            case User::LECTURER_TYPE:
                return 'lecturer';
            case User::EMPLOYEE_TYPE:
                return 'employee';
        }
    }

    public function setTypeAttribute($type)
    {
        switch (strtolower($type)) {
            case 'student':
                $this->attributes['type'] = User::STUDENT_TYPE;
                break;
            case 'lecturer':
                $this->attributes['type'] = User::LECTURER_TYPE;
                break;
            case 'employee':
                $this->attributes['type'] = User::EMPLOYEE_TYPE;
                break;
        }
    }

    public function getStatusAttribute($status)
    {
        switch ($status) {
            case 0:
                return 'inactive';
            case 1:
                return 'active';
            case 2:
                return 'suspended';
        }
    }

    public function setStatusAttribute($status)
    {
        switch (strtolower($status)) {
            case 'inactive':
                $this->attributes['status'] = 0;
                break;
            case 'active':
                $this->attributes['status'] = 1;
                break;
            case 'suspended':
                $this->attributes['status'] = 2;
                break;
        }
    }


    public function scopeWhereStatus($query, $status)
    {
        switch (strtolower($status)) {
            case 'inactive':
                $status = 0;
                break;
            case 'active':
                $status = 1;
                break;
            case 'suspended':
                $status = 2;
                break;
            default:
                $status = 0;
        }
        return $query->where('status', $status);
    }

    public function setGenderAttribute($gender)
    {
        switch (strtolower($gender)) {
            case 'male':
                $this->attributes['gender'] = 1;
                break;
            case 'female':
                $this->attributes['gender'] = 2;
                break;
        }
    }

    public function getGenderAttribute($type)
    {
        switch ($type) {
            case 1:
                return 'male';
            case 2:
                return 'female';
        }
    }

    public function getShortNameAttribute()
    {
        $lang = app()->getLocale();
        $firstName = 'first_name_' . $lang;
        $lastName = 'last_name_' . $lang;
        return $this->$firstName . ' ' . $this->$lastName;
    }

    public function getFullNameAr()
    {
        return $this->first_name_ar . ' ' . $this->second_name_ar . ' ' . $this->third_name_ar . ' ' . $this->last_name_ar;
    }

    public function getFullNameEn()
    {
        return $this->first_name_en . ' ' . $this->second_name_en . ' ' . $this->third_name_en . ' ' . $this->last_name_en;
    }

    public function getFullNameAttribute()
    {
        $lang = app()->getLocale();
        $firstName = 'first_name_' . $lang;
        $secondName = 'second_name_' . $lang;
        $thirdName = 'third_name_' . $lang;
        $lastName = 'last_name_' . $lang;
        if (!$this->$firstName || !$this->$secondName || !$this->$thirdName || !$this->$lastName) {
            $firstName = 'first_name_ar';
            $secondName = 'second_name_ar';
            $thirdName = 'third_name_ar';
            $lastName = 'last_name_ar';
        }
        return $this->$firstName . ' ' . $this->$secondName . ' ' . $this->$thirdName . ' ' . $this->$lastName;
    }

    public function getFirstNameAttribute()
    {
        $lang = app()->getLocale();
        $firstName = 'first_name_' . $lang;
        return $this->$firstName;
    }

    public function isVerified()
    {
        return $this->email_verified_at !== null;
    }

    public function isActive()
    {
        return $this->status == 'active';
    }


    public function isInactive()
    {
        return $this->status == 'inactive';
    }

    public function isSuspended()
    {
        return $this->status == 'suspended';
    }

    public function isType($type)
    {
        return $this->type == $type;
    }

    public function student()
    {
        return $this->hasOne(Student::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function lecturer()
    {
        return $this->hasOne(Lecturer::class);
    }

    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    public function currentEntityPermissions()
    {
        $entityId = EntitiesHelper::getEntityId();
        return $this->permissions()->wherePivot('entity_id', $entityId)->withPivot(['entity_id']);
    }

    public function currentEntityRoles()
    {
        $entityId = EntitiesHelper::getEntityId();
        return $this->roles()->wherePivot('entity_id', $entityId)->withPivot(['entity_id']);
    }

    public function panels($entity)
    {
        if ($this->is_superadmin) {
            return Permission::whereNotNull('panel_name')
                ->select('permissions.panel_name')
                ->groupBy('permissions.panel_name');
        }
        return $this->permissions()
            ->whereNotNull('panel_name')
            ->wherePivot('entity_id', $entity)
            ->select('permissions.panel_name')
            ->groupBy('permissions.panel_name');
    }

    public function smsPanels($entity)
    {
        if ($this->is_superadmin) {
            return Role::whereNotNull('panel_name')
                ->select('roles.panel_name')
                ->groupBy('roles.panel_name');
        }
        return $this->roles()
            ->whereNotNull('panel_name')
            ->wherePivot('entity_id', $entity)
            ->select('roles.panel_name')
            ->groupBy('roles.panel_name');
    }

    public function nationality()
    {
        return $this->belongsTo(Nationality::class);
    }

    public function publications()
    {
        return $this->belongsToMany(Publication::class, PublicationAuthor::class, 'author_id', 'publication_id')
            ->withPivot('author_sequence', 'author_name');
    }

    public function socialLinks()
    {
        return $this->belongsToMany(Link::class, UserSocialLink::class)->withPivot('link');
    }

    public function avatar()
    {
        return $this->morphOne(Media::class, 'model')->where('collection_name', '=', 'avatar');
    }
}
