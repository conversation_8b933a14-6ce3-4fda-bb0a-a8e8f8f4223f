<?php

namespace App\Models;

use App\Scopes\InDescendingEntities;
use App\Traits\HasEmptyTranslations;
use App\Traits\HasTranslations;
use App\Traits\Searchable;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;


class Program extends Model implements HasMedia, Auditable
{
    use SoftDeletes, HasTranslations, Searchable, InteractsWithMedia, \OwenIt\Auditing\Auditable;

    public $translatable = ['title', 'major', 'objectives', 'outcomes', 'description', 'entry_requirements', 'certificate_awarded', 'job_market'];
    public $searchableFields = ['title', 'major', 'objectives', 'outcomes', 'description', 'entry_requirements', 'certificate_awarded', 'job_market'];

    protected $hidden = ['created_at', 'updated_at'];
    protected $appends = ['url'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new InDescendingEntities());
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('image')
            ->useFallbackPath(public_path('/public/default-images/program.jpg'))
            ->useFallbackUrl(url('/default-images/program.jpg'))
            ->singleFile();

        $this
            ->addMediaCollection('booklet')
            ->singleFile();
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'model')->where('collection_name', '=', 'image');
    }

    public function booklet()
    {
        return $this->morphOne(Media::class, 'model')->where('collection_name', '=', 'booklet');
    }

    public function getUrlAttribute()
    {
        return route('programs.show', ['lang' => app()->getLocale(), 'program' => $this], false);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function scopeWherePlan($query, $termPlan)
    {
        if ($termPlan == Term::SEMESTER_PLAN) {
            return $query->where('duration_unit', ProgramDurationUnit::SEMESTER);
        }

        return $query->where('duration_unit', ProgramDurationUnit::YEAR);
    }

    public function programType()
    {
        return $this->belongsTo(ProgramType::class, 'type', 'id');
    }

    public function durationUnit()
    {
        return $this->belongsTo(ProgramDurationUnit::class, 'duration_unit', 'id');
    }

    public function language()
    {
        return $this->belongsTo(Language::class, 'lang', 'id');
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class);
    }

    public function basedOn()
    {
        return $this->belongsToMany(
            Subject::class,
            ProgramBasedOn::class,
            'program_id',
            'subject_id'
        )
            ->withPivot('code');
    }

    public function students()
    {
        return $this->hasMany(Student::class);
    }

    public function scopeWhereDurationUnit($query, $termPlan)
    {
        if ($termPlan == Term::SEMESTER_PLAN) {
            return $query->where('duration_unit', ProgramDurationUnit::SEMESTER);
        }

        return $query->where('duration_unit', ProgramDurationUnit::YEAR);
    }
}
