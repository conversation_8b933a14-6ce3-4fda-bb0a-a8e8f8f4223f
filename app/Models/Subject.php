<?php

namespace App\Models;

use App\Traits\HasTranslations;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class Subject extends Model implements Auditable
{
    use HasTranslations, \OwenIt\Auditing\Auditable;

    const GENERAL = 1;
    const COMPULSORY = 2;
    const ELECTIVE = 3;
    const SUPPORTIVE = 4;

    public $translatable = ['title', 'description'];

    protected $casts = [
        'is_active' => 'boolean'
    ];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function subjectType()
    {
        return $this->belongsTo(SubjectType::class, 'type');
    }

    public function prerequisites()
    {
        return $this->belongsToMany(Subject::class, SubjectPrerequisite::class, 'subject_id', 'prerequisite_subject_id', 'id');
    }
}
