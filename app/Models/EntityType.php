<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;


class EntityType extends Model
{
    use HasTranslations, Cachable;

    public const UNIVERSITY = 1;
    public const SCHOOL = 2;
    public const DEPARTMENT = 3;
    public const MANAGEMENT = 4;
    public const STUDENT = 5;
    public const JOURNAL = 6;

    public $translatable = ['title'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }
}
