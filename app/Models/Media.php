<?php

namespace App\Models;


use App\Classes\HtmlableMedia;

class Media extends \Spatie\MediaLibrary\MediaCollections\Models\Media
{

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function img(string $conversionName = '', $extraAttributes = []): HtmlableMedia
    {
        return (new HtmlableMedia($this))
            ->conversion($conversionName)
            ->attributes($extraAttributes);
    }
}
