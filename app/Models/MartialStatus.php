<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Hoyvoy\CrossDatabase\Eloquent\Model;


class MartialStatus extends Model
{
    use HasTranslations, Cachable;
    public $translatable = ['title'];
    protected $hidden = ['created_at', 'updated_at'];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }
}
