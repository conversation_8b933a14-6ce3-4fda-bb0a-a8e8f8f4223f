<?php

namespace App\Models;

use App\Helpers\Utils;
use App\Traits\EntityHasRecursiveRelation;
use App\Traits\HasTranslations;
use Benjaber\Permission\Models\Entity as EntityBase;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\Sms\Entities\Term;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Entity extends EntityBase implements HasMedia, Auditable
{
    use SoftDeletes, HasTranslations, EntityHasRecursiveRelation, Cachable, InteractsWithMedia, \OwenIt\Auditing\Auditable;

    public $className = Entity::class;
    public $translatable = ['title'];
    protected $hidden = ['created_at', 'updated_at'];

    public $incrementing = false;
    protected $appends = ['url'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }


    public function getParentKeyName()
    {
        return 'parent_entity_id';
    }

    public function getLocalKeyName()
    {
        return 'id';
    }

    public function getUrlAttribute()
    {
        $url = 'https://';
        $appUrl = Utils::removeHttp(config('app.url'));
        if (Str::startsWith($appUrl, $this->id)) {
            $url .= $appUrl;
        } else {
            $url .= $this->id . '.' . $appUrl;
        }
        return $url;
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('cover')
            ->singleFile();
    }

    public function entityCover()
    {
        return $this->morphOne(Media::class, 'model')->where('collection_name', '=', 'cover');
    }

    public function parent()
    {
        return $this->belongsTo(Entity::class, 'parent_entity_id');
    }

    public function directChildren()
    {
        return $this->hasMany(Entity::class, 'parent_entity_id');
    }

    public function isUniversity()
    {
        return $this->getAttribute('type') == EntityType::UNIVERSITY;
    }

    public function scopeSchools($query)
    {
        return $query->where('type', '=', EntityType::SCHOOL);
    }

    public function isSchool()
    {
        return $this->getAttribute('type') == EntityType::SCHOOL;
    }

    public function isStudentsPage()
    {
        return $this->getAttribute('type') == EntityType::STUDENT;
    }

    public function scopeDepartments($query)
    {
        return $query->where('type', EntityType::DEPARTMENT);
    }

    public function isDepartment()
    {
        return $this->getAttribute('type') == EntityType::DEPARTMENT;
    }

    public function isManagement()
    {
        return $this->getAttribute('type') == EntityType::MANAGEMENT;
    }

    public function entityType()
    {
        return $this->belongsTo(EntityType::class, 'type');
    }

    public function details()
    {
        return $this->hasOne(EntityDetails::class);
    }

    public function programs()
    {
        return $this->hasMany(Program::class);
    }

    public function buildings()
    {
        return $this->belongsToMany(Building::class, EntityBuildings::class);
    }

    public function head()
    {
        return $this->belongsTo(User::class, 'entity_head');
    }

    public function studyAndExams()
    {
        return $this->belongsTo(User::class, 'study_and_exams');
    }

    public function links()
    {
        return $this->belongsToMany(Link::class, EntityLink::class)->withPivot('link');
    }

    public function terms()
    {
        if ($this->isDepartment()) {
            return $this->hasMany(Term::class, 'entity_id', 'parent_entity_id');
        }
        return $this->hasMany(Term::class);
    }
}
