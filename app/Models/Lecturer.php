<?php

namespace App\Models;

use App\Traits\HasTranslations;
use <PERSON>yvoy\CrossDatabase\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Modules\Sms\Entities\TimetableContent;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;


class Lecturer extends Model implements HasMedia
{
    use HasTranslations, InteractsWithMedia;

    const COOPERATIVE_TYPE = 'cooperative';
    const PERMANENT_TYPE = 'permanent';

    public $translatable = ['bio', 'job_title'];
    protected $hidden = ['created_at', 'updated_at'];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function scopeSearch($query, $search)
    {
        return $query->whereHas('user', function ($query) use ($search) {
            return $query->where(DB::raw('concat(first_name_ar," ",second_name_ar, " ", third_name_ar, " ", last_name_ar)'), 'LIKE', "%$search%")
                ->orWhere(DB::raw('concat(first_name_en," ",second_name_en, " ", third_name_en, " ", last_name_en)'), 'LIKE', "%$search%");
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function firstQualification()
    {
        return $this->belongsTo(Qualification::class, 'qualification_id_2');
    }

    public function qualification()
    {
        return $this->belongsTo(Qualification::class);
    }

    public function academicRank()
    {
        return $this->belongsTo(AcademicRank::class);
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('cv')
            ->singleFile();
    }

    public function resume()
    {
        return $this->morphOne(Media::class, 'model')->where('collection_name', '=', 'cv');
    }

    public function timetable_contents()
    {
        return $this->hasMany(TimetableContent::class);
    }
}
