<?php

namespace App\Models;

use App\Traits\HasTranslations;
use <PERSON><PERSON><PERSON>\Permission\Contracts\Permission as PermissionContract;
use Benjaber\Permission\Exceptions\PermissionDoesNotExist;
use Benjaber\Permission\Models\Permission as BasePermission;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

/**
 * App\Permission
 *
 * @property int $id
 * @property array $name
 * @property string|null $panel_name
 * @property string|null $level
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read array $translations
 * @method static \Illuminate\Database\Eloquent\Builder|Permission disableCache()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission query()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission withCacheCooldownSeconds($seconds = null)
 * @mixin \Eloquent
 */
class Permission extends BasePermission
{
    use HasTranslations, Cachable;

    public $translatable = ['name'];

    protected $hidden = ['created_at', 'updated_at'];

    protected $casts = [
        'name' => 'array'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    /**
     * Find a permission by its name (and optionally guardName).
     *
     * @param string $name
     *
     * @throws \Benjaber\Permission\Exceptions\PermissionDoesNotExist
     *
     * @return \Benjaber\Permission\Contracts\Permission
     */
    public static function findByName(string $name): PermissionContract
    {
        $permission = static::where('name->en', $name)->first();
        if (! $permission) {
            throw PermissionDoesNotExist::create($name);
        }

        return $permission;
    }

    /**
     * Find a permission by its id (and optionally guardName).
     *
     * @param int $id
     * @param string|null $guardName
     *
     * @throws \Benjaber\Permission\Exceptions\PermissionDoesNotExist
     *
     * @return \Benjaber\Permission\Contracts\Permission
     */
    public static function findById(int $id): PermissionContract
    {
        $permission = static::where('id', $id)->first();

        if (! $permission) {
            throw PermissionDoesNotExist::withId($id);
        }

        return $permission;
    }


}
