<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;


class AcademicRank extends Model
{
    use HasTranslations, Cachable;

    const PROFESSOR = 6;

    public $translatable = ['title'];
    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

}
