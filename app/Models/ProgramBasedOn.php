<?php

namespace App\Models;

use Hoyvoy\CrossDatabase\Eloquent\Model;


class ProgramBasedOn extends Model
{

    public $incrementing = false;
    protected $table = 'program_based_on';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

    public function getRouteKeyName()
    {
        return 'code';
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

}
