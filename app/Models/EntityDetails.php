<?php

namespace App\Models;

use App\Traits\HasTranslations;
use Hoyvoy\CrossDatabase\Eloquent\Model;


class EntityDetails extends Model
{
    use HasTranslations;

    const Scientific = 'scientific';
    const Literary = 'literary';

    public $translatable = ['vision', 'message', 'goals', 'description', 'head_word'];

    public $incrementing = false;
    protected $primaryKey = 'entity_id';

    protected $casts = [
        'departments' => 'array',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }

}
