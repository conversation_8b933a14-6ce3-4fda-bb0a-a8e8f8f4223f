<?php


namespace App\Models;


use App\Traits\HasTranslations;
use <PERSON><PERSON>ber\Permission\Contracts\Role as RoleContract;
use <PERSON>jaber\Permission\Exceptions\RoleDoesNotExist;
use Benjaber\Permission\Models\Role as BaseRole;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

/**
 * App\Role
 *
 * @property int $id
 * @property array $name
 * @property string|null $panel_name
 * @property string|null $level
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read array $translations
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Permission[] $permissions
 * @method static \Illuminate\Database\Eloquent\Builder|Role disableCache()
 * @method static \Illuminate\Database\Eloquent\Builder|Role newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Role newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Role query()
 * @method static \Illuminate\Database\Eloquent\Builder|Role withCacheCooldownSeconds($seconds = null)
 * @mixin \Eloquent
 */
class Role extends BaseRole
{
    use HasTranslations, Cachable;

    public $translatable = ['name'];

    protected $casts = [
        'name' => 'array'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.default'));
    }


    /**
     * Find a role by its name and guard name.
     *
     * @param string $name
     * @param string|null $guardName
     *
     * @return \Benjaber\Permission\Contracts\Role|\Benjaber\Permission\Models\Role
     *
     * @throws \Benjaber\Permission\Exceptions\RoleDoesNotExist
     */
    public static function findByName(string $name): RoleContract
    {

        $role = static::where('name->en', $name)->first();

        if (! $role) {
            throw RoleDoesNotExist::named($name);
        }

        return $role;
    }

    public static function findById(int $id): RoleContract
    {

        $role = static::where('id', $id)->first();

        if (! $role) {
            throw RoleDoesNotExist::withId($id);
        }

        return $role;
    }
}
