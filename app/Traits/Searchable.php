<?php


namespace App\Traits;


use Illuminate\Support\Facades\DB;

trait Searchable
{

    public function scopeSearch($query, $search)
    {
        $i = 0;
        foreach ($this->searchableFields as $field) {
            if($i++ == 0) {
                $query->where(DB::raw('lower(' . $field. ')'), "LIKE", "%".mb_strtolower($search)."%");
                continue;
            }
            $query->orWhere(DB::raw('lower(' . $field. ')'), "LIKE", "%".mb_strtolower($search)."%");
        }

        return $query;
    }


    public function scopeSort($query, $field, $type = 'asc')
    {
        if(in_array($field, $this->getTranslatableAttributes())) {
            $query->orderBy("$field->".app()->getLocale(), $type)->orderBy('id', $type);
        } else {
            $query->orderBy($field, $type)->orderBy('id', $type);
        }
    }


}
