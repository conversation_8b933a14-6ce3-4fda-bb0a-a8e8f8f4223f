<?php


namespace App\Traits;


trait HasLang
{
    public function hasLang($lang)
    {
        foreach ($this->translatable as $attribute) {
            if ($this->getTranslation($attribute, $lang, false) == '' || $this->getTranslation($attribute, $lang, false) == null) {
                return false;
            }
        }
        return true;
    }

    public function scopeLang($query, $lang)
    {
        //if request is coming from api we need to return both languages
        if( request()->expectsJson() ) {
            return $query;
        }

        foreach ($this->translatable as $field) {
            $query->where($field.'->' . $lang, '!=', '')
                ->whereNotNull($field.'->' . $lang);
        }

        return $query;
    }
}
