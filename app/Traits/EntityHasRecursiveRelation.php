<?php


namespace App\Traits;


trait EntityHasRecursiveRelation
{
    public function getParentKeyName()
    {
        return 'parent_id';
    }

    public function getLocalKeyName()
    {
        return 'id';
    }

    public function children($withRelation = null)
    {
        if ($withRelation) {
            $allEntities = $this->className::with($withRelation)->get();
        } else {
            $allEntities = app('allEntities');
        }
        $localKeyName = $this->getLocalKeyName();
        return $this->parseTree($allEntities, $this->$localKeyName);
    }

    public function childrenAndSelf($withRelation = null)
    {
        if ($withRelation) {
            $allEntities = $this->className::with($withRelation)->get();
        } else {
            $allEntities = app('allEntities');
        }
        $localKeyName = $this->getLocalKeyName();
        $tree = $this->parseTree($allEntities, $this->$localKeyName);
        $withSelfTree = $allEntities->where($this->getLocalKeyName(), $this->$localKeyName)->first()->toArray();
        $withSelfTree['children'] = $tree;
        return $withSelfTree;
    }

    public function parents()
    {
        $allEntities = app('allEntities');
        $parentKeyName = $this->getParentKeyName();
        return $this->parseTreeForParents($allEntities, $this->$parentKeyName);
    }

    public function parseTreeForParents($items, $parentId = null)
    {
        $ids = array();
        for ($i = 0; $i < count($items); $i++) {
            foreach ($items as $item) {
                if ($item[$this->getLocalKeyName()] == $parentId) {
                    $ids[] = $item[$this->getLocalKeyName()];
                    $parentId = $item[$this->getParentKeyName()];
                }
            }

        }

        return empty($ids) ? null : $ids;
    }

    public function parseTree($items, $parentId = null)
    {
        $return = array();
        foreach ($items as $child) {
            # A direct child is found
            if ($child[$this->getParentKeyName()] == $parentId) {
                # Append the child into result array and parse its children
                $entity = $child->toArray();
                $children = $this->parseTree($items, $child[$this->getLocalKeyName()]);
                if ($children) {
                    $entity['children'] = $children;
                }
                $return[] = $entity;
            }
        }

        return empty($return) ? null : $return;
    }

    public function getIds($items)
    {
        if (!is_array($items)) {
            $items = $items->toArray();
        }
        $ids = [];
        $recursive = function ($item) use (&$recursive, &$ids) {
            $ids[] = $item['id'];
            if (isset($item['children'])) {
                foreach ($item['children'] as $child) {
                    $recursive($child);
                }
            }
        };

        $recursive($items);

        return $ids;
    }

}
