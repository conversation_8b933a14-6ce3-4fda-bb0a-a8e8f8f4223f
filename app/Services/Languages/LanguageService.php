<?php


namespace App\Services\Languages;


use App\Repositories\Languages\LanguageRepositoryInterface;

class LanguageService
{
    /**
     * @var LanguageRepositoryInterface
     */
    private $languageRepository;

    /**
     * LanguageService constructor.
     * @param LanguageRepositoryInterface $languageRepository
     */
    public function __construct(LanguageRepositoryInterface $languageRepository)
    {
        $this->languageRepository = $languageRepository;
    }

    public function getAll()
    {
        return $this->languageRepository->getAll();
    }
}
