<?php


namespace App\Services\Entities;


use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use App\Helpers\Utils;
use App\Http\Requests\Entity\StoreEntityRequest;
use App\Http\Requests\Entity\UpdateEntityRequest;
use App\Repositories\Entities\EntityRepositoryInterface;

class EntityService
{
    /**
     * @var EntityRepositoryInterface
     */
    private $entityRepository;

    /**
     * EntityService constructor.
     * @param EntityRepositoryInterface $entityRepository
     */
    public function __construct(EntityRepositoryInterface $entityRepository)
    {
        $this->entityRepository = $entityRepository;
    }

    public function getAll()
    {
        return $this->entityRepository->getAll();
    }

    public function create(StoreEntityRequest $request)
    {
        $validatedData = collect($request->validated());
        $currentEntityId = EntitiesHelper::getEntityId();

        $entityData = $validatedData->except(['vision', 'description', 'goals', 'message', 'buildings', 'head_word', 'links', 'entity_cover', 'departments']);
        $entityData->put('parent_entity_id', $currentEntityId);
        $entityData = $entityData->toArray();

        $entityDetailsData = $validatedData->only(['vision', 'description', 'goals', 'message', 'head_word', 'departments'])->toArray();
        $entityBuildingsIds =  $validatedData->only('buildings')->toArray()['buildings'];
        $socialLinks = $validatedData->only('links');
        $links = [];
        collect($socialLinks['links'])->each(function($link, $key) use (&$links) {
            if(! is_null($link)) {
                $links[$key] = ['link' => $link];
            }
        })->toArray();
        $entity = $this->entityRepository->create($entityData, $entityDetailsData, $entityBuildingsIds, $links);
        //upload cover image or default image
        StorageHelper::upload($entity, $request->file('entity_cover'), 'cover');

        if(!app()->environment('testing')) {
            Utils::addDomain($entityData['id']);
        }
        return ['message' => 'success', 'code' => 200, 'id' => $entity->id];
    }

    public function show($id)
    {
        return $this->entityRepository->show($id);
    }

    public function update(UpdateEntityRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $entityData = $validatedData->except(['vision', 'description', 'goals', 'message', 'goals', 'buildings', 'head_word', 'links', 'entity_cover', 'departments'])->toArray();
        $entityDetailsData = $validatedData->only(['vision', 'description', 'goals', 'message', 'goals', 'head_word', 'departments'])->toArray();
        $entityBuildingsIds =  $validatedData->only('buildings')->toArray()['buildings'];
        $socialLinks = $validatedData->only('links');
        $links = [];
        collect($socialLinks['links'])->each(function($link, $key) use (&$links) {
            if(! is_null($link)) {
                $links[$key] = ['link' => $link];
            }
        })->toArray();
        $entity = $this->entityRepository->update($entityData, $entityDetailsData, $entityBuildingsIds, $links, $id);
        if ($validatedData->has('entity_cover')) {
            StorageHelper::upload($entity, $request->file('entity_cover'), 'cover');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->entityRepository->delete($id);
        if(!app()->environment('testing')) {
            Utils::removeDomain($id);
//            dispatch(function() {
//                Utils::fixDomains();
//            });
        }
        return ['message' => 'success', 'code' => 200];
    }
}
