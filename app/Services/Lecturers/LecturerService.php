<?php


namespace App\Services\Lecturers;


use App\Helpers\StorageHelper;
use App\Http\Requests\Lecturer\StoreLecturerRequest;
use App\Http\Requests\Lecturer\UpdateLecturerDetailsRequest;
use App\Http\Requests\Lecturer\UpdateLecturerRequest;
use App\Models\Lecturer;
use App\Notifications\EmailVerificationNotification;
use App\Repositories\Lecturers\LecturerRepositoryInterface;
use App\Repositories\Users\UserRepositoryInterface;
use App\Services\PrivilegesService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Throwable;

class LecturerService
{

    /**
     * @var LecturerRepositoryInterface
     */
    private $lecturerRepository;
    /**
     * @var UserRepositoryInterface
     */
    private $userRepository;

    /**
     * LecturerService constructor.
     * @param LecturerRepositoryInterface $lecturerRepository
     * @param UserRepositoryInterface $userRepository
     */
    public function __construct(LecturerRepositoryInterface $lecturerRepository, UserRepositoryInterface $userRepository)
    {
        $this->lecturerRepository = $lecturerRepository;
        $this->userRepository = $userRepository;
    }

    public function getAll($allEntities = false, $status = null)
    {
        return $this->lecturerRepository->getAll($allEntities, $status);
    }

    public function getPagination($elementsPerPage, $search, $allEntities = false, $onlyPermanent = false)
    {
        return $this->lecturerRepository->getPagination($elementsPerPage, $search, $allEntities, $onlyPermanent);
    }

    public function getRandom($numberOfElements)
    {
        return $this->lecturerRepository->getRandom($numberOfElements);
    }

    public function create(StoreLecturerRequest $request, $status = 'inactive')
    {

        $validatedData = collect($request->validated());
        $validatedData->put('status', $status);
        $validatedData->put('password', Hash::make($validatedData->get('password')));

        $lecturerOnlyFields = [
            'qualification_id', 'qualification_university', 'qualification_year', 'general_major', 'specialization',
            'bank_name', 'bank_account_number', 'university_number',
            'academic_rank_id', 'start_date', 'personal_website', 'type'
        ];

        $userData = $validatedData->except($lecturerOnlyFields)->toArray();
        $userData['type'] = 'lecturer';
        $lecturerData = $validatedData->only($lecturerOnlyFields)->toArray();

        $response = [];

        try {
            DB::connection(config('database.default'))->transaction(function () use ($request, $userData, $lecturerData, &$response) {
                $user = $this->userRepository->create($userData);
                $lecturer = $this->lecturerRepository->create($user, $lecturerData);
                $user->notify(new EmailVerificationNotification());
                $response = ['message' => 'success', 'code' => 200, 'id' => $lecturer->id];
            }, 5);
        } catch (Throwable $e) {
            Log::error($e->getMessage());
            abort(500, 'failed');
        }

        return $response;
    }

    public function show($id)
    {
        return $this->lecturerRepository->show($id);
    }


    public function delete($id)
    {
        $this->lecturerRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

    public function changeStatus($lecturerId, $status)
    {
        $user = Lecturer::find($lecturerId)->user;
        $this->userRepository->changeStatus($user->id, $status);
        return ['message' => 'success', 'code' => 200];
    }

    public function updateDetails($lecturerId, UpdateLecturerDetailsRequest $request)
    {
        $validatedData = collect($request->validated());
        $lecturerDetailsData = $validatedData->except(['cv', 'removed_cv'])->toArray();
        $lecturer = $this->lecturerRepository->update($lecturerId, $lecturerDetailsData);
        if ($request->has('removed_cv')) {
            StorageHelper::deleteMedia($lecturer, $request->removed_cv);
        }
        if ($request->has('cv') && is_array($request->cv) && $request->cv[0]) {
            StorageHelper::upload($lecturer, $request->cv[0], 'cv');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function update(UpdateLecturerRequest $request, $id)
    {
        $lecturer = Lecturer::with('user')->find($id);
        $validatedData = collect($request->validated());
        $lecturerOnlyFields = [
            'qualification_id', 'qualification_university', 'qualification_year', 'general_major', 'specialization',
            'bank_name', 'bank_account_number', 'university_number',
            'academic_rank_id', 'start_date', 'personal_website', 'type'
        ];

        $userData = $validatedData->except($lecturerOnlyFields)->toArray();
        $lecturerData = $validatedData->only($lecturerOnlyFields)->toArray();
        if ($request->has('password')) {
            $userData['password'] = Hash::make($request->get('password'));
            $userData['email_verified_at'] = now();
        }
        $this->userRepository->update($lecturer->user->id, $userData);
        $this->lecturerRepository->update($lecturer->id, $lecturerData);
        return ['message' => 'success', 'code' => 200];
    }

    public function getPublicList()
    {
        return $this->lecturerRepository->getPublicList();
    }
}
