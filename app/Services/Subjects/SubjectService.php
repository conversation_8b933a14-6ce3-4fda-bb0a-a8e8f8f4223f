<?php


namespace App\Services\Subjects;


use App\Http\Requests\ProgramBasedOn\StoreProgramBasedOnSubjectRequest;
use App\Http\Requests\ProgramBasedOn\UpdateProgramBasedOnSubjectRequest;
use App\Http\Requests\Subject\StoreSubjectRequest;
use App\Http\Requests\Subject\UpdateSubjectRequest;
use App\Repositories\Subjects\SubjectRepositoryInterface;

class SubjectService
{

    /**
     * @var SubjectRepositoryInterface
     */
    private $subjectRepository;

    /**
     * ProgramService constructor.
     * @param SubjectRepositoryInterface $subjectRepository
     */
    public function __construct(SubjectRepositoryInterface $subjectRepository)
    {
        $this->subjectRepository = $subjectRepository;
    }

    public function show($id)
    {
        return $this->subjectRepository->show($id);
    }

    public function subjectsOfProgram($id, $isActive = false)
    {
        return $this->subjectRepository->subjectsOfProgram($id, $isActive);
    }

    public function create(StoreSubjectRequest $request)
    {
        $validatedData = collect($request->validated());
        $subjectData = $validatedData->except('prerequisites')->toArray();
        $prerequisitesData = $validatedData->only('prerequisites')->flatten(1)->toArray();
        $subject = $this->subjectRepository->create($subjectData);
        $this->subjectRepository->savePrerequisites($subject->id, $prerequisitesData);
        return ['message' => 'success', 'code' => 200, 'slug' => $subject->slug];
    }

    public function getBasedOnSubjectsOfProgram($id)
    {
        $programBasedOn = $this->subjectRepository->getBasedOnSubjectsOfProgram($id);
        $programBasedOn->each(function($subject) {
            $subject->code = $subject->pivot->code;
        });
        return $programBasedOn;
    }

    public function addBasedOnSubject($programId, StoreProgramBasedOnSubjectRequest $request)
    {
        $basedOnValidatedData = $request->validated();
        $basedOnValidatedData['program_id'] = $programId;
        $basedOnSubject = $this->subjectRepository->addBasedOnSubject($basedOnValidatedData);
        return ['message' => 'success', 'code' => 200, 'based_on_subject' => $basedOnSubject];
    }

    public function update(UpdateSubjectRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $subjectData = $validatedData->except('prerequisites')->toArray();
        $prerequisitesData = $validatedData->only('prerequisites')->flatten(1)->toArray();
        $this->subjectRepository->update($subjectData, $id);
        $this->subjectRepository->savePrerequisites($id, $prerequisitesData);
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->subjectRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

    public function showBasedOnSubject($programId, $programBasedOnCode)
    {
        return $this->subjectRepository->showBasedOnSubject($programId, $programBasedOnCode);
    }

    public function updateBasedOnSubjectCode($programId, $code, UpdateProgramBasedOnSubjectRequest $request)
    {
        $this->subjectRepository->updateBasedOnSubjectCode($programId, $code, $request->validated());
        return ['message' => 'success', 'code' => 200, 'subject_code' => $code];
    }

    public function deleteBasedOnSubjectCode($programId, $programBasedOnCode)
    {
        $this->subjectRepository->deleteBasedOnSubjectCode($programId, $programBasedOnCode);
        return ['message' => 'success', 'code' => 200];
    }

}
