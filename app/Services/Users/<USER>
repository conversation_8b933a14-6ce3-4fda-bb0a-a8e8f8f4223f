<?php


namespace App\Services\Users;


use App\Helpers\StorageHelper;
use App\Http\Requests\User\UpdateUserAvatarRequest;
use App\Http\Requests\User\UpdateUserSocialLinksRequest;
use App\Repositories\Users\UserRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class UserService
{
    /**
     * @var UserRepositoryInterface
     */
    private UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getSocialLinks($id)
    {
        return $this->userRepository->getSocialLinks($id);
    }

    public function changePassword($id, $password)
    {
        $this->userRepository->update($id, ['password' => Hash::make($password)]);
        return ['message' => 'success', 'code' => 200];
    }

    public function updateSocialLinks($id, UpdateUserSocialLinksRequest $request)
    {
        $socialLinks = $request->only('links');
        $links = [];
        collect($socialLinks['links'])->each(function($link, $key) use (&$links) {
            if(! is_null($link)) {
                $links[$key] = ['link' => $link];
            }
        })->toArray();
        $this->userRepository->updateSocialLinks($id, $links);
        return ['message' => 'success', 'code' => 200];
    }

    public function updateAvatar($id, UpdateUserAvatarRequest $request)
    {
        $user = $this->userRepository->show($id);
        $image = StorageHelper::upload($user, $request->file('image'), 'avatar');
        return ['message' => 'success', 'image' => $image->getFullUrl(),'code' => 200];
    }
}
