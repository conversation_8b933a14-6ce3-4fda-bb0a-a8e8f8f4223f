<?php


namespace App\Services\UserRoles;


use App\Http\Requests\UserRoles\UpdateUserRolesRequest;
use App\Models\User;
use App\Repositories\UserRoles\UserRoleRepositoryInterface;

class UserRoleService
{
    /**
     * @var UserRoleRepositoryInterface
     */
    private $userRoleRepository;

    public function __construct(UserRoleRepositoryInterface $userRoleRepository)
    {
        $this->userRoleRepository = $userRoleRepository;
    }

    public function getRoles(User $user)
    {
        $userRoles = $this->userRoleRepository->getRoles($user);
        return $userRoles->map(fn($role) => [
            'id' => $role->id,
            'entity_id' => $role->pivot->entity_id
        ]);
    }

    public function updateRoles(User $user, UpdateUserRolesRequest $request)
    {
        $this->userRoleRepository->updateUserRoles($user->id, $request->roles_ids, $request->entity_id);

        return ['message' => 'success', 'code' => 200];
    }
}
