<?php


namespace App\Services\Programs;


use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use App\Http\Requests\Program\StoreProgramRequest;
use App\Http\Requests\Program\UpdateProgramRequest;
use App\Repositories\Programs\ProgramRepositoryInterface;
use DB;
use Modules\Website\DTOs\ProgramData;
use Throwable;

class ProgramService
{
    /**
     * @var ProgramRepositoryInterface
     */
    private $programRepository;

    /**
     * ProgramService constructor.
     * @param ProgramRepositoryInterface $programRepository
     */
    public function __construct(ProgramRepositoryInterface $programRepository)
    {
        $this->programRepository = $programRepository;
    }

    public function getAll($type = null, $activeOnly = false)
    {
        return $this->programRepository->getAll($type, $activeOnly);
    }

    public function getPagination(ProgramData $programData)
    {
        return $this->programRepository->getPagination($programData);
    }

    public function getRandom($numberOfElements)
    {
        return $this->programRepository->getRandom($numberOfElements);
    }

    public function show($id)
    {
        return $this->programRepository->show($id);
    }

    public function create(StoreProgramRequest $request)
    {
        $validatedData = collect($request->validated());

        $programData = $validatedData->except('image', 'booklet')->toArray();

        try {
            DB::connection('website_db')->transaction(function () use ($validatedData, $request, $programData) {
                $program = $this->programRepository->create($programData);
                StorageHelper::upload($program, $request->file('image'), 'image');
                if ($validatedData->has('booklet')) {
                    StorageHelper::upload($program, $request->booklet[0], 'booklet');
                }
            }, 5);
        } catch (Throwable $e) {
            abort(500, 'failed');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function update(UpdateProgramRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $programData = $validatedData->except('image', 'booklet', 'removed_booklet')->toArray();

        try {
            DB::connection('website_db')->transaction(function () use ($request, $validatedData, $programData, $id) {
                $program = $this->programRepository->update($programData, $id);

                if ($validatedData->has('image')) {
                    StorageHelper::upload($program, $request->file('image'), 'image');
                }

                if ($request->has('removed_booklet')) {
                    StorageHelper::deleteMedia($program, $request->removed_booklet);
                }

                if ($validatedData->has('booklet')) {
                    StorageHelper::upload($program, $request->booklet[0], 'booklet');
                }
            }, 5);
        } catch (Throwable $e) {
            abort(500, 'failed');
        }

        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->programRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }
}
