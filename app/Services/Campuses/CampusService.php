<?php


namespace App\Services\Campuses;


use App\Repositories\Campuses\CampusRepositoryInterface;

class CampusService
{
    /**
     * @var CampusRepositoryInterface
     */
    private $campusRepository;

    /**
     * CampusService constructor.
     * @param CampusRepositoryInterface $campusRepository
     */
    public function __construct(CampusRepositoryInterface $campusRepository)
    {
        $this->campusRepository = $campusRepository;
    }

    public function getAll()
    {
        return $this->campusRepository->getAll();
    }
}
