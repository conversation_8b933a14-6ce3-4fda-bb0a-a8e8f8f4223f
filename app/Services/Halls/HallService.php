<?php


namespace App\Services\Halls;


use App\Http\Requests\Hall\StoreHallRequest;
use App\Http\Requests\Hall\UpdateHallRequest;
use App\Repositories\Buildings\BuildingRepositoryInterface;
use App\Repositories\Halls\HallRepositoryInterface;

class HallService
{

    /**
     * @var BuildingRepositoryInterface
     */
    private $hallRepository;

    /**
     * BuildingService constructor.
     * @param HallRepositoryInterface $hallRepository
     */
    public function __construct(HallRepositoryInterface $hallRepository)
    {

        $this->hallRepository = $hallRepository;
    }


    public function getAll($buildingId)
    {
        return $this->hallRepository->getAll($buildingId);
    }

    public function create(StoreHallRequest $request)
    {
        $hallData = $request->validated();
        $hall = $this->hallRepository->create($hallData);
        return ['message' => 'success', 'code' => 200, 'id' => $hall->id];
    }

    public function update(UpdateHallRequest $request, $id)
    {
        $hallData = $request->validated();
        $this->hallRepository->update($hallData, $id);
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->hallRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

}
