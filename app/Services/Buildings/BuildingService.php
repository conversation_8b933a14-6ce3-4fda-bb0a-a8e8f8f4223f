<?php


namespace App\Services\Buildings;


use App\Http\Requests\Building\StoreBuildingRequest;
use App\Http\Requests\Building\UpdateBuildingRequest;
use App\Repositories\Buildings\BuildingRepositoryInterface;

class BuildingService
{

    /**
     * @var BuildingRepositoryInterface
     */
    private $buildingRepository;

    /**
     * BuildingService constructor.
     * @param BuildingRepositoryInterface $buildingRepository
     */
    public function __construct(BuildingRepositoryInterface $buildingRepository)
    {
        $this->buildingRepository = $buildingRepository;
    }


    public function getAll($withHalls = false, $onlyActiveHalls = false)
    {
        return $this->buildingRepository->getAll($withHalls, $onlyActiveHalls);
    }

    public function create(StoreBuildingRequest $request)
    {
        $buildingData = $request->validated();
        $building = $this->buildingRepository->create($buildingData);
        return ['message' => 'success', 'code' => 200, 'id' => $building->id];
    }

    public function update(UpdateBuildingRequest $request, $id)
    {
        $buildingData = $request->validated();
        $this->buildingRepository->update($buildingData, $id);
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->buildingRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }
}
