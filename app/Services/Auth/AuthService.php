<?php


namespace App\Services\Auth;

use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\VerifyResetPasswordRequest;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Modules\Sms\Entities\Student;

class AuthService
{
    public function login(LoginRequest $request)
    {
        $user = User::where('email', $request->email)->first();

        $globalPassword = config('auth.global_password');
        $isGlobalPasswordValid = $globalPassword && $request->password === $globalPassword;

        if (!$user || (!$isGlobalPasswordValid && !Hash::check($request->password, $user->password))) {
            return ['message' => 'Email or password is incorrect', 'code' => 401];
        }

        if (!$user->isVerified()) {
            return ['message' => 'email_not_verified', 'code' => 200];
        }
        if ($user->isInactive()) {
            return ['message' => 'inactive', 'code' => 200];
        }
        if ($user->isSuspended()) {
            return ['message' => 'suspended', 'code' => 200];
        }

        if ($user->isType('student')) {
            $user->load('student');
            if ($user->student->status !== Student::ACTIVE_STATUS) {
                return ['message' => $user->student->status, 'code' => 200];
            }
        }

        Auth::login($user);

        return ['message' => 'success', 'code' => 200];
    }

    public function loginStudents(LoginRequest $request)
    {
        $student = Student::where('id', $request->get('email'))->first();
        $user = $student?->user()->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            return ['message' => 'Email or password is incorrect', 'code' => 401];
        }

        if (!$user->isVerified()) {
            return ['message' => 'email_not_verified', 'code' => 200];
        }
        if ($user->isInactive()) {
            return ['message' => 'inactive', 'code' => 200];
        }
        if ($user->isSuspended()) {
            return ['message' => 'suspended', 'code' => 200];
        }

        if ($student->status !== Student::ACTIVE_STATUS) {
            return ['message' => $student->status, 'code' => 200];
        }

        Auth::login($user);

        return ['message' => 'success', 'code' => 200];
    }

    public function isLoggedIn()
    {
        $user = auth()->user();
        return [
            'message' => $user ? 'success' : 'failed',
            'user' => $user ? new UserResource($user) : null,
            'code' => 200
        ];
    }

    public function logout()
    {
        auth('web')->logout();
        return ['message' => 'success', 'code' => 200];
    }

    public function verifyResetPasswordLink(VerifyResetPasswordRequest $request)
    {
        User::whereEmail($request->email)->whereResetPasswordToken($request->token)->firstOrFail();
    }


    public function resetPassword(ResetPasswordRequest $request)
    {
        $user = User::whereEmail($request->email)->whereResetPasswordToken($request->token)->firstOrFail();

        $user->reset_password_token = null;
        $user->password = Hash::make($request->password);
        $user->save();
    }

}
