<?php


namespace App\Services\UserPermissions;


use App\Http\Requests\UserPermissions\UpdateUserPermissionsRequest;
use App\Models\User;
use App\Repositories\UserPermissions\UserPermissionRepositoryInterface;

class UserPermissionService
{
    /**
     * @var UserPermissionRepositoryInterface
     */
    private $userPermissionRepository;

    public function __construct(UserPermissionRepositoryInterface $userPrivilegeRepository)
    {
        $this->userPermissionRepository = $userPrivilegeRepository;
    }

    public function getPermissionsOf(User $user)
    {
        $userPermissions = $this->userPermissionRepository->getPermissionsOf($user);
        return $userPermissions->map(fn($permission) => [
            'id' => $permission->id,
            'entity_id' => $permission->pivot->entity_id
        ]);
    }

    public function updatePermissions(User $user, UpdateUserPermissionsRequest $request)
    {
        $this->userPermissionRepository->updateUserPermissions($user->id, $request->permissions_ids, $request->entity_id);

        return ['message' => 'success', 'code' => 200];
    }

    public function getUsersPermissions()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return User::
        whereHas('permissions', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->orWhereHas('roles', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->with('avatar')
            ->get();
    }

    public function deleteRolesAndPermissionsOf(User $user)
    {
        $user->permissions()->detach();
        $user->roles()->detach();
    }
}
