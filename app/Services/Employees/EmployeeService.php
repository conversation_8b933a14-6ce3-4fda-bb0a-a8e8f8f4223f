<?php


namespace App\Services\Employees;


use App\Http\Requests\Employee\StoreEmployeeRequest;
use App\Http\Requests\Employee\UpdateEmployeeRequest;
use App\Notifications\EmailVerificationNotification;
use App\Repositories\Employees\EmployeeRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class EmployeeService
{

    /**
     * @var EmployeeRepositoryInterface
     */
    private $employeeRepository;

    /**
     * LecturerService constructor.
     * @param EmployeeRepositoryInterface $employeeRepository
     */
    public function __construct(EmployeeRepositoryInterface $employeeRepository)
    {
        $this->employeeRepository = $employeeRepository;
    }

    public function getAll()
    {
        return $this->employeeRepository->getAll();
    }

    public function getPagination($elementsPerPage)
    {
        return $this->employeeRepository->getPagination($elementsPerPage);
    }

    public function create(StoreEmployeeRequest $request, $status = 'inactive')
    {
        $employeeData = collect($request->safe()->except('employee_id', 'hiring_date', 'hiring_decision_number', 'job_title', 'job_level'));
        $employeeData->put('type', 'employee');
        $employeeData->put('status', 'active');
        $employeeData->put('password', Hash::make($employeeData->get('password')));
        $employeeData->put('status', $status);

        $employeeData->put('employee_id', $request->employee_id);
        $employeeData->put('hiring_date', $request->hiring_date);
        $employeeData->put('hiring_decision_number', $request->hiring_decision_number);
        $employeeData->put('job_title', $request->job_title);
        $employeeData->put('job_level', $request->job_level);


        $user = $this->employeeRepository->create($employeeData->toArray());
        $user->notify(new EmailVerificationNotification());
        return ['message' => 'success', 'code' => 200, 'id' => $user->id];
    }

    public function show($id)
    {
        return $this->employeeRepository->show($id);
    }

    public function update(UpdateEmployeeRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        if ($validatedData->has('password')) {
            $validatedData->put('password', Hash::make($validatedData->get('password')));
        }
        $this->employeeRepository->update($id, $validatedData->toArray());
        return ['message' => 'success', 'code' => 200];
    }


    public function delete($id)
    {
        $this->employeeRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

    public function changeStatus($id, $status)
    {
        $this->employeeRepository->changeStatus($id, $status);
        return ['message' => 'success', 'code' => 200];
    }


}
