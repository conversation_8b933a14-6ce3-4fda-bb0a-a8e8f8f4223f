<?php

namespace App\Listeners;

class UserCreatedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
//        if ($event->user instanceof MustVerifyEmail && !$event->user->hasVerifiedEmail()) {
//            $authService = new AuthService();
//            $authService->sendVerificationCode($event->user);
//        }
    }
}
