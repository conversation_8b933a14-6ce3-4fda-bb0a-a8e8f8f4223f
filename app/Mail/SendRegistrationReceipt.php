<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Modules\StudentApplication\Entities\StudentApplication;

class SendRegistrationReceipt extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var StudentApplication
     */
    private $student;
    private $downloadUrl;

    /**
     * Create a new message instance.
     *
     * @param StudentApplication $student
     * @param $downloadUrl
     */
    public function __construct(StudentApplication $student, $downloadUrl)
    {
        $this->student = $student;
        $this->downloadUrl = $downloadUrl;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.temp-students.receipt')
            ->subject('طباعة نموذج التسجيل')
            ->with([
                'student' => $this->student,
                'url' => $this->downloadUrl
            ]);
    }
}
