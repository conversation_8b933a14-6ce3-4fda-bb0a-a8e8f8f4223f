<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendResetPasswordEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var User
     */
    public $user;
    public $link;

    /**
     * Create a new message instance.
     *
     * @param $user
     * @param $link
     */
    public function __construct($user, $link)
    {
        //
        $this->user = $user;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.auth.reset_password')
            ->subject('إستعادة كلمة المرور')
            ->with([
                'user' => $this->user,
                'link' => $this->link
            ]);
    }
}
