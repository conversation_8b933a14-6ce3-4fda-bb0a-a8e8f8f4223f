<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendVerificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $link;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param $user
     * @param $link
     */
    public function __construct($user, $link)
    {
        $this->link = $link;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.auth.verification')
            ->subject('تأكيد الحساب')
            ->from(config('mail.from.address'))
            ->with([
                'user' => $this->user,
                'link' => $this->link
            ]);
    }
}
