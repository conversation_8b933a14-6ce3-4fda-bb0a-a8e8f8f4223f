<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendApplicationVerificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    private $student;
    private $link;

    /**
     * Create a new message instance.
     *
     * @param $student
     * @param $link
     */
    public function __construct($student, $link)
    {
        $this->student = $student;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.temp-students.verification')
            ->subject('تأكيد الحساب')
            ->from(config('mail.from.address'))
            ->with([
                'student' => $this->student,
                'link' => $this->link
            ]);
    }
}
