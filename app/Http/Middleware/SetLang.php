<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class SetLang
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $languages = Language::get()->pluck('id')->toArray();
        if (in_array($request->segment(1), $languages)) {
            app()->setLocale($request->segment(1));
            //append default parameter in route requests
            Url::defaults(['lang' => $request->segment(1)]);

            //remove parameter from parameter list to avoid requiring in every controller method
            $request->route()->forgetParameter('lang');
            return $next($request);
        }
        abort(404);
    }
}
