<?php

namespace App\Http\Middleware;

use Closure;

class RedirectToNoneWWW
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(substr($request->header('host'), 0, 4) === 'www.') {
            $request->headers->set('host', substr($request->header('host'), 4));

            return redirect()->to('http://'.$request->header('host'));
        }
        return $next($request);
    }
}
