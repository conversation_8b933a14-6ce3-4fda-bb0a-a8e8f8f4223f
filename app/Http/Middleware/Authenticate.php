<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use Modules\Sms\Entities\Student;

class Authenticate extends Middleware
{

    protected function authenticate($request, array $guards) {

        $user = auth()->user();
        if ($user && ! $user->isVerified()) {
            auth()->logout();
        }
        if ($user && $user->isInactive()) {
            auth()->logout();
        }
        if ($user && $user->isSuspended()) {
            auth()->logout();
        }

        if($user && $user->isType('student')) {
            $user->load('student');
            if($user->student->status !== Student::ACTIVE_STATUS) {
                auth()->logout();
            }
        }

        parent::authenticate($request, $guards);
    }
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param Request $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return route('login');
        }
        abort(401);
    }
}
