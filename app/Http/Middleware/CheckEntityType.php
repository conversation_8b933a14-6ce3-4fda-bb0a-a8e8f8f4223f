<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckEntityType
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param $types
     * @return mixed
     */
    public function handle($request, Closure $next, ...$types)
    {
        $currentEntity = app('current_entity');
        if ($currentEntity->entityType()->whereIn('title->en', $types)->exists()) {
            return $next($request);
        }
        abort(404, 'Sorry, Request Not Found');
    }
}
