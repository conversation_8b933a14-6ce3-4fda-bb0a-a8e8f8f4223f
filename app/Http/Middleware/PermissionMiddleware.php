<?php

namespace App\Http\Middleware;

use Benjaber\Permission\Exceptions\UnauthorizedException;
use Closure;

class PermissionMiddleware
{
    public function handle($request, Closure $next, $permission, $entityId)
    {
        if (app('auth')->guest()) {
            throw UnauthorizedException::notLoggedIn();
        }

        if(auth()->user()->is_superadmin) {
            return $next($request);
        }

        $permissions = is_array($permission)
            ? $permission
            : explode('|', $permission);

        if (! auth()->user()->hasAnyPermission($permissions, $entityId)) {
            throw UnauthorizedException::forPermissions($permissions);
        }

        return $next($request);

    }
}
