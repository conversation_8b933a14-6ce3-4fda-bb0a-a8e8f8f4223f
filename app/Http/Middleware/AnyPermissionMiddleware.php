<?php

namespace App\Http\Middleware;

use App\Helpers\EntitiesHelper;
use Closure;
use Illuminate\Http\Request;

class AnyPermissionMiddleware
{
    public function handle(Request $request, Closure $next, $panel)
    {
        $entity = EntitiesHelper::getEntityId();
        abort_if(
            !auth()->user()->panels($entity)->where('permissions.panel_name', $panel)->exists(),
            403
        );
        return $next($request);
    }
}
