<?php

namespace App\Http\Middleware;

use Benjaber\Permission\Exceptions\UnauthorizedException;
use Closure;

class RoleMiddleware
{
    public function handle($request, Closure $next, $role, $entityId)
    {
        if (auth()->guest()) {
            throw UnauthorizedException::notLoggedIn();
        }

        if (auth()->user()->is_superadmin) {
            return $next($request);
        }

        $roles = is_array($role)
            ? $role
            : explode('|', $role);


        if (! auth()->user()->hasAnyRole($roles, $entityId)) {
            throw UnauthorizedException::forRoles($roles);
        }

        return $next($request);
    }
}
