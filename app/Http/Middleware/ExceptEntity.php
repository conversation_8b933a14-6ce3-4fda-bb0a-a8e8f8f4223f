<?php

namespace App\Http\Middleware;

use Closure;

class ExceptEntity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, ...$types)
    {
        $currentEntity = app('current_entity');
        if ($currentEntity->entityType()->whereIn('title->en', $types)->exists()) {
            abort(404, 'Sorry, Request Not Found');
        }
        return $next($request);
    }
}
