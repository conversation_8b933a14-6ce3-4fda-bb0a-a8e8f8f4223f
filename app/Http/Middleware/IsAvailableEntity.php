<?php

namespace App\Http\Middleware;

use App\Helpers\EntitiesHelper;
use Closure;

class IsAvailableEntity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $entityId = EntitiesHelper::getEntityId();
        if(app('allEntities')?->where('id', $entityId)->count() > 0) {
            return $next($request);
        }
        abort(404);
    }
}
