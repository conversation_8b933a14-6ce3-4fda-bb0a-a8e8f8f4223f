<?php

namespace App\Http\Middleware;

use Closure;

class CheckUserType
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, ...$types)
    {
        $user = auth()->user();
        if(! $user ) {
            abort(404);
        }
        foreach($types as $type) {
            if($user->isType($type)) {
                return $next($request);
            }
        }

        abort(404);
    }
}
