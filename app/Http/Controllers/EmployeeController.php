<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Employee\StoreEmployeeRequest;
use App\Http\Requests\Employee\UpdateEmployeeRequest;
use App\Models\User;
use App\Services\Employees\EmployeeService;
use Illuminate\Http\JsonResponse;

class EmployeeController extends Controller
{
    /**
     * @var EmployeeService
     */
    private $employeeService;

    /**
     * EmployeeController constructor.
     * @param EmployeeService $employeeService
     */
    public function __construct(EmployeeService $employeeService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-employees,' . $currentEntityId, ['only' => ['index', 'show']]);
        $this->middleware('permission:create-employees,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-employees,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-employees,' . $currentEntityId, ['only' => ['destroy']]);
        $this->employeeService = $employeeService;
    }

    /**
     * Display a listing of the resource.
     *
     */
    public function index()
    {
        return $this->employeeService->getAll();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreEmployeeRequest $request
     * @return JsonResponse
     */
    public function store(StoreEmployeeRequest $request)
    {
        return response()->json($this->employeeService->create($request, 'active'));
    }

    /**
     * Display the specified resource.
     *
     * @param User $user
     */
    public function show($id)
    {
        $user = User::findOrFail($id);
        return $this->employeeService->show($user->id);
    }

    /**
     * @param UpdateEmployeeRequest $request
     * @param User $user
     * @return JsonResponse
     */
    public function update(UpdateEmployeeRequest $request, $id)
    {
        $user = User::findOrFail($id);
        return response()->json($this->employeeService->update($request, $user->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return array
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        return $this->employeeService->delete($user->id);
    }
}
