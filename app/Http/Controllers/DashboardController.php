<?php

namespace App\Http\Controllers;

use App\Models\Building;
use App\Models\Hall;
use App\Models\Lecturer;
use App\Models\Program;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $entitiesCount = count($entitiesIds);

        $programs = Program::count();

        $lecturers = Lecturer::whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))->count();

        $employees = User::whereIn('entity_id', $entitiesIds)->where('type', User::EMPLOYEE_TYPE)->count();

        $buildings = Building::count();

        $halls = Hall::count();

        $result = [
            'entities' => $entitiesCount,
            'programs' => $programs,
            'lecturers' => $lecturers,
            'employees' => $employees,
            'buildings' => $buildings,
            'halls' => $halls,
        ];

        return response()->json($result);
    }
}
