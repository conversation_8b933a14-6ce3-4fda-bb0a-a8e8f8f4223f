<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Subject\StoreSubjectRequest;
use App\Http\Requests\Subject\UpdateSubjectRequest;
use App\Models\Program;
use App\Models\Subject;
use App\Services\Subjects\SubjectService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class SubjectController extends Controller
{
    /**
     * @var SubjectService
     */
    private $subjectService;

    /**
     * SubjectController constructor.
     * @param SubjectService $subjectService
     */
    public function __construct(SubjectService $subjectService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-subjects,' . $currentEntityId, ['only' => ['index', 'show']]);
        $this->middleware('permission:create-subjects,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-subjects,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-subjects,' . $currentEntityId, ['only' => ['destroy']]);

        $this->subjectService = $subjectService;
    }

    public function index(Program $program)
    {
        $program->subjects = $this->subjectService->subjectsOfProgram($program->id);
        $program->based_on = $this->subjectService->getBasedOnSubjectsOfProgram($program->id);
        return $program;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Program $program
     * @param StoreSubjectRequest $request
     * @return JsonResponse
     */
    public function store(Program $program, StoreSubjectRequest $request)
    {
        return response()->json($this->subjectService->create($request), 200);
    }

    /**
     * Display the specified resource.
     *
     * @param Program $program
     * @param Subject $subject
     * @return Response
     */
    public function show(Program $program, Subject $subject)
    {
        return $this->subjectService->show($subject->id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Program $program
     * @param UpdateSubjectRequest $request
     * @param Subject $subject
     * @return JsonResponse
     */
    public function update(Program $program, UpdateSubjectRequest $request, Subject $subject)
    {
        return response()->json($this->subjectService->update($request, $subject->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Program $program
     * @param Subject $subject
     * @return array
     */
    public function destroy(Program $program, Subject $subject)
    {
        return $this->subjectService->delete($subject->id);
    }
}
