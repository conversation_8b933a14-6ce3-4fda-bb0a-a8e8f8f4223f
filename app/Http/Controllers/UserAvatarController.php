<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\UpdateUserAvatarRequest;
use App\Services\Users\UserService;

class UserAvatarController extends Controller
{
    /**
     * @var UserService
     */
    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    public function update(UpdateUserAvatarRequest $request)
    {
        // return response()->json(['message' => 'success', 'image' => auth()->user()->avatar?->getFullUrl(),'code' => 200]);
        return response()->json($this->userService->updateAvatar(auth()->user()->id, $request));
    }
}
