<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Program\StoreProgramRequest;
use App\Http\Requests\Program\UpdateProgramRequest;
use App\Http\Resources\Program\ShowProgramResource;
use App\Models\Program;
use App\Repositories\Programs\ProgramRepository;
use App\Services\Programs\ProgramService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProgramController extends Controller
{

    /**
     * @var ProgramRepository
     */
    private $programService;

    public function __construct(ProgramService $programService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-programs,' . $currentEntityId, ['only' => ['show']]);
        $this->middleware('permission:create-programs,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-programs,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('superadmin', ['only' => ['destroy']]);

        $this->programService = $programService;
    }

    public function index(Request $request)
    {
        return $this->programService->getAll($request->get('type'), $request->get('active_only'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreProgramRequest $request
     * @return JsonResponse
     */
    public function store(StoreProgramRequest $request)
    {
        return response()->json($this->programService->create($request), 200);
    }

    /**
     * Display the specified resource.
     *
     * @param Program $program
     */
    public function show(Program $program)
    {
        //        $program = $this->programService->show($program->id);
        return new ShowProgramResource($this->programService->show($program->id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateProgramRequest $request
     * @param Program $program
     * @return JsonResponse
     */
    public function update(UpdateProgramRequest $request, Program $program)
    {
        if($request->get('duration_unit') != $program->duration_unit && $program->students()->count()){
            abort(403, 'You can not change the duration unit of a program that has students');
        }
        return response()->json($this->programService->update($request, $program->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Program $program
     * @return array|void
     */
    public function destroy(Program $program)
    {
        return $this->programService->delete($program->id);
    }
}
