<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\UpdateUserSocialLinksRequest;
use App\Http\Resources\User\ShowSocialLinksResource;
use App\Services\Users\UserService;

class UserSocialLinksController extends Controller
{
    /**
     * @var UserService
     */
    private UserService $userService;

    /**
     * UserSocialLinksController constructor.
     * @param UserService $userService
     */
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function index()
    {
        $socialLinks = $this->userService->getSocialLinks(auth()->user()->id);
        return ShowSocialLinksResource::collection($socialLinks)->response();
    }

    public function update(UpdateUserSocialLinksRequest $request)
    {
        return response()->json($this->userService->updateSocialLinks(auth()->user()->id, $request), 200);
    }
}
