<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Building\StoreBuildingRequest;
use App\Http\Requests\Building\UpdateBuildingRequest;
use App\Http\Resources\BuildingResource;
use App\Models\Building;
use App\Services\Buildings\BuildingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BuildingController extends Controller
{
    /**
     * @var BuildingService
     */
    private $buildingService;

    /**
     * BuildingController constructor.
     * @param BuildingService $buildingService
     */
    public function __construct(BuildingService $buildingService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-buildings,' . $currentEntityId, ['only' => ['show']]);
        $this->middleware('permission:create-buildings,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-buildings,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-buildings,' . $currentEntityId, ['only' => ['destroy']]);
        $this->buildingService = $buildingService;
    }

    public function index()
    {
        return BuildingResource::collection($this->buildingService->getAll());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBuildingRequest $request
     * @return JsonResponse
     */
    public function store(StoreBuildingRequest $request)
    {
        return response()->json($this->buildingService->create($request), 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param Building $building
     * @return JsonResponse
     */
    public function update(UpdateBuildingRequest $request, Building $building)
    {
        return response()->json($this->buildingService->update($request, $building->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Building $building
     * @return array
     */
    public function destroy(Building $building)
    {
        return $this->buildingService->delete($building->id);
    }
}
