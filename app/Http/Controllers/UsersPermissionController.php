<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\UserPermissions\UpdateUserPermissionsRequest;
use App\Models\User;
use App\Models\UsersPrivileges;
use App\Services\PrivilegesService;
use App\Services\UserPermissions\UserPermissionService;
use App\Services\UserRoles\UserRoleService;

class UsersPermissionController extends Controller
{

    private $userPermissionService;
    private $userRoleService;

    public function __construct(UserPermissionService $userPermissionService, UserRoleService $userRoleService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-privileges,' . $currentEntityId, ['only' => ['index', 'show']]);
        $this->middleware('permission:update-privileges,' . $currentEntityId, ['only' => ['update']]);
        $this->userPermissionService = $userPermissionService;
        $this->userRoleService = $userRoleService;
    }

    public function index()
    {
        $users = $this->userPermissionService->getUsersPermissions();

        foreach ($users as &$user) {
            $user['image'] = $user->avatar()->first()?->getFullUrl();
        }

        return response()->json($users);
    }

    public function show(User $user)
    {
        $permissions = $this->userPermissionService->getPermissionsOf($user);
        $roles = $this->userRoleService->getRoles($user);
        $user['image'] = $user->avatar()->first()?->getFullUrl();
        return response()->json([
            'user' => $user,
            'permissions' => $permissions,
            'roles' => $roles
        ]);
    }

    public function update(UpdateUserPermissionsRequest $request, User $user)
    {
        return response()->json($this->userPermissionService->updatePermissions($user, $request));
    }

    public function destroy(User $user)
    {
        $this->userPermissionService->deleteRolesAndPermissionsOf($user);

        return response()->json(['message' => 'success']);
    }
}
