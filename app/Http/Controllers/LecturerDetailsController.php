<?php

namespace App\Http\Controllers;

use App\Http\Requests\Lecturer\UpdateLecturerDetailsRequest;
use App\Http\Resources\Lecturer\ShowLecturerDetailsResource;
use App\Services\Lecturers\LecturerService;

class LecturerDetailsController extends Controller
{
    /**
     * @var LecturerService
     */
    private LecturerService $lecturerService;

    /**
     * LecturerController constructor.
     * @param LecturerService $lecturerService
     */
    public function __construct(LecturerService $lecturerService)
    {
        $this->lecturerService = $lecturerService;
    }

    public function index()
    {
        $lecturer = auth()->user()->lecturer;
        return new ShowLecturerDetailsResource($lecturer);
    }

    public function update(UpdateLecturerDetailsRequest $request)
    {
        $lecturer = auth()->user()->lecturer;
        return response()->json($this->lecturerService->updateDetails($lecturer->id, $request), 200);
    }
}
