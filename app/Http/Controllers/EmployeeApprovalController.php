<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\ChangeStatusRequest;
use App\Models\User;
use App\Services\Employees\EmployeeService;

class EmployeeApprovalController extends Controller
{
    /**
     * @var EmployeeService
     */
    private $employeeService;

    /**
     * EmployeeApprovalController constructor.
     * @param EmployeeService $employeeService
     */
    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    public function update($id, ChangeStatusRequest $request)
    {
        $user = User::findOrFail($id);
        $result = $this->employeeService->changeStatus($user->id, $request->get('status'));
        return response()->json($result);
    }
}
