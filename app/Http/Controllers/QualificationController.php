<?php

namespace App\Http\Controllers;

use App\Models\Qualification;
use App\Repositories\Qualifications\QualificationRepository;
use App\Repositories\Qualifications\QualificationRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class QualificationController extends Controller
{

    /**
     * @var QualificationRepository
     */
    private $qualificationRepository;

    public function __construct(QualificationRepositoryInterface $qualificationRepository)
    {

        $this->qualificationRepository = $qualificationRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Qualification[]|Collection
     */
    public function __invoke()
    {
        return $this->qualificationRepository->getAll();
    }

}
