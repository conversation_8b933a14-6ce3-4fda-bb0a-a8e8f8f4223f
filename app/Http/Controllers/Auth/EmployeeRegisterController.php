<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Employee\StoreEmployeeRequest;
use App\Services\Employees\EmployeeService;

class EmployeeRegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */
    /**
     * @var EmployeeService
     */
    private $employeeService;

    /**
     * Create a new controller instance.
     *
     * @param EmployeeService $employeeService
     */
    public function __construct(EmployeeService $employeeService)
    {
        $this->middleware('guest');
        $this->employeeService = $employeeService;
    }

    public function register(StoreEmployeeRequest $request)
    {
        return response()->json($this->employeeService->create($request), 200);
    }
}
