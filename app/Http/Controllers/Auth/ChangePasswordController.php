<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ChangePasswordRequest;
use App\Services\Users\UserService;

class ChangePasswordController extends Controller
{

    /**
     * @var UserService
     */
    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    public function update(ChangePasswordRequest $request)
    {
        return response()->json($this->userService->changePassword(auth()->user()->id, $request->password));
    }
}
