<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\EmailVerificationNotification;
use Illuminate\Http\Request;

class VerificationController extends Controller
{

    public function resend(Request $request)
    {
        // $request->validate([
        //     'recaptcha_token' => ['required', new ReCaptchaRule],
        // ]);

        $user = User::where('email', $request->get('email'))->firstOrFail();

        if ($user->hasVerifiedEmail()) {
            return response()->json(['message' => 'already_verified']);
        }
        $user->notify(new EmailVerificationNotification());
        return response()->json(['message' => 'success']);
    }

    public function verify(Request $request, User $user)
    {
        if (!$request->hasValidSignature()) {
            abort(401);
        }
        if ($user->hasVerifiedEmail()) {
            return response()->json(['message' => 'already_verified']);
        }
        $user->markEmailAsVerified();
        return response()->json(['message' => 'success']);
    }
}
