<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgotPasswordEmailRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\VerifyResetPasswordRequest;
use App\Models\User;
use App\Notifications\ResetPasswordNotification;
use App\Services\Auth\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ForgotPasswordController extends Controller
{
    /**
     * @var AuthService
     */
    private $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Send a reset link to the given user.
     *
     * @param ForgotPasswordEmailRequest $request
     * @return JsonResponse
     */
    public function sendResetLinkEmail(ForgotPasswordEmailRequest $request)
    {
        $user = User::where('email', $request->email)->firstOrFail();
        $user->notify(new ResetPasswordNotification());

        return response()->json(['message' => 'success']);
    }

    /**
     * Send a reset link to the given user.
     *
     * @param VerifyResetPasswordRequest $request
     * @return JsonResponse
     */

    public function verifyResetPasswordLink(VerifyResetPasswordRequest $request)
    {
        if (! $request->hasValidSignature()) {
            abort(401, 'Route not valid');
        }
        $this->authService->verifyResetPasswordLink($request);

        return response()->json(['message' => 'success', 'email' => $request->email, 'token' => $request->token]);
    }

    /**
     * Send a reset link to the given user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        $this->authService->resetPassword($request);

        return response()->json(['message' => 'success']);
    }

}
