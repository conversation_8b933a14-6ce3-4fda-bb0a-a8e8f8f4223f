<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Lecturer\StoreLecturerRequest;
use App\Providers\RouteServiceProvider;
use App\Services\Lecturers\LecturerService;

class Lecturer<PERSON><PERSON><PERSON><PERSON><PERSON>roller extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME, $user;
    /**
     * @var LecturerService
     */
    private $lecturerService;

    /**
     * Create a new controller instance.
     *
     * @param LecturerService $lecturerService
     */
    public function __construct(LecturerService $lecturerService)
    {
        $this->middleware('guest');
        $this->lecturerService = $lecturerService;
    }

    public function register(StoreLecturerRequest $request)
    {
        return response()->json($this->lecturerService->create($request), 200);
    }
}
