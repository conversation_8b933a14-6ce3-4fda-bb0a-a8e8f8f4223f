<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Services\Auth\AuthService;
use Illuminate\Http\Request;

class LoginController extends Controller
{

    private $authService;

    public function __construct(AuthService $authService)
    {
        $this->middleware('guest')->only('login');
        $this->middleware('auth:sanctum')->only('logout');
        $this->authService = $authService;
    }

    public function login(LoginRequest $request)
    {
        $result = $this->authService->login($request);
        return response()
            ->json($result, $result['code']);
    }

    public function loginStudents(LoginRequest $request)
    {
        // $result = $this->authService->loginStudents($request);
        $result = $this->authService->login($request);
        return response()
            ->json($result, $result['code']);
    }

    public function logout(Request $request)
    {
        $result = $this->authService->logout();
        return $request->routeType == 'api' ? $result : redirect()->back();
    }


    public function isLoggedIn()
    {
        return $this->authService->isLoggedIn();
    }
}
