<?php

namespace App\Http\Controllers;

use App\Models\AcademicRank;
use App\Repositories\AcademicRanks\AcademicRankRepository;
use App\Repositories\AcademicRanks\AcademicRankRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class AcademicRankController extends Controller
{

    /**
     * @var AcademicRankRepository
     */
    private $academicRankRepository;

    public function __construct(AcademicRankRepositoryInterface $academicRankRepository)
    {
        $this->academicRankRepository = $academicRankRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return AcademicRank[]|Collection
     */
    public function __invoke()
    {
        return $this->academicRankRepository->getAll();
    }
}
