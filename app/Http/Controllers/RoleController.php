<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\RoleRequest;
use App\Http\Resources\RoleResource;
use App\Models\Role;

class RoleController extends Controller
{
    public function index()
    {
        $roles = Role::paginate();

        return RoleResource::collection($roles);
    }

    public function store(RoleRequest $request)
    {
        $role = Role::create([
            'panel_name' => 'sms',
            'name' => $request->get('name'),
            'level' => implode(',', $request->get('level'))
        ]);

        $role->permissions()->sync($request->get('permissions'));

        return new RoleResource($role);
    }

    public function show(Role $role)
    {
        $role->load('permissions');

        return new RoleResource($role);
    }

    public function update(RoleRequest $request, Role $role)
    {
        $role->update([
            'panel_name' => 'sms',
            'name' => $request->get('name'),
            'level' => implode(',', $request->get('level'))
        ]);

        $role->permissions()->sync($request->get('permissions'));

        return new RoleResource($role);
    }

    public function destroy(Role $role)
    {
        $role->delete();

        return new RoleResource($role);
    }
}
