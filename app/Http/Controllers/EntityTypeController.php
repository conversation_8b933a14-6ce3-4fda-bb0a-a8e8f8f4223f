<?php

namespace App\Http\Controllers;

use App\Repositories\EntityTypes\EntityTypeRepositoryInterface;
use Illuminate\Http\Response;

class EntityTypeController extends Controller
{
    /**
     * @var EntityTypeRepositoryInterface
     */
    private $entityTypeRepository;

    /**
     * MartialStatusController constructor.
     * @param EntityTypeRepositoryInterface $entityTypeRepository
     */
    public function __construct(EntityTypeRepositoryInterface $entityTypeRepository)
    {
        $this->entityTypeRepository = $entityTypeRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->entityTypeRepository->getAll();
    }
}
