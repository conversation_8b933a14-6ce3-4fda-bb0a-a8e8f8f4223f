<?php

namespace App\Http\Controllers;

use App\Services\Languages\LanguageService;
use Illuminate\Http\Response;

class LanguageController extends Controller
{
    /**
     * @var LanguageService
     */
    private $languageService;

    /**
     * LanguageController constructor.
     * @param LanguageService $languageService
     */
    public function __construct(LanguageService $languageService)
    {
        $this->languageService = $languageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->languageService->getAll();
    }
}
