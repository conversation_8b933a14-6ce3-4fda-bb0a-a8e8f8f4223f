<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Hall\StoreHallRequest;
use App\Http\Requests\Hall\UpdateHallRequest;
use App\Http\Resources\BuildingResource;
use App\Models\Building;
use App\Models\Hall;
use App\Services\Halls\HallService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class HallController extends Controller
{
    /**
     * @var HallService
     */
    private $hallService;

    /**
     * HallController constructor.
     * @param HallService $hallService
     */
    public function __construct(HallService $hallService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-halls,'.$currentEntityId, ['only' => ['show']]);
        $this->middleware('permission:create-halls,'.$currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-halls,'.$currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-halls,'.$currentEntityId, ['only' => ['destroy']]);

        $this->hallService = $hallService;
    }

    public function index(Building $building)
    {
        return new BuildingResource($this->hallService->getAll($building->id));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Building $building
     * @param StoreHallRequest $request
     * @return JsonResponse
     */
    public function store(Building $building, StoreHallRequest $request)
    {
        return response()->json($this->hallService->create($request), 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Building $building
     * @param Hall $hall
     * @return array
     */
    public function destroy(Building $building, Hall $hall)
    {
        abort_if($hall->building_id != $building->id, 404);

        $hall->update([
            'is_active' => !$hall->is_active
        ]);
        return ['message' => 'success', 'code' => 200];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Building $building
     * @param UpdateHallRequest $request
     * @param Hall $hall
     * @return JsonResponse
     * @throws HttpException
     * @throws NotFoundHttpException
     */
    public function update(Building $building, UpdateHallRequest $request, Hall $hall)
    {
        abort_if($hall->building_id != $building->id, 404);
        return response()->json($this->hallService->update($request, $hall->id));
    }
}
