<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Program;
use Illuminate\Http\Request;

class BasedOnProgramController extends Controller
{
    public function index(Request $request)
    {
        $currentEntity = app('current_entity');

        if($currentEntity->isDepartment()) {
            $currentEntity = $currentEntity->parent;
        }

        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        return Program::with('programType', 'entity')
            ->where('is_active', true)
            ->whereIn('entity_id', $entitiesIds)
            ->where('is_rewarded', false)
            ->get();

    }
}
