<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Models\Program;
use App\Services\Subjects\SubjectService;

class ProgramSubjectsController extends Controller
{
    /**
     * @var SubjectService
     */
    private $subjectService;

    /**
     * ProgramSubjectsController constructor.
     * @param SubjectService $subjectService
     */
    public function __construct(SubjectService $subjectService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-subjects,' . $currentEntityId, ['only' => ['show']]);
        $this->subjectService = $subjectService;
    }

    public function show(Program $program)
    {
        $program->subjects = $this->subjectService->subjectsOfProgram($program->id);
//        $program->based_on = $this->subjectService->getBasedOnSubjectsOfProgram($program->id);
        return $program;
    }

}
