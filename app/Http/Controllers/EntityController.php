<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Entity\DeleteEntityRequest;
use App\Http\Requests\Entity\StoreEntityRequest;
use App\Http\Requests\Entity\UpdateEntityRequest;
use App\Http\Resources\Entity\ShowEntityResource;
use App\Models\Entity;
use App\Services\Entities\EntityService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class EntityController extends Controller
{

    /**
     * @var EntityService
     */
    private $entityService;

    public function __construct(EntityService $entityService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-entities,' . $currentEntityId, ['only' => ['show']]);
        $this->middleware('permission:create-entities,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-entities,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-entities,' . $currentEntityId, ['only' => ['destroy']]);

        $this->entityService = $entityService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        return $this->entityService->getAll();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreEntityRequest $request
     * @return JsonResponse|void
     */
    public function store(StoreEntityRequest $request)
    {
        return response()->json($this->entityService->create($request), 200);
    }

    /**
     * Display the specified resource.
     *
     * @param Entity $entity
     */
    public function show(Entity $entity)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        if (!in_array($entity->id, $entitiesIds)) {
            abort(404);
        }
        return new ShowEntityResource($this->entityService->show($entity->id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateEntityRequest $request
     * @param Entity $entity
     * @return JsonResponse
     */
    public function update(UpdateEntityRequest $request, Entity $entity)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        if (!in_array($entity->id, $entitiesIds)) {
            abort(404);
        }
        return response()->json($this->entityService->update($request, $entity->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param DeleteEntityRequest $request
     * @param Entity $entity
     * @return array|void
     */
    public function destroy(DeleteEntityRequest $request, Entity $entity)
    {
        return $this->entityService->delete($entity->id);
    }
}
