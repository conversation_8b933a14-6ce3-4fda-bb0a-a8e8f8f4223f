<?php

namespace App\Http\Controllers;

use App\Repositories\SubjectTypes\SubjectTypeRepositoryInterface;
use Illuminate\Http\Response;

class SubjectTypeController extends Controller
{
    /**
     * @var SubjectTypeRepositoryInterface
     */
    private $subjectTypeRepository;

    /**
     * ProgramTypeController constructor.
     * @param SubjectTypeRepositoryInterface $subjectTypeRepository
     */
    public function __construct(SubjectTypeRepositoryInterface $subjectTypeRepository)
    {
        $this->subjectTypeRepository = $subjectTypeRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->subjectTypeRepository->getAll();
    }
}
