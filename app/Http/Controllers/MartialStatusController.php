<?php

namespace App\Http\Controllers;

use App\Repositories\MartialStatuses\MartialStatusRepositoryInterface;
use Illuminate\Http\Response;


class MartialStatusController extends Controller
{
    /**
     * @var MartialStatusRepositoryInterface
     */
    private $martialStatusRepository;

    /**
     * MartialStatusController constructor.
     * @param MartialStatusRepositoryInterface $martialStatusRepository
     */
    public function __construct(MartialStatusRepositoryInterface $martialStatusRepository)
    {
        $this->martialStatusRepository = $martialStatusRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->martialStatusRepository->getAll();
    }
}
