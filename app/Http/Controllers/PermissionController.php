<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Models\Privilege;
use App\Repositories\Permissions\PermissionRepositoryInterface;
use App\Repositories\Roles\RoleRepositoryInterface;

class PermissionController extends Controller
{
    /**
     * @var PermissionRepositoryInterface
     */
    private $permissionRepository;
    private $roleRepository;

    /**
     * @param PermissionRepositoryInterface $permissionRepository
     */
    public function __construct(PermissionRepositoryInterface $permissionRepository, RoleRepositoryInterface $roleRepository)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-privileges,' . $currentEntityId);
        $this->permissionRepository = $permissionRepository;
        $this->roleRepository = $roleRepository;
    }

    public function __invoke()
    {
        return response()->json([
            'permissions' => $this->permissionRepository->getAll(),
            'roles' => $this->roleRepository->getAll()
        ]);
    }
}
