<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\ProgramBasedOn\StoreProgramBasedOnSubjectRequest;
use App\Http\Requests\ProgramBasedOn\UpdateProgramBasedOnSubjectRequest;
use App\Models\Program;
use App\Models\ProgramBasedOn;
use App\Services\Subjects\SubjectService;

class ProgramBasedOnController extends Controller
{

    /**
     * @var SubjectService
     */
    private $subjectService;

    /**
     * ProgramSubjectsController constructor.
     * @param SubjectService $subjectService
     */
    public function __construct(SubjectService $subjectService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-subjects,' . $currentEntityId, ['only' => ['show']]);
        $this->middleware('permission:create-subjects,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-subjects,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-subjects,' . $currentEntityId, ['only' => ['destroy']]);

        $this->subjectService = $subjectService;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Program $program
     * @param StoreProgramBasedOnSubjectRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Program $program, StoreProgramBasedOnSubjectRequest $request)
    {
        return response()->json($this->subjectService->addBasedOnSubject($program->id, $request), 200);
    }

    /**
     * Display the specified resource.
     *
     * @param $programId
     * @param $programBasedOnCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($programId, $programBasedOnCode)
    {
        return response()->json($this->subjectService->showBasedOnSubject($programId, $programBasedOnCode));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Program $program
     * @param $programBasedOnCode
     * @param UpdateProgramBasedOnSubjectRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Program $program, $programBasedOnCode, UpdateProgramBasedOnSubjectRequest $request)
    {
        return response()->json($this->subjectService->updateBasedOnSubjectCode($program->id, $programBasedOnCode, $request), 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Program $program
     * @param $programBasedOnCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Program $program, $programBasedOnCode)
    {
        ProgramBasedOn::where('code', $programBasedOnCode)->where('program_id', $program->id)->firstOrFail();
        return response()->json($this->subjectService->deleteBasedOnSubjectCode($program->id, $programBasedOnCode), 200);
    }
}
