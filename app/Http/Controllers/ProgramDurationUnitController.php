<?php

namespace App\Http\Controllers;

use App\Repositories\ProgramTypes\ProgramDurationUnitRepositoryInterface;
use Illuminate\Http\Response;

class ProgramDurationUnitController extends Controller
{
    /**
     * @var ProgramDurationUnitRepositoryInterface
     */
    private $programDurationUnitRepository;

    /**
     * ProgramTypeController constructor.
     * @param ProgramDurationUnitRepositoryInterface $programDurationUnitRepository
     */
    public function __construct(ProgramDurationUnitRepositoryInterface $programDurationUnitRepository)
    {
        $this->programDurationUnitRepository = $programDurationUnitRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->programDurationUnitRepository->getAll();
    }
}
