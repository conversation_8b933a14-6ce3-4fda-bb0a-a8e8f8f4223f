<?php

namespace App\Http\Controllers;

use App\Repositories\Links\LinkRepositoryInterface;
use Illuminate\Support\Facades\Response;

class LinkController extends Controller
{
    /**
     * @var LinkRepositoryInterface
     */
    private $linkRepository;

    /**
     * @param LinkRepositoryInterface $linkRepository
     */
    public function __construct(LinkRepositoryInterface $linkRepository)
    {
        $this->linkRepository = $linkRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __invoke()
    {
        return $this->linkRepository->getAll();
    }
}
