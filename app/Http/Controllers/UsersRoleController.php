<?php


namespace App\Http\Controllers;


use App\Helpers\EntitiesHelper;
use App\Http\Requests\UserRoles\UpdateUserRolesRequest;
use App\Models\User;
use App\Services\UserRoles\UserRoleService;

class UsersRoleController extends Controller
{
    /**
     * @var UserRoleService
     */
    private $userRoleService;

    /**
     * UsersPrivilegesController constructor.
     * @param UserRoleService $userRoleService
     */
    public function __construct(UserRoleService $userRoleService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-privileges,' . $currentEntityId, ['only' => ['index']]);
        $this->userRoleService = $userRoleService;
    }

    public function update(UpdateUserRolesRequest $request, User $user)
    {
        return response()->json($this->userRoleService->updateRoles($user, $request));
    }
}
