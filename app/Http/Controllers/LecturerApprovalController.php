<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\ChangeStatusRequest;
use App\Models\Lecturer;
use App\Services\Lecturers\LecturerService;

class LecturerApprovalController extends Controller
{
    /**
     * @var LecturerService
     */
    private $lecturerService;

    /**
     * LecturerApprovalController constructor.
     * @param LecturerService $lecturerService
     */
    public function __construct(LecturerService $lecturerService)
    {
        $this->lecturerService = $lecturerService;
    }

    public function update(Lecturer $lecturer, ChangeStatusRequest $request)
    {
        $result = $this->lecturerService->changeStatus($lecturer->id, $request->get('status'));
        return response()->json($result);
    }
}
