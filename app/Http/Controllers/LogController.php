<?php

namespace App\Http\Controllers;

use App\Models\Audit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LogController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->get('search');
        $model = $request->get('model');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        return Audit::when($request->get('event'), fn($q) => $q->where('event', $request->get('event')))
            ->when(
                $user,
                fn($q) => $q->whereHas(
                    'user',
                    fn($query) => $query->where(DB::raw('concat(first_name_ar," ",second_name_ar, " ", third_name_ar, " ", last_name_ar)'), 'LIKE', "%$user%")
                )
            )
            ->when($request->get('model'), fn($q) => $q->where('auditable_type', 'LIKE', "%$model%"))
            ->when($startDate, fn($q) => $q->whereDate('created_at', '>=', $startDate))
            ->when($endDate, fn($q) => $q->whereDate('created_at', '<=', $endDate))
            ->with('user')
            ->orderByDesc('id')
            ->paginate($request->perPage);
    }
}
