<?php

namespace App\Http\Controllers;

use App\Repositories\Nationalities\NationalityRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class NationalityController extends Controller
{

    private $nationalityRepository;

    public function __construct(NationalityRepositoryInterface $nationalityRepository)
    {
        $this->nationalityRepository = $nationalityRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Builder[]|Collection|\Illuminate\Support\Collection
     */
    public function __invoke()
    {
        return $this->nationalityRepository->getAll();
    }
}
