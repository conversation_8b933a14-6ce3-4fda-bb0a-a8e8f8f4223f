<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\Lecturer\StoreLecturerRequest;
use App\Http\Requests\Lecturer\UpdateLecturerRequest;
use App\Http\Resources\Lecturer\LecturersPublicListResource;
use App\Models\Lecturer;
use App\Services\Lecturers\LecturerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Spatie\SimpleExcel\SimpleExcelWriter;

class LecturerController extends Controller
{

    /**
     * @var LecturerService
     */
    private $lecturerService;

    /**
     * LecturerController constructor.
     * @param LecturerService $lecturerService
     */
    public function __construct(LecturerService $lecturerService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-lecturers,' . $currentEntityId, ['only' => ['index', 'show', 'export']]);
        $this->middleware('permission:create-lecturers,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-lecturers,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-lecturers,' . $currentEntityId, ['only' => ['destroy']]);
        $this->lecturerService = $lecturerService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        return $this->lecturerService->getAll();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreLecturerRequest $request
     * @return JsonResponse
     */
    public function store(StoreLecturerRequest $request)
    {
        return response()->json($this->lecturerService->create($request, 'active'));
    }

    /**
     * Display the specified resource.
     *
     * @param Lecturer $lecturer
     * @return Response
     */
    public function show(Lecturer $lecturer)
    {
        return $this->lecturerService->show($lecturer->id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateLecturerRequest $request
     * @param Lecturer $lecturer
     * @return JsonResponse
     */
    public function update(UpdateLecturerRequest $request, Lecturer $lecturer)
    {
        return response()->json($this->lecturerService->update($request, $lecturer->id));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Lecturer $lecturer
     * @return array
     */
    public function destroy(Lecturer $lecturer)
    {
        return $this->lecturerService->delete($lecturer->id);
    }

    public function publicList()
    {
        return LecturersPublicListResource::collection($this->lecturerService->getPublicList())->response();
    }

    public function export()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $lecturers =  Lecturer
            ::whereHas('user', fn ($query) => $query->whereIn('entity_id', $entitiesIds))
            ->with('user.entity.parent', 'user.nationality', 'qualification', 'academicRank')
            ->get();
        $array = [
            [
                'الاسم عربي',
                'الاسم انجليزي',
                'تاريخ الميلاد',
                'الرقم الوطني',
                'رقم جواز السفر',
                'العنوان',
                'الجنسية',
                'الكلية',
                'القسم',
                'رقم الهاتف',
                'البريد الالكتروني',
                'المؤهل العلمي',
                'المستوى الاكاديمي',
                'النوع',
            ]
        ];
        foreach ($lecturers as $lecturer) {
            $array[] = [
                $lecturer->user->first_name_ar . ' ' . $lecturer->user->second_name_ar . ' ' . $lecturer->user->third_name_ar . ' ' . $lecturer->user->last_name_ar,
                $lecturer->user->first_name_en . ' ' . $lecturer->user->second_name_en . ' ' . $lecturer->user->third_name_en . ' ' . $lecturer->user->last_name_en,
                $lecturer->user->dob,
                strval($lecturer->user->national_id),
                $lecturer->user->passport_number,
                $lecturer->user->address,
                $lecturer->user->nationality->title,
                $lecturer->user->entity?->parent?->title,
                $lecturer->user->entity?->title,
                $lecturer->user->phone_number,
                $lecturer->user->email,
                $lecturer->qualification->title,
                $lecturer->academicRank->title,
                __('website::lecturers.' . $lecturer->type),
            ];
        }
        SimpleExcelWriter::streamDownload($currentEntity->title . ' - أعضاء هيئة التدريس'  . now()->format('Y-m-d') . '.xlsx')
            ->noHeaderRow()
            ->addRows($array)
            ->toBrowser();
    }
}
