<?php

namespace App\Http\Controllers;

use App\Helpers\EntitiesHelper;
use App\Http\Requests\ProgramTypeRequest;
use App\Models\ProgramType;
use App\Repositories\ProgramTypes\ProgramTypeRepositoryInterface;

class ProgramTypeController extends Controller
{
    /**
     * @var ProgramTypeRepositoryInterface
     */
    private $programTypeRepository;

    /**
     * ProgramTypeController constructor.
     * @param  ProgramTypeRepositoryInterface  $programTypeRepository
     */
    public function __construct(ProgramTypeRepositoryInterface $programTypeRepository)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->programTypeRepository = $programTypeRepository;
        //        $this->middleware('permission:create-program types,'.$currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-program types,' . $currentEntityId, ['only' => ['update']]);
    }

    public function index()
    {
        return $this->programTypeRepository->getAll();
    }

    //    public function store(ProgramTypeRequest $request)
    //    {
    //        return response()->json(ProgramType::create($request->validated()));
    //    }

    public function update(ProgramType $programType, ProgramTypeRequest $request)
    {
        return response()->json($programType->update($request->validated()));
    }
}
