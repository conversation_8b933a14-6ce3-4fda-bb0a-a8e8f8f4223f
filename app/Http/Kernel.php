<?php

namespace App\Http;

use App\Http\Middleware\AnyPermissionMiddleware;
use App\Http\Middleware\Authenticate;
use App\Http\Middleware\CheckEntityType;
use App\Http\Middleware\CheckForMaintenanceMode;
use App\Http\Middleware\CheckUserMiddleware;
use App\Http\Middleware\CheckUserType;
use App\Http\Middleware\DenyIFrameRenderingMiddleware;
use App\Http\Middleware\EncryptCookies;
use App\Http\Middleware\ExceptEntity;
use App\Http\Middleware\IsAvailableEntity;
use App\Http\Middleware\OnlyAjax;
use App\Http\Middleware\PermissionMiddleware;
use App\Http\Middleware\RedirectIfAuthenticated;
use App\Http\Middleware\RedirectToNoneWWW;
use App\Http\Middleware\RoleMiddleware;
use App\Http\Middleware\SetLang;
use App\Http\Middleware\SuperadminMiddleware;
use App\Http\Middleware\TrimStrings;
use App\Http\Middleware\TrustProxies;
use App\Http\Middleware\VerifyCsrfToken;
use Benjaber98\LaravelHttps\Middlewares\ForceHttpMiddleware;
use Fruitcake\Cors\HandleCors;
use Illuminate\Auth\Middleware\AuthenticateWithBasicAuth;
use Illuminate\Auth\Middleware\Authorize;
use Illuminate\Auth\Middleware\EnsureEmailIsVerified;
use Illuminate\Auth\Middleware\RequirePassword;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Foundation\Http\Middleware\ValidatePostSize;
use Illuminate\Http\Middleware\SetCacheHeaders;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Routing\Middleware\ValidateSignature;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful;
use Modules\StudentApplication\Http\Middleware\CheckNewApplicationsEnabledMiddleware;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        TrustProxies::class,
        HandleCors::class,
        ForceHttpMiddleware::class,
        CheckForMaintenanceMode::class,
        ValidatePostSize::class,
        TrimStrings::class,
        ConvertEmptyStringsToNull::class,
        RedirectToNoneWWW::class,
        IsAvailableEntity::class,
        DenyIFrameRenderingMiddleware::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            ShareErrorsFromSession::class,
            VerifyCsrfToken::class,
            SubstituteBindings::class,
        ],

        'api' => [
            EnsureFrontendRequestsAreStateful::class,
            'throttle:60,1',
            SubstituteBindings::class,
            OnlyAjax::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => Authenticate::class,
        'auth.basic' => AuthenticateWithBasicAuth::class,
        'bindings' => SubstituteBindings::class,
        'cache.headers' => SetCacheHeaders::class,
        'can' => Authorize::class,
        'guest' => RedirectIfAuthenticated::class,
        'password.confirm' => RequirePassword::class,
        'signed' => ValidateSignature::class,
        'throttle' => ThrottleRequests::class,
        'verified' => EnsureEmailIsVerified::class,
        'type' => CheckEntityType::class,
        'except_entity' => ExceptEntity::class,
        'user_type' => CheckUserType::class,
        'setLang' => SetLang::class,
        'onlyAjax' => OnlyAjax::class,
        'role' => RoleMiddleware::class,
        'permission' => PermissionMiddleware::class,
        'in_user_entity' => CheckUserMiddleware::class,

        'superadmin' => SuperadminMiddleware::class,
        'any_permission' => AnyPermissionMiddleware::class,
        'new_applications_enabled' => CheckNewApplicationsEnabledMiddleware::class,
    ];
}
