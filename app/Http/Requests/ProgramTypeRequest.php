<?php

namespace App\Http\Requests;

use App\Rules\JsonTranslationsArray;
use Illuminate\Foundation\Http\FormRequest;

class ProgramTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => ['required', new JsonTranslationsArray],
            'total_credits' => ['required', 'numeric', 'min:0'],
            'min_enroll_subjects' => ['required', 'numeric', 'min:0'],
            'max_enroll_subjects' => ['required', 'numeric', 'min:0'],
        ];
    }
}
