<?php

namespace App\Http\Requests\Lecturer;

use App\Rules\JsonTranslationsArray;
use Illuminate\Foundation\Http\FormRequest;

class UpdateLecturerDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'start_date' => ['nullable', 'date'],
            'personal_website' => ['nullable', 'url'],
            'bio' => ['required', new JsonTranslationsArray],
            'job_title' => ['required', new JsonTranslationsArray],
            'general_major' => ['sometimes', 'nullable', 'string'],
            'specialization' => ['sometimes', 'nullable', 'string'],
            'cv' => ['sometimes', 'nullable', 'array'],
            'cv.*' => ['sometimes', 'nullable', 'file', 'mimes:pdf'],
            'removed_cv' => ['sometimes', 'nullable', 'array'],
            'removed_cv.*' => ['sometimes', 'required', 'integer']
        ];
    }
}
