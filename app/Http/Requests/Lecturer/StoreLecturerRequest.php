<?php

namespace App\Http\Requests\Lecturer;

use App\Http\Requests\User\UserCustomRules;
use App\Models\Lecturer;
use App\Models\User;
use App\Rules\AllowedDomain;
use App\Rules\JsonTranslationsArray;
use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLecturerRequest extends FormRequest
{

    public function type()
    {
        return 'lecturer';
    }

    use UserCustomRules;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $emailRules = [
            'required',
            'email',
            'max:255',
            Rule::unique(config('database.default') . '.users', 'email')
        ];
        // if ($this->get('type') == Lecturer::PERMANENT_TYPE && config('app.domain') == 'academy.edu.ly') {
        //     $emailRules[] = new AllowedDomain('academy.edu.ly');
        // }

        return array_merge($this->storeUserRules(), [
            'national_id' => [
                Rule::requiredIf($this->nationality_id == 'ly'),
                'nullable',
                'digits:12',
                Rule::unique(config('database.default') . '.users', 'national_id')
                    ->where('type', User::LECTURER_TYPE)
            ],
            'phone_number' => [
                'required',
                new PhoneNumber(),
                Rule::unique(config('database.default') . '.users', 'phone_number')->where('type', User::LECTURER_TYPE)
            ],
            'qualification_id' => [
                'required', 'integer',
                Rule::exists(config('database.default') . '.qualifications', 'id')
            ],
            'qualification_university' => ['sometimes', 'nullable', 'string', 'max:255'],
            'qualification_year' => ['sometimes', 'nullable', 'date_format:Y'],
            'general_major' => ['required', 'string', 'max:255'],
            'specialization' => ['required', 'string', 'max:255'],
            'university_number' => ['required', 'string', 'max:255'],

            'bank_name' => ['sometimes', 'nullable', 'string', 'max:255'],
            'bank_account_number' => ['sometimes', 'nullable', 'string', 'max:255'],
            'start_date' => ['sometimes', 'nullable', 'date'],
            'academic_rank_id' => [
                'required', 'integer',
                Rule::exists(config('database.default') . '.academic_ranks', 'id')
            ],
            'type' => ['required', Rule::in([Lecturer::COOPERATIVE_TYPE, Lecturer::PERMANENT_TYPE])],
            'email' => $emailRules,
        ]);
    }
}
