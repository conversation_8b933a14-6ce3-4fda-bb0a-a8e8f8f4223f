<?php


namespace App\Http\Requests\Subject;


use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait SubjectCustomRules
{
    public function getProgramId()
    {
        return null;
    }

    public function commonRules()
    {
        return [
            'program_id' => ['required', Rule::exists(config('database.default') . '.programs', 'id')],
            'title' => ['required', new JsonTranslationsArray],
            'description' => ['required', new JsonTranslationsArray],
            'weekly_lecture_hours' => ['required', 'integer'],
            'weekly_tutorial_hours' => ['required', 'integer',],
            'credits' => ['required', 'integer'],
            'mid_mark' => ['required', 'numeric'],
            'final_mark' => ['required', 'numeric'],
            'pass_mark' => ['required', 'numeric'],
            'type' => ['required', Rule::exists(config('database.default') . '.subjects_types', 'id')],
            'prerequisites' => ['present', 'array'],
            'is_active' => ['required', 'boolean'],
            'is_practical_exam_hurdle' => ['required', 'boolean'],
            'practical_final_mark' => ['required', 'numeric'],
            'sub_semester' => ['nullable', 'integer'],
        ];
    }
}
