<?php

namespace App\Http\Requests\Subject;

use App\Rules\SubjectInTheProgram;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSubjectRequest extends FormRequest
{
    use SubjectCustomRules;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $code = $this->code;
        $programId = $this->getProgramId();
        return array_merge($this->commonRules(), [
            'code' => ['required', 'max:10',
//                Rule::unique(config('database.default').'.subjects')
//                    ->where(
//                        fn($query) => $query->where('code', $code)->where('program_id', $programId)
//                    )
//                    ->ignore($this->subject->id, 'id')
            ],
            'prerequisites.*' => [
                'required',
                'integer',
                new SubjectInTheProgram($this->getProgramId()),
                Rule::notIn([$this->subject->id])
            ]
        ]);
    }

    public function getProgramId()
    {
        return $this->program_id;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'program_id' => $this->program->id
        ]);
    }
}
