<?php


namespace App\Http\Requests\Hall;


use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait HallCustomRules
{
    protected function prepareForValidation()
    {
        $this->merge([
            'building_id' => $this->building->id
        ]);
    }

    public function commonRules()
    {
        return [
            'entity_id' => ['required', Rule::exists(config('database.default') . '.entities', 'id')],
            'capacity' => ['required', 'integer'],
            'building_id' => [
                'required', 'integer',
                Rule::exists(config('database.default') . '.buildings', 'id')
            ],
            'title' => ['required', new JsonTranslationsArray]
        ];
    }
}
