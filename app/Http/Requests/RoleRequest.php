<?php

namespace App\Http\Requests;

use App\Rules\JsonTranslationsArray;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', new JsonTranslationsArray],
            'permissions' => ['required', 'array'],
            'permissions.*' => ['required', 'integer', Rule::exists(config('database.default') . '.permissions', 'id')],
            'level' => ['required', 'array', 'min:1'],
            'level.*' => ['required', Rule::in(['university', 'school', 'department'])]
        ];
    }
}
