<?php

namespace App\Http\Requests\UserPermissions;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserPermissionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'permissions_ids' => [
                'present',
                'array'
            ],
            'permissions_ids.*' => [
                'required',
                Rule::exists(config('database.default').'.permissions', 'id')
                    ->where(fn($query) => $query->whereNotNull('panel_name'))
            ],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default').'.entities', 'id')
            ]
        ];
    }
}
