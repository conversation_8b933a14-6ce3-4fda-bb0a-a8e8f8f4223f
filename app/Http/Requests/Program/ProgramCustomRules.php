<?php


namespace App\Http\Requests\Program;

use App\Models\EntityType;
use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait ProgramCustomRules
{
    public function commonRules()
    {
        return [
            'title' => ['required', new JsonTranslationsArray],
            'major' => ['required', new JsonTranslationsArray],
            'objectives' => ['required', new JsonTranslationsArray],
            'outcomes' => ['required', new JsonTranslationsArray],
            'description' => ['required', new JsonTranslationsArray],
            'entry_requirements' => ['required', new JsonTranslationsArray],
            'certificate_awarded' => ['required', new JsonTranslationsArray],
            'job_market' => ['required', new JsonTranslationsArray],
            'type' => ['required', Rule::exists(config('database.default') . '.program_types', 'id')],
            'duration' => ['required', 'integer', 'max:100'],
            'duration_unit' => ['required', Rule::exists(config('database.default') . '.program_duration_units', 'id')],
            'lang' => ['required', Rule::exists(config('database.default') . '.languages', 'id')],
            'weakly_teaching_hours' => ['required', 'integer'],
            'weakly_lab_hours' => ['required', 'integer'],
            'general_credits' => ['required', 'integer'],
            'elective_credits' => ['required', 'integer'],
            'compulsory_credits' => ['required', 'integer'],
            'training_credits' => ['required', 'integer'],
            'supportive_credits' => ['required', 'integer'],
            'total_credits' => ['required', 'integer'],
            'is_active' => ['required', 'boolean'],
            'entity_id' => ['required', Rule::exists(config('database.default').'.entities', 'id')->where('type', EntityType::DEPARTMENT)],
            'is_rewarded' => ['required', 'boolean'],
            'program_based_on_id' => ['nullable', Rule::exists(config('database.default').'.programs', 'id')->where('is_rewarded', false)],
        ];
    }
}
