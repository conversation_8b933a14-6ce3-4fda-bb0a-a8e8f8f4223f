<?php

namespace App\Http\Requests\Program;

use Illuminate\Foundation\Http\FormRequest;

class StoreProgramRequest extends FormRequest
{
    use ProgramCustomRules;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->commonRules(), [
            'image' => ['required', 'image'],
            'booklet' => ['sometimes', 'nullable', 'array'],
            'booklet.*' => ['sometimes', 'nullable', 'file', 'mimes:pdf'],
        ]);
    }
}
