<?php


namespace App\Http\Requests\User;


use App\Models\EntityType;
use App\Models\User;
use App\Rules\ArabicChars;
use App\Rules\EnglishChars;
use App\Rules\PhoneNumber;
use Illuminate\Validation\Rule;

trait UserCustomRules
{

    public function updateUserRules()
    {

        $type = $this->type();
        $user = $type == 'employee' ? User::find($this->id) : $this->$type->user;
        return array_merge($this->commonRules(), [
            'national_id' => [
                Rule::when($this->nationality_id == 'ly', ['required', 'digits:12', Rule::unique(config('database.default') . '.users', 'national_id')->ignore($user->id)], ['nullable']),
            ],
            'passport_number' => [Rule::requiredIf($this->nationality_id !== 'ly'), 'nullable',],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default') . '.entities', 'id')
                    ->where(function ($query) {
                        $query->where('type', EntityType::DEPARTMENT);
                    })
            ],
            'phone_number' => ['required', new PhoneNumber],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique(config('database.default') . '.users', 'email')->ignore($user->id)],
        ]);
    }

    public function type()
    {
        return 'student';
    }

    private function commonRules()
    {
        return [
            'first_name_ar' => ['required', 'string', 'max:255', new ArabicChars],
            'second_name_ar' => ['required', 'string', 'max:255', new ArabicChars],
            'third_name_ar' => ['required', 'string', 'max:255', new ArabicChars],
            'last_name_ar' => ['required', 'string', 'max:255', new ArabicChars],
            'first_name_en' => ['required', 'string', 'max:255', new EnglishChars],
            'second_name_en' => ['required', 'string', 'max:255', new EnglishChars],
            'third_name_en' => ['required', 'string', 'max:255', new EnglishChars],
            'last_name_en' => ['required', 'string', 'max:255', new EnglishChars],
            'nationality_id' => ['required', Rule::exists(config('database.default') . '.nationalities', 'id')],
            'address' => ['required', 'max:255'],
            'dob' => ['required', 'date'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'personal_id' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    public function storeUserRules()
    {
        return array_merge($this->commonRules(), [
            'national_id' => [
                Rule::when($this->nationality_id == 'ly', ['required', 'digits:12', Rule::unique(config('database.default') . '.users', 'national_id')], ['nullable']),
            ],
            'passport_number' => [Rule::requiredIf($this->nationality_id !== 'ly'), 'nullable'],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default') . '.entities', 'id')
                    ->where(function ($query) {
                        $query->where('type', EntityType::DEPARTMENT);
                    })
            ],
            'phone_number' => ['required', new PhoneNumber],
            'email' => ['required', 'email', 'max:255', Rule::unique(config('database.default') . '.users', 'email')],
            'password' => ['required', 'min:8', 'confirmed'],
        ]);
    }
}
