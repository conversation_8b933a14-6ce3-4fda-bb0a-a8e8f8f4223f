<?php


namespace App\Http\Requests\Building;


use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait BuildingCustomRules
{
    public function commonRules()
    {
        return [
            'longitude' => ['required', 'numeric', 'regex:/^\d+(\.\d{1,})?$/'],
            'latitude' => ['required', 'numeric', 'regex:/^\d+(\.\d{1,})?$/'],
            'title' => ['required', new JsonTranslationsArray],
            'description' => ['required', new JsonTranslationsArray],
            'campus_id' => ['required', 'integer', Rule::exists(config('database.default').'.campuses', 'id')]
        ];
    }
}
