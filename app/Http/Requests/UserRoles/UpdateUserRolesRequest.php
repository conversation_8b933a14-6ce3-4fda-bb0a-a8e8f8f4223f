<?php

namespace App\Http\Requests\UserRoles;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRolesRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'roles_ids' => [
                'present',
                'array'
            ],
            'permissions_ids.*' => [
                'required',
                Rule::exists(config('database.default').'.roles', 'id')
                    ->where(fn($query) => $query->whereNotNull('panel_name'))
            ],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default').'.entities', 'id')
            ]
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
