<?php

namespace App\Http\Requests\ProgramBasedOn;

use App\Models\ProgramBasedOn;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProgramBasedOnSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        ProgramBasedOn::where('code', $this->basedOn)->where('program_id', $this->program->id)->firstOrFail();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $programId = $this->program->id;
        return [
            'code' => [
                'required',
                Rule::unique(config('database.default').'.program_based_on', 'code')
                    ->where(fn($query) => $query->where('program_id', $programId))
                    ->ignore($this->basedOn, 'code')
            ],
        ];
    }
}
