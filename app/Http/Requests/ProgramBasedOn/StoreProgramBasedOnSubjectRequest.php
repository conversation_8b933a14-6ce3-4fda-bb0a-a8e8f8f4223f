<?php

namespace App\Http\Requests\ProgramBasedOn;

use App\Rules\SubjectNotInProgram;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProgramBasedOnSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $currentProgramId = $this->program->id;
        $programId = $this->program_id;
        return [
            'code' => [
                'required',
                Rule::unique(config('database.default').'.program_based_on', 'code')
                    ->where(fn($query) => $query->where('program_id', $currentProgramId))
                ],
            'subject_id' => [
                'required',
                new SubjectNotInProgram($currentProgramId),
                Rule::exists(config('database.default').'.subjects', 'id')
                ->where(function($query) use ($programId) {
                    $query->where('program_id', '=', $programId);
                }),
                Rule::unique(config('database.default').'.program_based_on', 'subject_id')
                    ->where(fn($query) => $query->where('program_id', $programId))
            ]
        ];
    }
}
