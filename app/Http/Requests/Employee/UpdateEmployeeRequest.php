<?php

namespace App\Http\Requests\Employee;

use App\Http\Requests\User\UserCustomRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeRequest extends FormRequest
{

    public function type()
    {
        return 'employee';
    }

    use UserCustomRules;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->updateUserRules(), [
            'national_id' => [
                Rule::requiredIf($this->nationality_id == 'ly'), 'nullable', 'digits:12', 
                Rule::unique(config('database.default') . '.users', 'national_id')->ignore($this->id),
            ],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default') . '.entities', 'id')
            ],
            'employee_id' => ['required', 'string', 'max:255'],
            'hiring_date' => ['required', 'date'],
            'hiring_decision_number' => ['required', 'string', 'max:255'],
            'job_title' => ['required', 'string', 'max:255'],
            'job_level' => ['required', 'integer', 'min:1', 'max:15'],
            'qualification_id' => [
                'required',
                Rule::exists(config('database.default') . '.qualifications', 'id')
            ],
        ]);
    }
}
