<?php


namespace App\Http\Requests\Entity;

use App\Models\EntityDetails;
use App\Models\EntityType;
use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait EntityCustomRules
{

    public function commonRules()
    {
        return [
            'title' => ['required', new JsonTranslationsArray],
            'tel' => ['required', 'regex:/^[0-9]{7,15}/'],
            'email' => ['required', 'email'],
            'entity_head' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.users', 'id')],
            'study_and_exams' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.users', 'id')],
            'campus_id' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.campuses', 'id')],
            'buildings' => ['present', 'array'],
            'buildings.*' => ['required', Rule::exists(config('database.default') . '.buildings', 'id')],
            'vision' => ['required', new JsonTranslationsArray],
            'message' => ['required', new JsonTranslationsArray],
            'description' => ['required', new JsonTranslationsArray],
            'goals' => ['required', new JsonTranslationsArray],
            'head_word' => ['required', new JsonTranslationsArray],
            'links' => ['present', 'array'],
            'links.*' => ['sometimes', 'nullable', 'url', 'max:255'],
            'entity_cover' => ['sometimes', 'nullable', 'image'],
            'departments' => [
                Rule::requiredIf(function () {
                    return $this->type === EntityType::SCHOOL || $this->type === EntityType::DEPARTMENT;
                }),
                'array', 'min:1', 'max:2'],
            'departments.*' => ['required', 'string', Rule::in([EntityDetails::Scientific, EntityDetails::Literary])],
        ];
    }
}
