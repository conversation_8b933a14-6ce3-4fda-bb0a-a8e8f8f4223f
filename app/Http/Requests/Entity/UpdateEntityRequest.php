<?php

namespace App\Http\Requests\Entity;

use App\Models\EntityType;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEntityRequest extends FormRequest
{
    use EntityCustomRules;

    protected function prepareForValidation()
    {
        if(!$this->has('links')) {
            $this->merge([
                'links' => [],
            ]);
        }

        if(!$this->has('buildings')) {
            $this->merge([
                'buildings' => []
            ]);
        }
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $entityTypeRules = [
            EntityType::UNIVERSITY => [EntityType::SCHOOL, EntityType::MANAGEMENT, EntityType::STUDENT],
            EntityType::SCHOOL => [EntityType::DEPARTMENT]
        ];
        $currentEntity = app('current_entity');
        return in_array($this->entity->type, $entityTypeRules[$currentEntity->type] ?? []) || $this->entity->id == $currentEntity->id;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->commonRules(), []);
    }
}
