<?php

namespace App\Http\Requests\Entity;

use App\Helpers\EntitiesHelper;
use App\Models\EntityType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StoreEntityRequest extends FormRequest
{
    use EntityCustomRules;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    public function prepareForValidation()
    {
        if (!preg_match('/^[a-zA-Z]{2,5}$/', $this->id)) {
            throw ValidationException::withMessages(['id' => 'The id format is invalid.']);
        }
        $this->merge([
            'id' => strtolower(EntitiesHelper::generateFullId($this->id)),
        ]);

        if(!$this->has('links')) {
            $this->merge([
                'links' => [],
            ]);
        }

        if(!$this->has('buildings')) {
            $this->merge([
                'buildings' => [],
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $entityTypeRules = [
            EntityType::UNIVERSITY => [EntityType::SCHOOL, EntityType::MANAGEMENT],
            EntityType::SCHOOL => [EntityType::DEPARTMENT]
        ];
        $currentEntity = app('current_entity');
        return array_merge($this->commonRules(), [
            'id' => ['required', 'max:30', 'regex:/^(?!:\/\/)([a-zA-Z0-9-_]+\.)*[a-zA-Z0-9][a-zA-Z0-9-_]+(\.[a-zA-Z])*?$/', Rule::unique(config('database.default').'.entities', 'id')],
            'type' => ['required', Rule::exists(config('database.default').'.entity_types', 'id')->where(fn($query) => $query->whereIn('id', $entityTypeRules[$currentEntity->type] ?? []))],
            'entity_cover' => ['required', 'image']
        ]);


    }
}
