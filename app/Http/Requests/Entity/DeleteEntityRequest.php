<?php

namespace App\Http\Requests\Entity;

use App\Models\EntityType;
use Illuminate\Foundation\Http\FormRequest;

class DeleteEntityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return false;
        $entityTypeRules = [
            EntityType::UNIVERSITY => [EntityType::SCHOOL, EntityType::MANAGEMENT],
            EntityType::SCHOOL => [EntityType::DEPARTMENT, EntityType::MANAGEMENT],
            EntityType::DEPARTMENT => [EntityType::MANAGEMENT],
        ];
        $currentEntity = app('current_entity');
        return in_array($this->entity->type, $entityTypeRules[$currentEntity->type]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
        ];
    }
}
