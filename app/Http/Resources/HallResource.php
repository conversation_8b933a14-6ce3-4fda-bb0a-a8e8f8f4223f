<?php

namespace App\Http\Resources;

use App\Models\Hall;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Hall */
class HallResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'capacity' => $this->capacity,
            'title' => $this->getTranslations('title'),
            'building_id' => $this->building_id,
            'entity_id' => $this->entity_id,
            'building' => new BuildingResource($this->whenLoaded('building')),
            'is_active' => $this->is_active
        ];
    }
}
