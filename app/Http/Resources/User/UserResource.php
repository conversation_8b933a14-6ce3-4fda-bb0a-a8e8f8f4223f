<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'first_name_ar' => $this->first_name_ar,
            'second_name_ar' => $this->second_name_ar,
            'third_name_ar' => $this->third_name_ar,
            'last_name_ar' => $this->last_name_ar,
            'first_name_en' => $this->first_name_en,
            'second_name_en' => $this->second_name_en,
            'third_name_en' => $this->third_name_en,
            'last_name_en' => $this->last_name_en,
            'dob' => $this->dob,
            'national_id' => $this->national_id,
            'passport_number' => $this->passport_number,
            'address' => $this->address,
            'type' => $this->type,
            'gender' => $this->gender,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'secondary_email' => $this->secondary_email,
            'image' => $this->avatar ? $this->avatar->getFullUrl() : $this->getFallbackMediaUrl('avatar'),
            'nationality_id' => $this->nationality_id,
            'entity_id' => $this->entity_id,
            'current_entity_permissions' => $this->currentEntityPermissions,
            'current_entity_roles' => $this->currentEntityRoles,
            'is_superadmin' => $this->when($this->is_superadmin, fn() => $this->is_superadmin)
        ];
    }
}
