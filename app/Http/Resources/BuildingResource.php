<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Building */
class BuildingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'title' => $this->getTranslations('title'),
            'description' => $this->getTranslations('description'),
            'halls' => HallResource::collection($this->whenLoaded('halls'))
        ];
    }
}
