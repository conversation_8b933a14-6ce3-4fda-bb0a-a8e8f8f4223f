<?php

namespace App\Http\Resources\Entity;

use Illuminate\Http\Resources\Json\JsonResource;

class ShowEntityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'url' => $this->url,
            'type' => $this->type,
            'tel' => $this->tel,
            'email' => $this->email,
            'entity_head' => $this->entity_head,
            'study_and_exams' => $this->study_and_exams,
            'buildings' => $this->buildings,
            'campus_id' => $this->campus_id,
            'details' => $this->details,
            'links' => $this->links,
            'entity_cover' => $this->entityCover?->getFullUrl(),
        ];
    }
}
