<?php

namespace App\Http\Resources\Entity;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Entity */
class SimpleEntityResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'parent' => new SimpleEntityResource($this->whenLoaded('parent')),
            'children' => SimpleEntityResource::collection($this->whenLoaded('directChildren')),
            'min_gpa' => $this->when($this->pivot, $this->pivot?->min_gpa),
            'departments' => $this->when($this->relationLoaded('details'), fn() => $this->details->departments)
        ];
    }
}
