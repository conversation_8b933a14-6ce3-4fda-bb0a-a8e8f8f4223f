<?php

namespace App\Http\Resources\Lecturer;

use App\Http\Resources\FileMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowLecturerDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'personal_website' => $this->personal_website,
            'start_date' => $this->start_date,
            'bio' => $this->getTranslations('bio'),
            'job_title' => $this->getTranslations('job_title'),
            'general_major' => $this->general_major,
            'specialization' => $this->specialization,
            'cv' => FileMediaResource::collection($this->getMedia('cv')),
        ];
    }
}
