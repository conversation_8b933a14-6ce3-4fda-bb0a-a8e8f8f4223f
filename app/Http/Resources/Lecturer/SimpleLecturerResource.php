<?php

namespace App\Http\Resources\Lecturer;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleLecturerResource extends JsonResource
{
    public function toArray($request)
    {
        app()->setLocale('ar');
        $result = [
            'id' => $this->id,
            'name_ar' => $this->user->full_name
        ];
        app()->setLocale('en');
        $result['name_en'] = $this->user->full_name;
        return $result;
    }
}
