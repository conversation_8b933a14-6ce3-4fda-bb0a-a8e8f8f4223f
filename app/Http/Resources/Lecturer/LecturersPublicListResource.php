<?php

namespace App\Http\Resources\Lecturer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LecturersPublicListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        app()->setLocale('ar');
        $result = [
            'id' => $this->id,
            'name_ar' => $this->full_name
        ];
        app()->setLocale('en');
        $result['name_en'] = $this->full_name;
        return $result;
    }
}
