<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Subject */
class SimpleSubjectResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title' => $this->getTranslations('title'),
            'program_id' => $this->program_id,
            'type' => $this->type,
            'mid_mark' => $this->mid_mark,
            'final_mark' => $this->final_mark,
            'practical_final_mark' => $this->practical_final_mark
        ];
    }
}
