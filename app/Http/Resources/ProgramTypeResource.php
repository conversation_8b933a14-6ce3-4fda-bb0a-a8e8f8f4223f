<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProgramTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->name,
            'total_credits' => $this->total_credits,
            'min_enroll_subjects' => $this->min_enroll_subjects,
            'max_enroll_subjects' => $this->max_enroll_subjects,
        ];
    }
}
