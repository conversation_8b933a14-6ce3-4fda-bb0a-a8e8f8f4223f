<?php

namespace App\Http\Resources\Program;

use App\Http\Resources\FileMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowProgramResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'entity_id' => $this->entity_id,
            'program_based_on_id' => $this->program_based_on_id,
            'title' => $this->getTranslations('title'),
            'major' => count($this->getTranslations('major')) > 0 ? $this->getTranslations('major') : ['translate' => true],
            'objectives' => count($this->getTranslations('objectives')) > 0 ? $this->getTranslations('objectives') : ['translate' => true],
            'outcomes' => count($this->getTranslations('outcomes')) > 0 ? $this->getTranslations('outcomes') : ['translate' => true],
            'description' => count($this->getTranslations('description')) > 0 ? $this->getTranslations('description') : ['translate' => true],
            'entry_requirements' => count($this->getTranslations('entry_requirements')) > 0 ? $this->getTranslations('entry_requirements') : ['translate' => true],
            'certificate_awarded' => count($this->getTranslations('certificate_awarded')) > 0 ? $this->getTranslations('certificate_awarded') : ['translate' => true],
            'job_market' => count($this->getTranslations('job_market')) > 0 ? $this->getTranslations('job_market') : ['translate' => true],
            'type' => $this->type,
            'duration' => $this->duration,
            'duration_unit' => $this->duration_unit,
            'lang' => $this->lang,
            'weakly_teaching_hours' => $this->weakly_teaching_hours,
            'weakly_lab_hours' => $this->weakly_lab_hours,
            'general_credits' => $this->general_credits,
            'elective_credits' => $this->elective_credits,
            'compulsory_credits' => $this->compulsory_credits,
            'training_credits' => $this->training_credits,
            'supportive_credits' => $this->supportive_credits,
            'total_credits' => $this->total_credits,
            'is_active' => $this->is_active,
            'image' => optional($this->image)->getFullUrl(),
            'is_rewarded' => $this->is_rewarded,
            'booklet' => FileMediaResource::collection($this->getMedia('booklet'))
        ];
    }
}
