<?php

namespace App\Http\Resources\Program;

use App\Http\Resources\Entity\SimpleEntityResource;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Program */
class SimpleProgramResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'entity_id' => $this->entity_id,
            'type' => $this->type,
            'entity' => new SimpleEntityResource($this->whenLoaded('entity')),
        ];
    }
}
