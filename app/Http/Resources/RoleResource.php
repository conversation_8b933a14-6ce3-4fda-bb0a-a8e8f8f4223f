<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Subject */
class RoleResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->getTranslations('name'),
            'panel_name' => $this->panel_name,
            'level' => explode(',', $this->level),
            'permissions' => $this->relationLoaded('permissions') ? $this->permissions : [],
        ];
    }
}
