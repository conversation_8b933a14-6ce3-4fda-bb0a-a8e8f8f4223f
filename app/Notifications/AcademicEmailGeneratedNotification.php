<?php

namespace App\Notifications;

use App\Helpers\AcademicEmailsHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class AcademicEmailGeneratedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    public function viaQueues()
    {
        return [
            'mail' => 'emails',
        ];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('البريد الأكاديمي')
            ->from(config('mail.from.address'))
            ->line(new HtmlString('<h1>مرحبا ' . $notifiable->full_name . '</h1>'))
            ->line('تم إنشاء بريد أكاديمي خاص بك على جامعة غريان.')
            ->line('البيانات الخاصة بحسابك هي:')
            ->line(new HtmlString('<strong>البريد الإلكتروني</strong> : ' . $notifiable->secondary_email))
            ->line(new HtmlString('<strong>كلمة المرور</strong> : ' . AcademicEmailsHelper::generatePasswordFor($notifiable)))
            ->line('يرجى تسجيل دخولك إلى الحساب عن طريق الGmail ومن ثم تغيير كلمة المرور الخاصة بك');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
