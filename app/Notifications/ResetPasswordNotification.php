<?php

namespace App\Notifications;

use App\Helpers\UrlGenerator;
use App\Helpers\Utils;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class ResetPasswordNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function viaQueues()
    {
        return [
            'mail' => 'emails',
        ];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $token = Utils::generateToken(32);
        $notifiable->reset_password_token = $token;
        $notifiable->save();

        $urlGenerator = new UrlGenerator();
        $link = $urlGenerator->resetPasswordUrl($notifiable);

        return (new MailMessage)
            ->subject('إستعادة كلمة المرور')
            ->from(config('mail.from.address'))
            ->line(new HtmlString('<h1>مرحبا ' . $notifiable->full_name . '</h1>'))
            ->line('.تم إستلام طلب إستعادة كلمة المرور لحسابك')
            ->line('.بإمكانك زيارة هذا الرابط لتغيير كلمة المرور الخاصة بك')
            ->line(new HtmlString('<span style="color:red">ملاحظة: هذا الرابط صالح لمدة 12 ساعة من تاريخ إرساله.</span>'))
            ->action('تغيير كلمة المرور', $link);

    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
