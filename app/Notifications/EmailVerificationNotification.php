<?php

namespace App\Notifications;

use App\Helpers\UrlGenerator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class EmailVerificationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function viaQueues()
    {
        return [
            'mail' => 'emails',
        ];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $verifyEmail = new UrlGenerator();
        $link = $verifyEmail->verificationUrl($notifiable);
        return (new MailMessage)
            ->subject('تأكيد الحساب')
            ->from(config('mail.from.address'))
            ->line(new HtmlString('<h1>مرحبا ' . $notifiable->full_name . '</h1>'))
            ->line('هذا البريد تم ارساله للتأكد من أن البريد الإلكتروني الذي تم التسجيل بواسطته في جامعة غريان يعود لك.')
            ->line('يرجى تأكيد بريدك الإلكتروني عن طريق زيارة هذا الرابط')
            ->action('تأكيد الحساب', $link);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
