<?php

namespace App\Providers;

use App\Models\Lecturer;
use App\Models\User;
use App\Observers\LecturerObserver;
use App\Observers\UserObserver;
use Illuminate\Support\ServiceProvider;

class ObserverProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        User::observe(UserObserver::class);
        Lecturer::observe(LecturerObserver::class);
    }
}
