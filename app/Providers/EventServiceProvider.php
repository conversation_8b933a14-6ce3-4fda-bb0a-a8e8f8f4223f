<?php

namespace App\Providers;

use App\Events\PasswordResetRequested;
use App\Listeners\SaveAuditToDBListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use OwenIt\Auditing\Events\Auditing;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
//            UserCreatedListener::class
        ],
        PasswordResetRequested::class => [
//            SendResetPasswordEmail::class
        ],

        Auditing::class => [
            SaveAuditToDBListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
