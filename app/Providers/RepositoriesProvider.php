<?php

namespace App\Providers;

use App\Repositories\AcademicRanks\AcademicRankRepository;
use App\Repositories\AcademicRanks\AcademicRankRepositoryInterface;
use App\Repositories\Buildings\BuildingRepository;
use App\Repositories\Buildings\BuildingRepositoryInterface;
use App\Repositories\Campuses\CampusRepository;
use App\Repositories\Campuses\CampusRepositoryInterface;
use App\Repositories\Employees\EmployeeRepository;
use App\Repositories\Employees\EmployeeRepositoryInterface;
use App\Repositories\Entities\EntityRepository;
use App\Repositories\Entities\EntityRepositoryInterface;
use App\Repositories\EntityTypes\EntityTypeRepository;
use App\Repositories\EntityTypes\EntityTypeRepositoryInterface;
use App\Repositories\Halls\HallRepository;
use App\Repositories\Halls\HallRepositoryInterface;
use App\Repositories\Languages\LanguageRepository;
use App\Repositories\Languages\LanguageRepositoryInterface;
use App\Repositories\Lecturers\LecturerRepository;
use App\Repositories\Lecturers\LecturerRepositoryInterface;
use App\Repositories\Links\LinkRepository;
use App\Repositories\Links\LinkRepositoryInterface;
use App\Repositories\MartialStatuses\MartialStatusRepository;
use App\Repositories\MartialStatuses\MartialStatusRepositoryInterface;
use App\Repositories\Nationalities\NationalityRepository;
use App\Repositories\Nationalities\NationalityRepositoryInterface;
use App\Repositories\Permissions\PermissionRepository;
use App\Repositories\Permissions\PermissionRepositoryInterface;
use App\Repositories\Programs\ProgramRepository;
use App\Repositories\Programs\ProgramRepositoryInterface;
use App\Repositories\ProgramTypes\ProgramDurationUnitRepository;
use App\Repositories\ProgramTypes\ProgramDurationUnitRepositoryInterface;
use App\Repositories\ProgramTypes\ProgramTypeRepository;
use App\Repositories\ProgramTypes\ProgramTypeRepositoryInterface;
use App\Repositories\Qualifications\QualificationRepository;
use App\Repositories\Qualifications\QualificationRepositoryInterface;
use App\Repositories\Roles\RoleRepository;
use App\Repositories\Roles\RoleRepositoryInterface;
use App\Repositories\Subjects\SubjectRepository;
use App\Repositories\Subjects\SubjectRepositoryInterface;
use App\Repositories\SubjectTypes\SubjectTypeRepository;
use App\Repositories\SubjectTypes\SubjectTypeRepositoryInterface;
use App\Repositories\UserPermissions\UserPermissionRepository;
use App\Repositories\UserPermissions\UserPermissionRepositoryInterface;
use App\Repositories\UserRoles\UserRoleRepository;
use App\Repositories\UserRoles\UserRoleRepositoryInterface;
use App\Repositories\Users\UserRepository;
use App\Repositories\Users\UserRepositoryInterface;
use Illuminate\Support\ServiceProvider;

class RepositoriesProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(AcademicRankRepositoryInterface::class, fn() => new AcademicRankRepository());
        $this->app->singleton(EntityRepositoryInterface::class, fn() => new EntityRepository());
        $this->app->singleton(LecturerRepositoryInterface::class, fn() => new LecturerRepository());
        $this->app->singleton(LecturerRepositoryInterface::class, fn() => new LecturerRepository());
        $this->app->singleton(NationalityRepositoryInterface::class, fn() => new NationalityRepository());
        $this->app->singleton(PermissionRepositoryInterface::class, fn() => new PermissionRepository());
        $this->app->singleton(RoleRepositoryInterface::class, fn() => new RoleRepository());
        $this->app->singleton(QualificationRepositoryInterface::class, fn() => new QualificationRepository());
        $this->app->singleton(UserRepositoryInterface::class, fn() => new UserRepository());
        $this->app->singleton(ProgramRepositoryInterface::class, fn() => new ProgramRepository());
        $this->app->singleton(BuildingRepositoryInterface::class, fn() => new BuildingRepository());
        $this->app->singleton(HallRepositoryInterface::class, fn() => new HallRepository());
        $this->app->singleton(LanguageRepositoryInterface::class, fn() => new LanguageRepository());
        $this->app->singleton(HallRepositoryInterface::class, fn() => new HallRepository());
        $this->app->singleton(CampusRepositoryInterface::class, fn() => new CampusRepository());
        $this->app->singleton(ProgramTypeRepositoryInterface::class, fn() => new ProgramTypeRepository());
        $this->app->singleton(ProgramDurationUnitRepositoryInterface::class, fn() => new ProgramDurationUnitRepository());
        $this->app->singleton(SubjectRepositoryInterface::class, fn() => new SubjectRepository());
        $this->app->singleton(SubjectTypeRepositoryInterface::class, fn() => new SubjectTypeRepository());
        $this->app->singleton(MartialStatusRepositoryInterface::class, fn() => new MartialStatusRepository());
        $this->app->singleton(EntityTypeRepositoryInterface::class, fn() => new EntityTypeRepository());
        $this->app->singleton(UserPermissionRepositoryInterface::class, fn() => new UserPermissionRepository());
        $this->app->singleton(UserRoleRepositoryInterface::class, fn() => new UserRoleRepository());
        $this->app->singleton(LinkRepositoryInterface::class, fn() => new LinkRepository());
        $this->app->singleton(EmployeeRepositoryInterface::class, fn() => new EmployeeRepository());

    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
