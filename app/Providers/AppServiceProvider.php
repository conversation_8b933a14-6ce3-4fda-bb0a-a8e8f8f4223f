<?php

namespace App\Providers;

use App\Helpers\EntitiesHelper;
use App\Models\Entity;
use <PERSON>yvoy\CrossDatabase\Eloquent\Model;
use Illuminate\Database\QueryException;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\ServiceProvider;
use ReflectionException;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     * @throws ReflectionException
     */
    public function boot()
    {
        Paginator::useBootstrap();
        JsonResource::withoutWrapping();
        Model::unguard();

        $this->app->singleton('current_entity', function(){
            try {
                $currentEntityId = EntitiesHelper::getEntityId();
                return Entity::where('id', $currentEntityId)->first();
            } catch (QueryException $exception){

            }
        });

        $this->app->singleton('allEntities', function(){
            try {
                return Entity::get(['id', 'title', 'type', 'parent_entity_id']);
            } catch (QueryException $exception){

            }
        });

        Model::preventLazyLoading(! app()->isProduction());

    }
}
