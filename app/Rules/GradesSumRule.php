<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class GradesSumRule implements Rule
{
    public function __construct()
    {
        //
    }

    public function passes($attribute, $value): bool
    {
        foreach ($value as $v) {
            $mid_mark = $v['mid_mark'] ?? 0;
            $final_mark = $v['final_mark'] ?? 0;
            $practical_final_mark = $v['practical_final_mark'] ?? 0;

            if (($mid_mark + $final_mark + $practical_final_mark) > 100) {
                return false;
            }
        }

        return true;
    }

    public function message(): string
    {
        return 'يجب ألا يتجاوز مجموع الدرجات 100';
    }
}
