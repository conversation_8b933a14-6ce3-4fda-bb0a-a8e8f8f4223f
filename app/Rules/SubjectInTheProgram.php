<?php

namespace App\Rules;

use App\Models\ProgramBasedOn;
use App\Models\Subject;
use Illuminate\Contracts\Validation\Rule;

class SubjectInTheProgram implements Rule
{
    private $programId;

    /**
     * Create a new rule instance.
     *
     * @param $programId
     */
    public function __construct($programId)
    {
        $this->programId = $programId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $isSubjectInProgram = Subject::where('program_id', $this->programId)->where('id', $value)->exists();
        $isProgramDependsOnSubject = ProgramBasedOn::where('program_id', $this->programId)->where('subject_id', $value)->exists();
        if($isSubjectInProgram || $isProgramDependsOnSubject) {
            return true;
        }
        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The subject id is not in program and the program not based on the subject.';
    }
}
