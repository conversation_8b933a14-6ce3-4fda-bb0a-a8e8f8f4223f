<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class AllowedDomain implements Rule
{
    public $domain;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($domain)
    {
        $this->domain = $domain;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $parts = explode('@', $value);
        $domain = last($parts);

        return $domain === $this->domain;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The Email Is Not Valid.';
    }
}
