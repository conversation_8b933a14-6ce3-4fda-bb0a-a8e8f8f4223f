<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class EnglishChars implements Rule
{
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return preg_match('/(^([a-zA-Z \s]+)$)/u', $value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('validation.english');
    }
}
