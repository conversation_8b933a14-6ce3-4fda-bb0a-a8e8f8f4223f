<?php

namespace App\Rules;

use App\Models\Language;
use Illuminate\Contracts\Validation\Rule;

class JsonTranslationsArray implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $languages = Language::get()->pluck('id')->toArray();
        if (!is_array($value)) {
            return false;
        }

        $keys = [];
        foreach ($value as $key => $item) $keys[] = $key;

        return array_intersect($keys, $languages) == $keys;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute must be languages array.';
    }
}
