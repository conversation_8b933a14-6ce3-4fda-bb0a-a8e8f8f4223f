<?php


namespace App\Repositories\UserRoles;


use App\Models\User;
use App\Models\UsersPrivileges;

class UserRoleRepository implements UserRoleRepositoryInterface
{

    public function getRoles(User $user)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return $user->roles()->whereIn('entity_id', $entitiesIds)->get();
    }

    public function updateUserRoles($userId, $rolesIds, $entityId)
    {
        $user = User::findOrFail($userId);
        $user->syncRoles($rolesIds, $entityId);
    }
}
