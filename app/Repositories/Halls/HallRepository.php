<?php


namespace App\Repositories\Halls;


use App\Models\Building;
use App\Models\Hall;

class HallRepository implements HallRepositoryInterface
{

    public function getAll($buildingId)
    {
        return Building::with('halls')->find($buildingId);
    }

    public function create($hallData)
    {
        return Hall::create($hallData);
    }

    public function update($hallData, $id)
    {
        Hall::where('id', $id)->update($hallData);
    }

    public function delete($id)
    {
        Hall::destroy($id);
    }
}
