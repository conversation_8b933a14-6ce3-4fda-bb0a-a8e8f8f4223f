<?php


namespace App\Repositories\UserPermissions;


use App\Models\User;

class UserPermissionRepository implements UserPermissionRepositoryInterface
{

    public function getPermissionsOf(User $user)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return $user->permissions()->whereIn('entity_id', $entitiesIds)->get();
    }

    public function updateUserPermissions($userId, $permissionsIds, $entityId)
    {
        $user = User::findOrFail($userId);
        $user->syncPermissions($permissionsIds, $entityId);
    }
}
