<?php


namespace App\Repositories\Users;


use App\Models\User;

class UserRepository implements UserRepositoryInterface
{

    public function create(array $userData)
    {
        return User::create($userData);
    }

    public function update($id, $userData)
    {
        User::find($id)->update($userData);
    }

    public function show($id)
    {
        return User::find($id);
    }

    public function changeStatus($id, $status)
    {
        User::find($id)->update(['status' => $status]);
    }

    public function getSocialLinks($id)
    {
        return User::find($id)->socialLinks()->get();
    }

    public function updateSocialLinks($id, $links)
    {
        User::find($id)->socialLinks()->sync($links);
    }
}
