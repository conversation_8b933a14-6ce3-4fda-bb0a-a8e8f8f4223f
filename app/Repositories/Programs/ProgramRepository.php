<?php


namespace App\Repositories\Programs;


use App\Models\Entity;
use App\Models\Program;
use Modules\Website\DTOs\ProgramData;

class ProgramRepository implements ProgramRepositoryInterface
{
    public function getAll($type, $activeOnly)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return Program::with('programType', 'entity', 'durationUnit')
            ->when($activeOnly, fn ($q) => $q->where('is_active', true))
            ->when($type, fn ($q) => $q->where('type', $type))
            ->whereIn('entity_id', $entitiesIds)->get();
    }

    public function getPagination(ProgramData $programData)
    {
        $currentEntity = app('current_entity');
        $currentEntityTree = $currentEntity->childrenAndSelf();
        $currentEntitiesIds = $currentEntity->getIds($currentEntityTree);

        $programs = Program::query();

        if ($programData->search) {
            $programs->search($programData->search);
        }

        if ($programData->entityId) {
            $entity = Entity::findOrFail($programData->entityId);
            $entityTree = $entity->childrenAndSelf();
            $entitiesIds = $entity->getIds($entityTree);
            $programs->whereIn('entity_id', $entitiesIds);
        }

        return $programs
            ->with('entity:id,title', 'image')
            ->select(['id', 'title', 'entity_id'])
            ->whereIn('entity_id', $currentEntitiesIds)
            ->where('is_active', true)
            ->paginate($programData->perPage);
    }

    public function getRandom($numberOfElements)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return Program::with('programType:id,title', 'entity:id,title')
            ->whereIn('entity_id', $entitiesIds)
            ->inRandomOrder()
            ->limit($numberOfElements)
            ->get();
    }

    public function create($programData)
    {
        return Program::create($programData);
    }

    public function show($id)
    {
        return Program::with('image', 'booklet')->find($id);
    }

    public function update($programData, $id)
    {
        $program = Program::where('id', $id)->first();
        $program->update($programData);
        return $program;
    }

    public function delete($id)
    {
        Program::destroy($id);
    }
}
