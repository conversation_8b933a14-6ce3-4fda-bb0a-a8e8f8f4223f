<?php


namespace App\Repositories\Programs;


use Modules\Website\DTOs\ProgramData;

interface ProgramRepositoryInterface
{
    public function getAll($type, $activeOnly);

    public function getPagination(ProgramData $programData);

    public function create($programData);

    public function show($id);

    public function update($programData, $id);

    public function delete($id);

    public function getRandom($numberOfElements);
}
