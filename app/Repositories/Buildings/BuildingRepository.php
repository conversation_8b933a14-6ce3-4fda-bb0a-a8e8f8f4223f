<?php


namespace App\Repositories\Buildings;


use App\Models\Building;

class BuildingRepository implements BuildingRepositoryInterface
{

    public function getAll($withHalls = false, $onlyActiveHalls = false)
    {
        return Building::when($withHalls, fn($q) => $q->with([
            'halls' => fn($q) => $q->when(
                $onlyActiveHalls, fn($q) => $q->where('is_active', true)
            )
        ]))
            ->get();
    }

    public function create($buildingData)
    {
        return Building::create($buildingData);
    }

    public function update($buildingData, $id)
    {
        Building::where('id', $id)->update($buildingData);
    }

    public function delete($id)
    {
        Building::destroy($id);
    }
}
