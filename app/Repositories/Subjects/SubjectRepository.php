<?php


namespace App\Repositories\Subjects;


use App\Models\Program;
use App\Models\ProgramBasedOn;
use App\Models\Subject;

class SubjectRepository implements SubjectRepositoryInterface
{

    public function subjectsOfProgram($id, $isActive)
    {
        return Subject::where('program_id', $id)
            ->when($isActive, fn($q) => $q->where('is_active', '1'))
            ->get();
    }

    public function create($subjectData)
    {
        return Subject::create($subjectData);
    }

    public function savePrerequisites($id, $prerequisitesData)
    {
        Subject::find($id)->prerequisites()->sync($prerequisitesData);
    }

    public function show($id)
    {
        return Subject::with('prerequisites')->where('id', $id)->first();
    }

    public function update($subjectData, $id)
    {
        $subject = Subject::where('id', $id)->first();
        $subject->update($subjectData);
    }

    public function delete($id)
    {
        Subject::destroy($id);
    }


    // program based on subjects
    public function getBasedOnSubjectsOfProgram($id)
    {
        return Program::find($id)->basedOn()->get();
    }

    public function addBasedOnSubject(array $basedOnValidatedData)
    {
        return ProgramBasedOn::create($basedOnValidatedData);
    }

    public function showBasedOnSubject($programId, $programBasedOnCode)
    {
        return ProgramBasedOn::where('code', $programBasedOnCode)->where('program_id', $programId)->firstOrFail();
    }

    public function updateBasedOnSubjectCode($programId, $code, $newCode)
    {
        ProgramBasedOn::where('program_id', $programId)->where('code', $code)->update($newCode);
    }

    public function deleteBasedOnSubjectCode($programId, $programBasedOnCode)
    {
        ProgramBasedOn::where('program_id', $programId)->where('code', $programBasedOnCode)->delete();
    }
}
