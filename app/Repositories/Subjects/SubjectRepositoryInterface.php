<?php


namespace App\Repositories\Subjects;


interface SubjectRepositoryInterface
{

    public function create($subjectData);

    public function show($id);

    public function subjectsOfProgram($id, $isActive);

    public function update($subjectData, $id);

    public function delete($id);

    public function getBasedOnSubjectsOfProgram($id);

    public function addBasedOnSubject(array $basedOnValidatedData);

    public function showBasedOnSubject($programId, $programBasedOnCode);

    public function updateBasedOnSubjectCode($programId, $code, $newCode);

    public function deleteBasedOnSubjectCode($programId, $programBasedOnCode);

    public function savePrerequisites($id, $prerequisitesData);

}
