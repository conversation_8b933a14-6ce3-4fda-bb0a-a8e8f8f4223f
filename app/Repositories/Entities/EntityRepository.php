<?php


namespace App\Repositories\Entities;


use App\Models\Entity;

class EntityRepository implements EntityRepositoryInterface
{

    public function getAll()
    {
        return app('current_entity')->childrenAndSelf();
    }

    public function show($id)
    {
        return Entity::with('details', 'buildings', 'links', 'entityCover')->find($id);
    }

    public function create($entityData, $entityDetailsData, $entityBuildingsIds, $links)
    {
        $entity = $entity = Entity::create($entityData);
        $entity->details()->create($entityDetailsData);
        $entity->buildings()->sync($entityBuildingsIds);
        $entity->links()->sync($links);
        return $entity;
    }

    public function update($entityData, $entityDetailsArray, $entityBuildingsIds, $links, $id)
    {
        $entity = Entity::find($id);
        $entity->update($entityData);
        $entity->details()->updateOrCreate(['entity_id' => $entity->id], $entityDetailsArray);
        $entity->buildings()->sync($entityBuildingsIds);
        $entity->links()->sync($links);
        return $entity;
    }

    public function delete($id)
    {
        Entity::destroy($id);
    }
}
