<?php


namespace App\Repositories\Employees;


use App\Models\User;
use App\Repositories\Users\UserRepository;

class EmployeeRepository extends UserRepository implements EmployeeRepositoryInterface
{

    public function getAll()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return User::with('entity', 'employee')->whereIn('entity_id', $entitiesIds)->where('type', User::EMPLOYEE_TYPE)->get();
    }

    public function getPagination($elementsPerPage)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return User::with('entity', 'employee')->whereIn('entity_id', $entitiesIds)->where('type', User::EMPLOYEE_TYPE)->paginate($elementsPerPage);
    }

    public function create(array $userData)
    {
        $employeeDataFields = ['employee_id', 'hiring_date', 'hiring_decision_number', 'job_title', 'job_level', 'qualification_id'];
        $employeeData = collect($userData)->only($employeeDataFields);
        $userData = collect($userData)->except($employeeDataFields);

        $user = User::create($userData->toArray());
        $user->employee()->create($employeeData->toArray());
        return $user;
    }

    public function update($id, $userData)
    {
        $employeeDataFields = ['employee_id', 'hiring_date', 'hiring_decision_number', 'job_title', 'job_level', 'qualification_id'];
        $employeeData = collect($userData)->only($employeeDataFields);
        $userData = collect($userData)->except($employeeDataFields);

        $user = User::find($id);
        $user->update($userData->toArray());
        $user->employee()->updateOrCreate(['user_id' => $id], $employeeData->toArray());
        return $user;
    }

    public function delete($id)
    {
        User::destroy($id);
    }

    public function show($id)
    {
        return User::with('employee')->find($id);
    }
}
