<?php

namespace App\Repositories\Lecturers;

interface LecturerRepositoryInterface
{
    public function getAll($allEntities, $status);

    public function getPagination($elementsPerPage, $search, $allEntities, $onlyPermanent);

    public function getRandom($numberOfElements);

    public function create($user, $lecturerData);

    public function update($id, $lecturerData);

    public function delete($id);

    public function show($id);

    public function getPublicList();
}
