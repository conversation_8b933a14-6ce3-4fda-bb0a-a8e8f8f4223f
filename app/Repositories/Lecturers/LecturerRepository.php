<?php


namespace App\Repositories\Lecturers;


use App\Models\Lecturer;
use App\Models\User;

class LecturerRepository implements LecturerRepositoryInterface
{

    public function getAll($allEntities, $status)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        return Lecturer
            ::whereHas(
                'user',
                fn ($query) => $query->when(!$allEntities, fn ($q) => $q->whereIn('entity_id', $entitiesIds))
            )
            ->with('user')
            ->when($status, fn ($query) => $query->whereHas('user', fn ($q) => $q->where('status', $status)))
            ->get();
    }

    public function getPagination($elementsPerPage, $search, $allEntities, $onlyPermanent)
    {
        $lecturers = Lecturer::query();

        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $lecturers->whereHas('user', fn ($query) => $query->when(!$allEntities, fn ($q) => $q->whereIn('entity_id', $entitiesIds))->where('status', 1));
        if ($search) $lecturers->search($search);

        if ($onlyPermanent) $lecturers->where('type', Lecturer::PERMANENT_TYPE);

        $lecturers->with('user', 'user.avatar', 'user.entity', 'academicRank');

        return $lecturers->paginate($elementsPerPage);
    }

    public function getRandom($numberOfElements)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        return Lecturer::whereHas('user', fn ($query) => $query->whereIn('entity_id', $entitiesIds)->where('status', 1))
            ->with('user', 'user.avatar', 'user.entity', 'academicRank')
            ->where('type', Lecturer::PERMANENT_TYPE)
            ->limit($numberOfElements)
            ->get();
    }

    public function create($user, $lecturerData)
    {
        return $user->lecturer()->create($lecturerData);
    }

    public function delete($id)
    {
        Lecturer::destroy($id);
    }

    public function show($id)
    {
        return Lecturer::with('user')->find($id);
    }

    public function update($id, $lecturerData)
    {
        $lecturer = Lecturer::where('id', $id)->first();
        $lecturer->update($lecturerData);
        return $lecturer;
    }

    public function getPublicList()
    {
        return
            User::where('type', User::LECTURER_TYPE)
            ->orWhere('type', User::EMPLOYEE_TYPE)
            ->where('status', 1)
            ->get();
    }
}
