<?php


namespace App\Helpers\Files;

use App\Helpers\StorageHelper;
use Illuminate\Support\Facades\Storage;

class FileUploader implements FilesUploader
{
    public function upload($path, $file)
    {
        $fileName = $this->getFileName($file);
        $filePath = "{$path}/{$fileName}";
        $this->uploadToDisk($filePath, $file);
        return $fileName;
    }
    private function getFileName($file)
    {
        $name = md5(now() . $file->getClientOriginalName());
        $extension = $file->getClientOriginalExtension();
        return $name . '.' . $extension;
    }
    private function uploadToDisk($filePath, $file)
    {
        Storage::disk(StorageHelper::$disk)->put($filePath, file_get_contents($file));
    }
}
