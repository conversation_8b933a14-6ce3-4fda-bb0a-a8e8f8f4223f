<?php


namespace App\Helpers\Files;

use App\Helpers\StorageHelper;
use Illuminate\Http\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ImageUploader implements FilesUploader
{
    public function upload($path, $image)
    {
        $filePath = "{$path}";
        return $this->uploadToDisk($filePath, $image);
    }

    private function uploadToDisk($filePath, $image)
    {
        return Storage::disk(StorageHelper::$disk)->put($filePath, $image);
    }


    public function generateWebpImage($file, $outputFile)
    {
        switch ($file->getClientOriginalExtension()) {
            case 'jpeg':
            case 'jpg':
                $image = imagecreatefromjpeg($file);
                break;

            case 'png':
                $image = imagecreatefrompng($file);
                imagepalettetotruecolor($image);
                imagealphablending($image, true);
                imagesavealpha($image, true);
                break;

            case 'gif':
                $image = imagecreatefromgif($file);
                break;

            default:
                return false;
        }

        $result = imagewebp($image, $outputFile);

        // Free up memory
        imagedestroy($image);

        return $result;
    }

    public function getUploadedFileObject(string $outputFile)
    {
        $base64 = base64_encode(file_get_contents($outputFile));

        // decode the base64 file
        $fileData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));

        $tmpFilePath = $outputFile;
        file_put_contents($tmpFilePath, $fileData);

        $tmpFile = new File($tmpFilePath);

        return new UploadedFile(
            $tmpFile->getPathname(),
            $tmpFile->getFilename(),
            $tmpFile->getMimeType(),
            0,
            true // Mark it as test, since the file isn't from real HTTP POST.
        );
    }
}
