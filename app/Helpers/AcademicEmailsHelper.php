<?php


namespace App\Helpers;


use App\Models\User;
use Carbon\Carbon;

class AcademicEmailsHelper
{

    public static function generatePasswordFor($user)
    {
        try {
            $password = mb_strtoupper($user->first_name_en[0]);
            $password .= mb_strtolower($user->last_name_en[0]);
            $date = Carbon::parse($user->dob);
            $day = $date->day;
            $month = $date->month;
            $year = $date->year;
            $password .= $day . $month . $year;

            return $password;
        } catch (\Throwable $ex) {
            dd($user->id);
        }
    }

    public static function generateEmailFor($user)
    {
        $first_name = mb_strtolower($user->first_name_en);
        $last_name = mb_strtolower($user->last_name_en);
        $index = 0;
        $firstNamePart = $first_name[$index];
        $email = $firstNamePart . ".$last_name" ."<EMAIL>";
        $email = str_replace(' ', '-', $email);
        while(User::where('secondary_email', $email)->exists()) {
            $index++;
            $firstNamePart .= $first_name[$index];
            $email = $firstNamePart . ".$last_name" . "<EMAIL>";
        }
        return $email;

    }
}
