<?php

namespace App\Helpers;

use DB;
use Exception;

class XATransaction
{

    public static function openTransaction($connection, $callback)
    {
        $xa = uniqid();
        DB::connection($connection)->raw("XA PREPARE $xa");
        try {
            $result = $callback();
        } catch (Exception $e) {
            $result = null;
        }
        DB::connection($connection)->raw("XA END '$xa'");
        if ($result) {
            DB::connection($connection)->raw("XA PREPARE $xa");
        }
        return [$xa, $result];
    }

    public static function commitTransaction($connection, $xa)
    {
        DB::connection($connection)->raw("XA COMMIT '$xa'");
    }

    public static function rollbackMultipleTransactions($xas)
    {
        foreach ($xas as $xa) {
            self::rollbackTransaction($xa['connection'], $xa['xa']);
        }
    }

    public static function rollbackTransaction($connection, $xa)
    {
        DB::connection($connection)->raw("XA ROLLBACK '$xa'");
    }

}
