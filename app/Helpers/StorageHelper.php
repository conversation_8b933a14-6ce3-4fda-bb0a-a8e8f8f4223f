<?php


namespace App\Helpers;


use App\Helpers\Files\FileUploader;
use App\Helpers\Files\ImageUploader;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\FileAdder;

class StorageHelper
{
    public static $disk = 'public';

    //Helper Functions
    /**
     * @param $path
     * @param $file
     * @return string $fileName
     */
    public static function uploadFile($path, $file): string
    {
        if(self::is_image($file)) {
            $fileName = (new ImageUploader)->upload($path, $file);
        } else {
            $fileName = (new FileUploader())->upload($path, $file);
        }

        return $fileName;
    }

    public static function getUrlOf($path)
    {
        return Storage::disk(self::$disk)->url($path);
    }

    private static function is_image($file) :bool {
        return Str::startsWith($file->getMimeType(), 'image/');
    }

    // Media Library Package
    public static function upload($model, UploadedFile $file, $collection = 'default')
    {
        if(self::is_image($file)) {

            $imageUploader = new ImageUploader();

            $outputFile = $file->getPath() . '/' . pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME) . '.webp';

            if($imageUploader->generateWebpImage($file, $outputFile)) {
                $file = $imageUploader->getUploadedFileObject($outputFile);
            }

        }

        return $model
            ->addMedia($file)
            ->withResponsiveImages()
            ->toMediaCollection($collection);
    }

    public static function uploadMultipleFromRequest($model, $requestName)
    {

        $i = 0;
        $files = array_values(request()->file($requestName));

        $model->addMultipleMediaFromRequest([$requestName])
            ->each(function (FileAdder $fileAdder) use ($files, &$i) {
                $file = $files[$i++];

                if(self::is_image($file)) {

                    $imageUploader = new ImageUploader();

                    $outputFile = $file->getPath() . '/' . pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME) . '.webp';

                    if($imageUploader->generateWebpImage($file, $outputFile)) {
                        $file = $imageUploader->getUploadedFileObject($outputFile);
                    }

                }

                $fileAdder
                    ->setFile($file)
                    ->withResponsiveImages()
                    ->toMediaCollection();
            });
    }

    public static function deleteMedia($model, $ids)
    {
        $model->media()->whereIn('id', $ids)->delete();
    }

}
