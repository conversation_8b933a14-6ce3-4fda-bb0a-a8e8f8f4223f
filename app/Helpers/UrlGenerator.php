<?php


namespace App\Helpers;

use Illuminate\Support\Facades\URL;

class UrlGenerator
{
    /**
     * Get the verification URL for the given notifiable.
     *
     * @param mixed $user
     * @return string
     */
    public function verificationUrl($user)
    {
        $prefix = config('frontend.url') . config('frontend.email_verify_url');
        $temporarySignedURL = URL::signedRoute('verification.verify', ['user' => $user]);
        // I use urlencode to pass a link to my frontend.
        return $prefix . urlencode($temporarySignedURL);
    }
    /**
     * Get the verification URL for the given notifiable.
     *
     * @param mixed $user
     * @return string
     */
    public function resetPasswordUrl($user)
    {
        $prefix = config('frontend.url') . config('frontend.reset_password_url');
        $temporarySignedURL = URL::temporarySignedRoute('reset.verify', now()->addHours(12), [
            'email' => $user->email,
            'token' => $user->reset_password_token
            ]
        );
        // I use urlencode to pass a link to my frontend.
        return $prefix . urlencode($temporarySignedURL);
    }

    /**
     * Get the verification URL for the given notifiable.
     *
     * @param $student
     * @return string
     */
    public function applicationVerificationUrl($student)
    {
        $prefix = config('frontend.url') . config('frontend.application_email_verify_url');
        $temporarySignedURL = URL::signedRoute('application.verify', [
            'student' => $student->getKey(),
        ]);
        // I use urlencode to pass a link to my frontend.
        return $prefix . urlencode($temporarySignedURL);
    }
    /**
     * Get the verification URL for the given notifiable.
     *
     * @param $student
     * @return string
     */
    public function studentApplicationDownloadReceiptUrl($student)
    {
        return URL::signedRoute('application.download-receipt', ['student' => $student->getKey()]);
    }
}
