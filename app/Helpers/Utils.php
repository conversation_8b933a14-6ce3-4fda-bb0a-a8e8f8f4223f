<?php


namespace App\Helpers;


use App\Models\Entity;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Jackiedo\DotenvEditor\Facades\DotenvEditor;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\TermDetails;

class Utils
{
    /**
     * @param int $length
     * @return string
     * @throws Exception
     */
    public static function generateToken($length = 64)
    {
        return hash_hmac('sha256', Str::random(40), config('app.key'));
    }

    public static function fixDomains()
    {
        $envDomains = self::getEnvDomains();
        $databaseDomains = Entity::get()->pluck('id');

        $envOnlyDomains = (array_diff(
            $envDomains->toArray(),
            $databaseDomains->toArray()
        ));

        $databaseOnlyDomains = array_diff(
            $databaseDomains->toArray(),
            $envDomains->toArray()
        );

        //fixing domains
        collect($envOnlyDomains)->each(function ($domain) {
            self::removeDomain($domain);
        });

        collect($databaseOnlyDomains)->each(function ($domain) {
            self::addDomain($domain);
        });
    }

    private static function getEnvDomains(): Collection
    {
        $domains = collect(explode(',', DotenvEditor::getValue('SANCTUM_STATEFUL_DOMAINS')));
        $mainDomain = config('app.url');
        $mainDomain = Utils::removeHttp($mainDomain);
        $domainPartsCount = count(explode('.', $mainDomain));
        $domains->each(function ($domain, $index) use ($domainPartsCount, &$domains) {
            //means not main domain
            if ($index != 0) {
                $domainArray = explode('.', $domain);
                $arrayLength = count($domainArray);
                $tempDomain = '';
                for ($i = 0; $i < $arrayLength - $domainPartsCount; $i++) {
                    $tempDomain .= $domainArray[$i] . '.';
                }
                $tempDomain = rtrim($tempDomain, '.');
                $domains[$index] = $tempDomain;
            } else {
                $domains[0] = explode('.', $domain)[0];
            }
        });
        return $domains;
    }

    public static function removeHttp($url)
    {
        $disallowed = array('http://', 'https://');
        foreach ($disallowed as $d) {
            if (strpos($url, $d) == 0) {
                $url = str_replace($d, '', $url);
            }
        }
        return $url;
    }

    //usage for tinker only

    /**
     * @param $domain
     * without the university main domain
     * remove domain from Sanctum domains if exists
     */
    public static function removeDomain($domain)
    {
        $appUrl = Utils::removeHttp(config('app.url'));
        $domain = $domain . '.' . $appUrl;
        $domains = collect(explode(',', DotenvEditor::getValue('SANCTUM_STATEFUL_DOMAINS')));
        $domains->each(function ($oldDomain, $index) use ($domains, $domain) {
            if ($oldDomain == $domain) {
                $domains->forget($index);
            }
        });
        $strDomains = implode(',', $domains->toArray());
        DotenvEditor::setKey('SANCTUM_STATEFUL_DOMAINS', $strDomains);
        DotenvEditor::save();
        Artisan::call('config:clear');
    }

    /**
     * @param $domain
     * without the university main domain
     *  add domain to Sanctum domains if not exists
     */
    public static function addDomain($domain)
    {
        $appUrl = Utils::removeHttp(config('app.url'));
        $domain = $domain . '.' . $appUrl;
        $oldDomains = DotenvEditor::getValue('SANCTUM_STATEFUL_DOMAINS');
        $oldDomainsArray = explode(',', $oldDomains);
        if (in_array($domain, $oldDomainsArray)) {
            return;
        }

        $newDomains = $oldDomains . ',' . $domain;
        DotenvEditor::setKey('SANCTUM_STATEFUL_DOMAINS', $newDomains);
        DotenvEditor::save();
        Artisan::call('config:clear');
    }

    public static function isStageEnabled($stageId)
    {
        $currentEntity = app('current_entity');
        $activeTerm = $currentEntity->terms()->active()->first();

        if (!$activeTerm) return false;

        $stage = $activeTerm->stages()->where('id', $stageId)->first();

        if (!$stage) return false;

        if (auth()->user()?->hasRole('general registrar', $currentEntity->id)) return true;

        if ($stage->pivot->status == TermDetails::SUSPENDED_STATUS) {
            return false;
        }

        if ($stage->pivot->status == TermDetails::ACTIVE_STATUS) {
            return true;
        }

        $startAt = $stage->pivot->start_at;
        $endAt = $stage->pivot->end_at;

        if (Carbon::parse($startAt)->gt(now()) || Carbon::parse($endAt)->lt(now())) {
            return false;
        }

        return true;
    }
}
