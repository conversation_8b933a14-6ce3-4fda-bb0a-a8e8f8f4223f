<?php


namespace App\Helpers;

use Illuminate\Support\Str;

class EntitiesHelper
{
    public static function getEntityId()
    {
        $url = request()->header('origin') ?? url()->current() ?? request()->header('referer');

        $appUrl = Utils::removeHttp(config('app.url'));
        $url = Utils::removeHttp($url);

        $url = explode('/', $url)[0];

        if (Str::startsWith($appUrl, $url)) {
            return explode('.', $url)[0];
        }

        return explode('.' . $appUrl, $url)[0];
    }

    public static function generateFullId($newEntityId)
    {
        $entityId = self::getEntityId();
        $appUrl = Utils::removeHttp(config('app.url'));
        if ($entityId == explode('.', $appUrl)[0]) {
            return $newEntityId;
        }
        return $newEntityId . '.' . $entityId;
    }

}
