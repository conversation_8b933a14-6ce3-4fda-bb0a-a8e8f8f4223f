<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendMailsToSecondEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var User
     */
    public $user;
    /**
     * @var Mailable
     */
    public $mailable;

    /**
     * Create a new job instance.
     *
     * @param User $user
     * @param Mailable $mailable
     */
    public function __construct($user, Mailable $mailable)
    {
        $this->user = $user;
        $this->mailable = $mailable;
        $this->queue = 'emails';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Mail::to($this->user->secondary_email)->send($this->mailable);
    }
}
