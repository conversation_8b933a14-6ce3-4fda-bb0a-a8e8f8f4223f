<?php

namespace App\Observers;

use Modules\Sms\Entities\Student;

class StudentObserver
{
    /**
     * Handle the student "created" event.
     *
     * @param $student
     * @return void
     */
    public function created(Student $student)
    {
        //
    }

    /**
     * Handle the student "updated" event.
     *
     * @param    $student
     * @return void
     */
    public function updated(Student $student)
    {
        //
    }

    /**
     * Handle the student "deleted" event.
     *
     * @param  $student
     * @return void
     */
    public function deleted(Student $student)
    {
        $student->user()->delete();
    }

    /**
     * Handle the student "restored" event.
     *
     * @param    $student
     * @return void
     */
    public function restored(Student $student)
    {
        //
    }

    /**
     * Handle the student "force deleted" event.
     *
     * @param    $student
     * @return void
     */
    public function forceDeleted(Student $student)
    {
        //
    }
}
