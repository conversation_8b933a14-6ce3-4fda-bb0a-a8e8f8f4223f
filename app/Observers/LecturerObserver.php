<?php

namespace App\Observers;

use App\Models\Lecturer;

class LecturerObserver
{
    /**
     * Handle the lecturer "created" event.
     *
     * @param Lecturer $lecturer
     * @return void
     */
    public function created(Lecturer $lecturer)
    {
        //
    }

    /**
     * Handle the lecturer "updated" event.
     *
     * @param Lecturer $lecturer
     * @return void
     */
    public function updated(Lecturer $lecturer)
    {
        //
    }

    /**
     * Handle the lecturer "deleted" event.
     *
     * @param Lecturer $lecturer
     * @return void
     */
    public function deleted(Lecturer $lecturer)
    {
        $lecturer->user()->delete();
    }

    /**
     * Handle the lecturer "restored" event.
     *
     * @param Lecturer $lecturer
     * @return void
     */
    public function restored(Lecturer $lecturer)
    {
        //
    }

    /**
     * Handle the lecturer "force deleted" event.
     *
     * @param Lecturer $lecturer
     * @return void
     */
    public function forceDeleted(Lecturer $lecturer)
    {
        //
    }
}
