{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "vcs", "url": "https://github.com/Benjaber-98/eloquent-sluggable"}, {"type": "vcs", "url": "https://github.com/Benjaber-98/slugify"}, {"type": "vcs", "url": "https://github.com/Benjaber-98/laravel-sluggable"}, {"type": "vcs", "url": "https://github.com/Benjaber-98/laravel-medialibrary"}], "require": {"php": "^8.0", "ext-json": "*", "ext-pcntl": "*", "artesaos/seotools": "^0.20.1", "awobaz/compoships": "^2.1", "benjaber-98/laravel-https": "^1.0", "benjaber-98/laravel-multitenancy-permission": "9999999-dev", "bugsnag/bugsnag-laravel": "^2.22", "cocur/slugify": "dev-master", "doctrine/dbal": "^3.8", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0.4", "genealabs/laravel-model-caching": "*", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^7.0.1", "hoyvoy/laravel-cross-database-subqueries": "^8.0", "jackiedo/dotenv-editor": "1.*", "jenssegers/date": "^4.0", "laravel/framework": "^8.0", "laravel/horizon": "^5.0", "laravel/sanctum": "^2.2", "laravel/tinker": "^2.0", "niklasravnsborg/laravel-pdf": "^4.1", "nwidart/laravel-modules": "^8.2.0", "owen-it/laravel-auditing": "^12.0.0", "predis/predis": "^1.1", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/data-transfer-object": "^3.1.1", "spatie/laravel-backup": "^7.5", "spatie/laravel-medialibrary": "dev-dev-add-webp-responsive-images as 9.6.2", "spatie/laravel-sluggable": "dev-master", "spatie/laravel-translatable": "^5.0.0", "spatie/simple-excel": "^1.10"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "barryvdh/laravel-ide-helper": "^2.8", "facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.2", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0", "spatie/laravel-web-tinker": "^1.7"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false, "php": "8.0"}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeds/"}, "classmap": []}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}