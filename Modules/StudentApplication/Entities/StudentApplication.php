<?php

namespace Modules\StudentApplication\Entities;

use App\Models\Entity;
use App\Models\MartialStatus;
use App\Models\Nationality;
use App\Models\Program;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;


class StudentApplication extends Model implements MustVerifyEmail, Auditable
{
    use \Illuminate\Auth\MustVerifyEmail, \OwenIt\Auditing\Auditable;

    protected $hidden = ['created_at', 'updated_at'];
    protected $table = 'student_applications';
    protected $with = ['program', 'entity'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('national_id', 'LIKE', "$search%")
            ->orWhere(DB::raw('concat(first_name_ar," ",second_name_ar, " ", third_name_ar, " ", last_name_ar)'), 'LIKE', "%$search%")
            ->orWhere(DB::raw('concat(first_name_en," ",second_name_en, " ", third_name_en, " ", last_name_en)'), 'LIKE', "%$search%")
            ->orWhere('email', 'LIKE', "%$search%")
            ->orWhere('registration_number', 'LIKE', "$search%");
    }

    public function scopeDefault($query)
    {
        return $query->where('status', 0);
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 1);
    }

    public function scopeDeclined($query)
    {
        return $query->where('status', 2);
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 3);
    }

    public function scopeStudentIdIssued($query)
    {
        return $query->where('status', 4);
    }

    public function getStatusAttribute($status)
    {
        switch ($status) {
            case 0:
                return 'default';
            case 1:
                return 'confirmed';
            case 2:
                return 'declined';
            case 3:
                return 'accepted';
            case 4:
                return 'student_id_issued';
        }
    }

    public function setStatusAttribute($status)
    {
        switch (strtolower($status)) {
            case 'default':
                $this->attributes['status'] = 0;
                break;
            case 'confirmed':
                $this->attributes['status'] = 1;
                break;
            case 'declined':
                $this->attributes['status'] = 2;
                break;
            case 'accepted':
                $this->attributes['status'] = 3;
                break;
            case 'student_id_issued':
                $this->attributes['status'] = 4;
                break;
        }
    }

    public function setGenderAttribute($gender)
    {
        switch (strtolower($gender)) {
            case 'male':
                $this->attributes['gender'] = 1;
                break;
            case 'female':
                $this->attributes['gender'] = 2;
                break;
        }
    }

    public function getGenderAttribute($type)
    {
        switch ($type) {
            case 1:
                return 'male';
            case 2:
                return 'female';
        }
    }

    public function getFullName($lang = 'ar')
    {
        $firstName = 'first_name_' . $lang;
        $secondName = 'second_name_' . $lang;
        $thirdName = 'third_name_' . $lang;
        $lastName = 'last_name_' . $lang;
        return $this->$firstName . ' ' . $this->$secondName . ' ' . $this->$thirdName . ' ' . $this->$lastName;
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function nationality()
    {
        return $this->belongsTo(Nationality::class);
    }

    public function martialStatus()
    {
        return $this->belongsTo(MartialStatus::class, 'martial_status');
    }
}
