<?php

namespace Modules\StudentApplication\Transformers\TempStudent;

use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \Modules\StudentApplication\Entities\StudentApplication */
class StudentApplicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'first_name_ar' => $this->first_name_ar,
            'second_name_ar' => $this->second_name_ar,
            'third_name_ar' => $this->third_name_ar,
            'last_name_ar' => $this->last_name_ar,
            'first_name_en' => $this->first_name_en,
            'second_name_en' => $this->second_name_en,
            'third_name_en' => $this->third_name_en,
            'last_name_en' => $this->last_name_en,
            'sponsor' => $this->sponsor,
            'dob' => $this->dob,
            'national_id' => $this->national_id,
            'passport_number' => $this->passport_number,
            'gender' => $this->gender,
            'nationality_id' => $this->nationality_id,
            'status' => $this->status,
            'address' => $this->address,
            'mother_name' => $this->mother_name,
            'birth_place' => $this->birth_place,
            'work_place' => $this->work_place,
            'phone_number' => $this->phone_number,
            'martial_status' => $this->martial_status,
            'email' => $this->email,
            'entity_id' => $this->entity_id,
            'program_id' => $this->program_id,
            'registration_number' => $this->registration_number,
            'qualification_id' => $this->qualification_id,
            'qualification_university' => $this->qualification_university,
            'general_major' => $this->general_major,
            'specialization' => $this->specialization,
            'school_name' => $this->school_name,
            'department' => $this->department,
            'year_of_study' => $this->year_of_study,
            'seat_number' => $this->seat_number,
            'gpa' => $this->gpa,
            'enrollment_type' => $this->enrollment_type,
        ];
    }
}
