<?php


namespace Modules\StudentApplication\Repositories\TempStudents;


use Modules\StudentApplication\DTOs\ApplicationData;
use Modules\StudentApplication\Entities\StudentApplication;

interface StudentApplicationRepositoryInterface
{
    public function register($studentData);

    public function getApplicationsInEntities($entitiesIds, ApplicationData $data);

    public function changeStatus(StudentApplication $student, $status);
}
