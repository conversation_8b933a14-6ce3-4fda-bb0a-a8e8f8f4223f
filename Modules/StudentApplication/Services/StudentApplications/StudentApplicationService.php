<?php


namespace Modules\StudentApplication\Services\StudentApplications;

use App\Helpers\UrlGenerator;
use App\Helpers\XATransaction;
use App\Jobs\SendMailsJob;
use App\Jobs\SendMailsToSecondEmailJob;
use App\Mail\SendApplicationVerificationEmail;
use App\Mail\SendRegistrationReceipt;
use App\Repositories\Users\UserRepositoryInterface;
use Exception;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentTerm;
use Modules\Sms\Entities\Term;
use Modules\Sms\Repositories\Students\StudentRepositoryInterface;
use Modules\StudentApplication\DTOs\ApplicationData;
use Modules\StudentApplication\Emails\SendStudentIdMail;
use Modules\StudentApplication\Entities\StudentApplication;
use Modules\StudentApplication\Events\NewStudentCreated;
use Modules\StudentApplication\Http\Requests\StudentApplications\ApplicationRequest;
use Modules\StudentApplication\Http\Requests\StudentApplications\ChangeApplicationRequest;
use Modules\StudentApplication\Repositories\TempStudents\StudentApplicationRepositoryInterface;

class StudentApplicationService
{
    /**
     * @var StudentApplicationRepositoryInterface
     */
    private $applicationRepository;

    /**
     * TempStudentService constructor.
     * @param StudentApplicationRepositoryInterface $applicationRepository
     */
    public function __construct(StudentApplicationRepositoryInterface $applicationRepository)
    {
        $this->applicationRepository = $applicationRepository;
    }

    /**
     * @param ApplicationRequest $request
     */
    public function register(ApplicationRequest $request)
    {
        $studentData = collect($request->validated());
        $registrationNumber = Carbon::now()->timestamp + rand(1000, 999999999);
        $studentData->put('registration_number', $registrationNumber);
        $student = $this->applicationRepository->register($studentData->toArray());
        event(new NewStudentCreated($student));
        return ['message' => 'success', 'student' => $student];
    }

    public function sendVerificationCode(MustVerifyEmail $student)
    {
        $verifyEmail = new UrlGenerator();
        $link = $verifyEmail->applicationVerificationUrl($student);
        $mailable = new SendApplicationVerificationEmail($student, $link);
        SendMailsJob::dispatch($student, $mailable);
    }

    public function getAll(ApplicationData $data)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        return $this->applicationRepository->getApplicationsInEntities($entitiesIds, $data);
    }

    public function sendDownloadLink(StudentApplication $student)
    {
        $urlGenerator = new UrlGenerator();
        $downloadUrl = $urlGenerator->studentApplicationDownloadReceiptUrl($student);

        $mailable = new SendRegistrationReceipt($student, $downloadUrl);
        SendMailsJob::dispatch($student, $mailable);
        return ['message' => 'success', 'url' => $downloadUrl];
    }

    public function forwardToMainTables(StudentApplication $application)
    {
        $userRepository = app(UserRepositoryInterface::class);
        $studentRepository = app(StudentRepositoryInterface::class);

        // $term = Term::active()->firstOrFail();

        $userData = $this->getUserData($application);

        $studentData = $this->getStudentData($application);

        $studentId = rand(11111, 999999); //$this->getLastAvailableStudentId($term);

        $studentData['id'] = $studentId;

        try {

            $xa = [];

            list($userXA, $user) = XATransaction::openTransaction(config('database.default'), function () use ($userData, $userRepository) {
                return $userRepository->create($userData);
            });

            if (!$user) {
                $xa[] = [
                    'connection' => config('database.default'),
                    'xa' => $userXA
                ];
                throw new Exception('failed');
            }

            $studentData['user_id'] = $user->id;
            list($studentXA, $student) = XATransaction::openTransaction(config('database.sms_connection'), function () use ($user, $studentRepository, $application, $studentData) {
                $application->update(['student_id' => $studentData['id'], 'status' => 'student_id_issued']);
                $student = $studentRepository->create($user, $studentData);

                $department = $user->entity()->first();

                $term = Term::where('entity_id', $department->parent_entity_id)->whereType($student->program->duration_unit)->active()->first();
                if ($term) {
                    $student->terms()->attach([
                        $term->id => [
                            'status' => StudentTerm::DEFAULT_STATUS
                        ]
                    ]);
                }
                return $student;
            });

            if (!$student) {
                $xa[] = [
                    'connection' => config('database.sms_connection'),
                    'xa' => $studentXA
                ];
                throw new Exception('failed');
            }

            list($userEmailXA, $updateUserEmail) = XATransaction::openTransaction(config('database.default'), function () use ($user, $application) {
                return $user->update(['email' => $application->student_id . '@academy.edu.ly', 'secondary_email' => $user->email]);
            });

            if (!$updateUserEmail) {
                $xa[] = [
                    'connection' => config('database.default'),
                    'xa' => $userEmailXA
                ];
                throw new Exception('failed');
            }

            XATransaction::commitTransaction(config('database.default'), $userXA);
            XATransaction::commitTransaction(config('database.sms_connection'), $studentXA);
            XATransaction::commitTransaction(config('database.default'), $userEmailXA);

            $student->refresh();
            $student->load('user.entity.parent');

            $mailable = new SendStudentIdMail($student);
            SendMailsToSecondEmailJob::dispatch($user, $mailable);

            return ['message' => 'success', 'code' => 200, 'id' => $student->id];
        } catch (Exception $exception) {
            Log::channel('bugsnag')->error($exception->getMessage());
            XATransaction::rollbackMultipleTransactions($xa);
            return ['message' => 'failed', 'exception' => $exception->getMessage()];
        }
    }


    private function getUserData(StudentApplication $student): array
    {
        return [
            'first_name_ar' => $student->first_name_ar,
            'second_name_ar' => $student->second_name_ar,
            'third_name_ar' => $student->third_name_ar,
            'last_name_ar' => $student->last_name_ar,
            'first_name_en' => $student->first_name_en,
            'second_name_en' => $student->second_name_en,
            'third_name_en' => $student->third_name_en,
            'last_name_en' => $student->last_name_en,
            'address' => $student->address,
            'dob' => $student->dob,
            'national_id' => $student->national_id,
            'passport_number' => $student->passport_number,
            'personal_id' => $student->personal_id,
            'gender' => $student->gender,
            'nationality_id' => $student->nationality_id,
            'entity_id' => $student->entity_id,
            'phone_number' => $student->phone_number,
            'email' => $student->email,
            'email_verified_at' => now(),
            'password' => Hash::make(Carbon::createFromFormat('Y-m-d', $student->dob)->format('Ymd')),
            'type' => 'student',
            'status' => 'active'
        ];
    }


    private function getStudentData(StudentApplication $student): array
    {
        return [
            'program_id' => $student->program_id,
            'mother_name' => $student->mother_name,
            'birth_place' => $student->birth_place,
            'work_place' => $student->work_place,
            'martial_status' => $student->martial_status,
            'sponsor' => $student->sponsor,
            'enrollment_type' => $student->enrollment_type
        ];
    }

    private function getLastAvailableStudentId(Term $term)
    {
        $lastStudent = Student::where('id', 'like', $term->prefix . '%')
            ->whereRaw('LENGTH(id) > 5')
            ->orderByDesc('id')->first();
        if (!$lastStudent) {
            return intval($term->prefix . '00001');
        }

        return $lastStudent->id + 1;
    }

    public function changeStatus($id, ChangeApplicationRequest $request)
    {
        $this->applicationRepository->changeStatus($id, $request->get('status'));
        return ['message' => 'success'];
    }
}
