<?php

namespace Modules\StudentApplication\Http\Controllers;

use App\Http\Resources\Program\SimpleProgramResource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;
use Modules\StudentApplication\Entities\AcademicYear;

class AcademicYearProgramController extends Controller
{

    public function index(AcademicYear $academicYear)
    {

        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $academicYearPrograms = $academicYear->programs()
        ->whereIn('entity_id', $entitiesIds)
        ->get();

        return $academicYearPrograms->map(function ($academicYearProgram) {
            return [
                'program_id' => $academicYearProgram->pivot->program_id,
            ];
        });
    }

    public function update(AcademicYear $academicYear, Request $request)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $request->validate([
            'programs' => ['sometimes', 'array', 'min:0'],
            'programs.*' => ['required', 'integer', Rule::exists(config('database.default') . '.programs', 'id')->whereIn('entity_id', $entitiesIds)],
        ]);

        $academicYear->academicYearPrograms()->whereHas('program', function ($query) use ($entitiesIds) {
            $query->whereIn('entity_id', $entitiesIds);
        })
        ->delete();

        $academicYear->academicYearPrograms()->createMany(
            collect($request->programs)->map(function ($programId) {
                return [
                    'program_id' => $programId,
                ];
            })->toArray()
        );

        return response()->noContent();
    }
}
