<?php

namespace Modules\StudentApplication\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use DB;
use Exception;
use Modules\Sms\Entities\Term;
use Modules\StudentApplication\DTOs\ApplicationData;
use Modules\StudentApplication\Entities\ApplicationArchive;
use Modules\StudentApplication\Entities\StudentApplication;
use Modules\StudentApplication\Http\Requests\StudentApplications\ChangeApplicationRequest;
use Modules\StudentApplication\Http\Requests\StudentApplications\ApplicationRequest;
use Modules\StudentApplication\Http\Requests\StudentApplications\IndexApplicationsRequest;
use Modules\StudentApplication\Http\Requests\StudentApplications\UpdateApplicationRequest;
use Modules\StudentApplication\Services\StudentApplications\StudentApplicationService;
use Modules\StudentApplication\Transformers\TempStudent\StudentApplicationResource;

class StudentApplicationController extends Controller
{
    private $applicationService;

    public function __construct(StudentApplicationService $applicationService)
    {
        $this->applicationService = $applicationService;
    }

    public function index(IndexApplicationsRequest $request)
    {
        $applicationData = ApplicationData::fromRequest($request);

        return StudentApplicationResource::collection($this->applicationService->getAll($applicationData));
    }

    public function store(ApplicationRequest $request)
    {
        return response()->json($this->applicationService->register($request));
    }

    public function show($id)
    {
        $student = StudentApplication::findOrFail($id);
        return new StudentApplicationResource($student);
    }

    public function update(UpdateApplicationRequest $request, $id)
    {
        $student = StudentApplication::findOrFail($id);

        if ($student->student_id) {
            return response()->json(['message' => 'student_already_accepted']);
        }

        $student->update($request->validated());
        $student->save();

        return response()->json(['message' => 'success']);
    }

    public function destroy($id)
    {
        $application = StudentApplication::where('id', $id)->firstOrFail();
        $application->delete();

        return response()->json(['message' => 'success']);
    }

    public function archiveAll()
    {
        try {
            DB::connection(config('database.sms_connection'))->beginTransaction();

            $columns = [
                'student_id', 'registration_number', 'first_name_ar', 'second_name_ar', 'third_name_ar', 'last_name_ar', 'first_name_en', 'second_name_en', 'third_name_en', 'last_name_en', 'sponsor', 'dob', 'national_id', 'passport_number', 'gender', 'nationality_id', 'status', 'entity_id', 'program_id', 'address', 'mother_name', 'birth_place', 'work_place', 'phone_number', 'martial_status', 'email', 'email_verified_at', 'created_at', 'updated_at'
            ];

            $select = StudentApplication::select($columns);
            DB::connection(config('database.sms_connection'))->table('application_archives')->insertUsing(
                $columns,
                $select
            );

            StudentApplication::truncate();

            DB::connection(config('database.sms_connection'))->commit();

        } catch (Exception $e) {
            DB::connection(config('database.sms_connection'))->rollBack();
            return response()->json(['message' => $e->getMessage()]);
        }
    }

    public function changeStatus(StudentApplication $application, ChangeApplicationRequest $request)
    {
        if($request->status == 'accepted' && ! $application->status == 'confirmed') {
            return response()->json(['message' => 'invalid_status']);
        }

        if($request->status == 'confirmed' && ! $application->status == 'default') {
            return response()->json(['message' => 'invalid_status']);
        }

        return response()->json($this->applicationService->changeStatus($application->id, $request));
    }

    public function accept(StudentApplication $application)
    {
        if($application->status != 'accepted') {
            return response()->json(['message' => 'يجب قبول الطالب أولا'], 400);
        }

        if(User::where('phone_number', $application->phone_number)->exists()) {
            return response()->json(['message' => 'رقم الهاتف موجود مسبقا'], 400);
        }

        if(User::where('email', $application->email)->orWhere('secondary_email', $application->email)->exists()) {
            return response()->json(['message' => 'البريد الإلكتروني موجود لطالب مسبقا'], 400);
        }
        // if(! Term::active()->exists()) {
        //     return response()->json(['message' => 'no_active_term']);
        // }
        return response()->json($this->applicationService->forwardToMainTables($application));
    }
}
