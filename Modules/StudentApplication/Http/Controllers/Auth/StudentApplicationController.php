<?php

namespace Modules\StudentApplication\Http\Controllers\Auth;

use App\Models\Program;
use App\Rules\ArabicChars;
use App\Rules\EnglishChars;
use App\Rules\PhoneNumber;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Modules\Sms\Entities\Student;
use Modules\StudentApplication\Entities\AcademicYear;
use Modules\StudentApplication\Entities\StudentApplication;
use Modules\StudentApplication\Events\NewStudentCreated;
use Modules\StudentApplication\Http\Requests\StudentApplications\ApplicationRequest;
use Modules\StudentApplication\Services\StudentApplications\StudentApplicationService;
use Modules\StudentApplication\Transformers\TempStudent\StudentApplicationResource;
use niklasravnsborg\LaravelPdf\Facades\Pdf;

class StudentApplicationController extends Controller
{
    /**
     * @var StudentApplicationService
     */
    private $applicationService;

    /**
     * TempStudentRegisterController constructor.
     * @param  StudentApplicationService  $applicationService
     */
    public function __construct(StudentApplicationService $applicationService)
    {
        $this->applicationService = $applicationService;
    }

    /**
     * @param  ApplicationRequest  $request
     * @return JsonResponse
     */
    public function register(ApplicationRequest $request)
    {
        $selectedProgram = Program::find($request->get('program_id'));
        $academicYear = AcademicYear::active()->first();
        if (
            $request->get('national_id') &&
            Student::whereHas('user', function ($q) use ($request) {
                return $q->where('national_id', $request->get('national_id'));
            })
            ->whereHas('program', function ($query) use ($selectedProgram) {
                $query->where('type', $selectedProgram->type);
            })
            ->first()
        ) {
            throw ValidationException::withMessages([
                'national_id' => [__('validation.unique', ['attribute' => 'national_id'])],
            ]);
        }

        if (
            $request->get('passport_number') &&
            Student::whereHas('user', function ($q) use ($request) {
                return $q->where('passport_number', $request->get('passport_number'));
            })
            ->whereHas('program', function ($query) use ($selectedProgram) {
                $query->where('type', $selectedProgram->type);
            })
            ->first()
        ) {
            throw ValidationException::withMessages([
                'passport_number' => [__('validation.unique', ['attribute' => 'passport_number'])],
            ]);
        }

        $faculty = $selectedProgram->entity->parent;
        $academicYearEntity = $academicYear->entities()->find($faculty->id);

        if ($academicYearEntity->pivot->min_gpa > $request->get('gpa')) {
            throw ValidationException::withMessages([
                'gpa' => ['المعدل الدراسي الذي أدخلته أقل من المعدل المطلوب لهذا القسم'],
            ]);
        }

        if (
            !in_array($request->get('department'), $faculty->details->departments)
        ) {
            throw ValidationException::withMessages([
                'faculty_id' => ['هذه الكلية غير متاحة للتخصص المختار'],
            ]);
        }

        return response()->json($this->applicationService->register($request));
    }

    /**
     * @param  StudentApplication  $student
     * @param  Request  $request
     */
    public function verify(StudentApplication $student, Request $request)
    {
        if ($student->hasVerifiedEmail()) {
            return response()->json(['message' => 'already_verified']);
        }

        $student->markEmailAsVerified();
        $response = $this->applicationService->sendDownloadLink($student);

        return response()->json($response);
    }

    public function sendRegistrationReceipt(Request $request)
    {
        // $student = StudentApplication::where('email', $request->get('email'))->firstOrFail();

        $request->validate([
            'username' => ['required', 'max:255'],
            'phone_number' => ['required', 'max:255'],
        ]);

        $student = StudentApplication::where(
            fn(Builder $q) => $q
                ->where('national_id', $request->get('username'))
                ->orWhere('passport_number', $request->get('username'))
        )
            ->where('phone_number', $request->get('phone_number'))
            ->firstOrFail();

        return response()->json($this->applicationService->sendDownloadLink($student));
    }

    public function downloadReceipt(StudentApplication $student)
    {
        $student->load('entity.parent');

        $data = [
            'student' => $student,
        ];

        $pdf = Pdf::loadView('pdf.receipt', $data);
        return $pdf->stream($student->registration_number . '-receipt.pdf');
    }

    public function checkNewStudent(Request $request)
    {
        $request->validate([
            'username' => ['required', 'max:255'],
            'phone_number' => ['required', 'max:255'],
        ]);

        $student = StudentApplication::where(
            fn(Builder $q) => $q
                ->where('national_id', $request->get('username'))
                ->orWhere('passport_number', $request->get('username'))
        )
            ->where('phone_number', 'like', '%' . $request->get('phone_number'))
            ->first();

        if (!$student) {
            abort(401, __('auth.failed'));
        }

        return response()->json(['message' => 'success', 'student' => new StudentApplicationResource($student)]);
    }

    public function updateStudent(Request $request)
    {
        $student = StudentApplication::where(
            fn(Builder $q) => $q
                ->where('national_id', $request->get('old_username'))
                ->orWhere('passport_number', $request->get('old_username'))
        )
            ->where('phone_number', 'like', '%' . $request->get('old_phone_number'))
            ->first();

        if (!$student) {
            abort(401, __('auth.failed'));
        }

        $validation = $request->validate([
            'first_name_ar' => ['required', 'max:255', new ArabicChars],
            'second_name_ar' => ['required', 'max:255', new ArabicChars],
            'third_name_ar' => ['required', 'max:255', new ArabicChars],
            'last_name_ar' => ['required', 'max:255', new ArabicChars],
            'first_name_en' => ['required', 'max:255', new EnglishChars],
            'second_name_en' => ['required', 'max:255', new EnglishChars],
            'third_name_en' => ['required', 'max:255', new EnglishChars],
            'last_name_en' => ['required', 'max:255', new EnglishChars],
            'nationality_id' => ['required', Rule::exists(config('database.default') . '.nationalities', 'id')],
            'national_id' => [
                Rule::requiredIf($request->nationality_id == 'ly'),
                'nullable',
                'digits:12',
                Rule::unique(
                    config('database.sms_connection') . '.student_applications',
                    'national_id'
                )->ignore($student->id)
            ],
            'passport_number' => [
                Rule::requiredIf($request->nationality_id !== 'ly'),
                'nullable',
                Rule::unique(
                    config('database.sms_connection') . '.student_applications',
                    'passport_number'
                )->ignore($student->id)
            ],
            'personal_id' => ['sometimes', 'nullable', 'string', 'max:255'],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default') . '.entities', 'id')
                    ->where(function ($query) {
                        $query->where('type', 3);
                    })
            ],
            'program_id' => [
                'required',
                'integer',
                Rule::exists(config('database.default') . '.programs', 'id')
                    ->where(function ($query) use ($request) {
                        $query->where('entity_id', $request->entity_id);
                    })
            ],
            'dob' => ['required', 'date'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(config('database.sms_connection') . '.student_applications', 'email')->ignore($student->id)
            ],
            'mother_name' => ['required', 'max:255'],
            'birth_place' => ['required', 'max:255'],
            'work_place' => ['present', 'nullable', 'max:255'],
            'martial_status' => ['required', Rule::exists(config('database.default') . '.martial_statuses', 'id')],
            'address' => ['required', 'max:255'],
            'phone_number' => ['required', new PhoneNumber],
        ]);

        $oldEmail = $student->email;

        $student->update($validation);

        if ($student->email !== $oldEmail) {
            $student->email_verified_at = null;
            $student->save();
            event(new NewStudentCreated($student));
        } else {
            if (!$student->hasVerifiedEmail()) {
                event(new NewStudentCreated($student));
            }
        }

        return response()->json(['message' => 'success']);
    }
}
