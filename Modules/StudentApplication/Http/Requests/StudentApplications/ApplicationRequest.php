<?php

namespace Modules\StudentApplication\Http\Requests\StudentApplications;

use App\Models\EntityDetails;
use App\Models\EntityType;
use App\Rules\ArabicChars;
use App\Rules\EnglishChars;
use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Student;

class ApplicationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_name_ar' => ['required', 'max:255', new ArabicChars],
            'second_name_ar' => ['required', 'max:255', new ArabicChars],
            'third_name_ar' => ['required', 'max:255', new ArabicChars],
            'last_name_ar' => ['required', 'max:255', new ArabicChars],
            'first_name_en' => ['required', 'max:255', new EnglishChars],
            'second_name_en' => ['required', 'max:255', new EnglishChars],
            'third_name_en' => ['required', 'max:255', new EnglishChars],
            'last_name_en' => ['required', 'max:255', new EnglishChars],
            'sponsor' => ['sometimes', 'nullable', 'string', 'max:255'],
            'nationality_id' => ['required', Rule::exists(config('database.default') . '.nationalities', 'id')],
            'national_id' => [Rule::requiredIf($this->nationality_id == 'ly'), 'nullable', 'digits:12'],
            'passport_number' => [Rule::requiredIf($this->nationality_id !== 'ly'), 'nullable'],
            'personal_id' => ['sometimes', 'nullable', 'string', 'max:255'],
            'entity_id' => [
                'required',
                Rule::exists(config('database.default') . '.entities', 'id')
                    ->where(function ($query) {
                        $query->where('type', 3);
                    })
            ],
            'program_id' => [
                'required', 'integer',
                Rule::exists(config('database.default') . '.programs', 'id')
                    ->where(function ($query) {
                        $query->where('entity_id', $this->entity_id);
                    })
            ],
            'dob' => ['required', 'date'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique(config('database.sms_connection') . '.student_applications', 'email')],
            'mother_name' => ['required', 'max:255'],
            'birth_place' => ['required', 'max:255'],
            // 'work_place' => ['present', 'nullable', 'max:255'],
            'martial_status' => ['required', Rule::exists(config('database.default') . '.martial_statuses', 'id')],
            'address' => ['required', 'max:255'],
            'phone_number' => ['required', new PhoneNumber],
            // 'qualification_id' => ['required', Rule::exists(config('database.default') . '.qualifications', 'id')],
            // 'qualification_university' => ['required', 'string', 'max:255'],
            // 'general_major' => ['required', 'string', 'max:255'],
            // 'specialization' => ['required', 'string', 'max:255'],

            'school_name' => ['required', 'string', 'max:255'],
            'department' => ['required', 'string', Rule::in([EntityDetails::Scientific, EntityDetails::Literary])],
            'year_of_study' => ['required', 'string', 'max:255'],
            'seat_number' => ['required', 'string', 'max:255'],
            'gpa' => ['required', 'numeric', 'between:0,100'],
            'enrollment_type' => ['required', Rule::in([Student::FULL_TIME_ENROLLMENT_TYPE, Student::PART_TIME_ENROLLMENT_TYPE])],

        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
