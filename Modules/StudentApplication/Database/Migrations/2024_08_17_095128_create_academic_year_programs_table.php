<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAcademicYearProgramsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->create('academic_year_programs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('academic_year_id')->unsigned();
            $table->integer('program_id')->unsigned();
            $table->timestamps();


            $table->foreign('program_id')->references('id')->on(config('database.connections.main_db.database') . '.programs')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('academic_year_id')->references('id')->on(config('database.connections.sms_db.database') . '.academic_years')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.sms_connection'))->dropIfExists('academic_year_programs');
    }
}
