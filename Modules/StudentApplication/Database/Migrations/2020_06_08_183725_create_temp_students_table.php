<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTempStudentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->create('temp_students', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('registration_number')->unique();
            $table->string('first_name_ar');
            $table->string('second_name_ar');
            $table->string('third_name_ar');
            $table->string('last_name_ar');
            $table->string('first_name_en');
            $table->string('second_name_en');
            $table->string('third_name_en');
            $table->string('last_name_en');
            $table->date('dob');
            $table->unsignedBigInteger('national_id')->unique()->nullable()->default(null);
            $table->string('passport_number')->unique()->nullable()->default(null);
            $table->tinyInteger('gender');
            $table->char('nationality_id', 5);
            $table->tinyInteger('status')->default(0);
            $table->char('entity_id', 30);
            $table->unsignedInteger('program_id');

            $table->string('address');
            $table->string('mother_name');
            $table->string('birth_place');
            $table->string('work_place');
            $table->string('phone_number');
            $table->unsignedInteger('martial_status');


            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamps();

            $table->foreign('program_id')->references('id')->on(config('database.connections.main_db.database') . '.programs')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('entity_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('nationality_id')->references('id')->on(config('database.connections.main_db.database') . '.nationalities')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('martial_status')->references('id')->on(config('database.connections.main_db.database') . '.martial_statuses')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.sms_connection'))->dropIfExists('temp_students');
    }
}
