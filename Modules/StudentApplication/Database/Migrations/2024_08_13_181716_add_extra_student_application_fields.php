<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddExtraStudentApplicationFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('student_applications', function (Blueprint $table) {
            $table->string('school_name')->nullable();
            $table->string('department')->nullable();
            $table->string('year_of_study')->nullable();
            $table->string('seat_number')->nullable();
            $table->string('gpa')->nullable();

            // $table->unsignedInteger('martial_status')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.sms_connection'))->table('student_applications', function (Blueprint $table) {
            $table->dropColumn('school_name');
            $table->dropColumn('department');
            $table->dropColumn('year_of_study');
            $table->dropColumn('seat_number');
            $table->dropColumn('gpa');
        });
    }
}
