<?php

namespace Modules\Sms\Entities;

use App\Traits\HasLang;
use App\Traits\HasTranslations;
use <PERSON><PERSON>voy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class TermStage extends Model implements Auditable
{
    use HasTranslations, HasLang, \OwenIt\Auditing\Auditable;

    public const ENROLLMENT = 1;
    public const DROP_AND_ADD = 2;
//    public const Replace = 3;
    public const PARTIAL_WITHDRAW = 4;
    public const TOTAL_WITHDRAW = 5;
    public const FINAL_EXAMS = 6;
    public const NEW_STUDENTS_REGISTRATION = 7;
    public const NEW_DOCTORATE_STUDENTS_REGISTRATION = 8;
    public const SUSPEND_REGISTRATION = 9;

    public const APPEALS = 10;

    public const DROP = 11;

    public $translatable = ['title'];

    protected $connection = 'sms_db';
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }


}
