<?php

namespace Modules\Sms\Entities;

use App\Models\Hall;
use App\Models\Lecturer;
use App\Models\Program;
use App\Models\Subject;
use <PERSON>yvoy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class TimetableContent extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use \Awobaz\Compoships\Compoships;

    protected $connection = 'sms_db';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
    ];

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function timetable()
    {
        return $this->belongsTo(Timetable::class);
    }

    public function hall()
    {
        return $this->belongsTo(Hall::class);
    }

    public function lecturer()
    {
        return $this->belongsTo(Lecturer::class);
    }

    public function student_subjects()
    {
        return $this->hasMany(StudentSubject::class, ['subject_id', 'group'], ['subject_id', 'group']);
    }


}
