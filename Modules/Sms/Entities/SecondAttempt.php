<?php

namespace Modules\Sms\Entities;

use Hoyvoy\CrossDatabase\Eloquent\Model;

class SecondAttempt extends Model
{
    public const PENDING_STATUS = 'pending';
    public const APPROVED_STATUS = 'approved';
    public const REJECTED_STATUS = 'rejected';

    protected $connection = 'sms_db';
    
    protected $fillable = [
        'student_subject_id',
        'old_mid_mark',
        'old_practical_final_mark',
        'old_final_mark',
        'mid_mark',
        'practical_final_mark',
        'final_mark',
        'note',
        'status'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }

    public function studentSubject()
    {
        return $this->belongsTo(StudentSubject::class);
    }
}
