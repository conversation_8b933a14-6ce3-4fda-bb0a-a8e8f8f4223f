<?php

namespace Modules\Sms\Entities;

use Hoyvoy\CrossDatabase\Eloquent\Model;

class SecondAttempt extends Model
{

    protected $connection = 'sms_db';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }

    public function studentSubject()
    {
        return $this->belongsTo(StudentSubject::class);
    }
}
