<?php

namespace Modules\Sms\Entities;

use App\Models\Subject;
use App\Models\SubjectType;
use <PERSON><PERSON><PERSON>z\Compoships\Compoships;
use <PERSON><PERSON>voy\CrossDatabase\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Sms\Services\TranscriptHelper;
use OwenIt\Auditing\Contracts\Auditable;

class StudentSubject extends Model implements Auditable
{
    use Compoships;
    use \OwenIt\Auditing\Auditable;
//    use SoftDeletes;

    protected $connection = 'sms_db';

    protected $casts = [
        'is_passed' => 'boolean',
    ];
    const TRANSFERRED_FROM_DEPARTMENT = 'from_department';
    const TRANSFERRED_FROM_FACULTY = 'from_faculty';
    const TRANSFERRED_FROM_UNIVERSITY = 'from_university';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.sms_connection'));
    }

    protected static function booted()
    {
        static::updating(function ($studentSubject) {
            //            if ($studentSubject->isDirty('mid_mark') || $studentSubject->isDirty('final_mark')) {
            //                $studentSubject->grade = GradeHelper::calculateGrade($studentSubject->full_mark);
            //            }
        });
    }

    public function isConfirmedBySchool()
    {
        return $this->confirmed_by == 'school';
    }

    public function isConfirmedByTeacher()
    {
        return $this->confirmed_by == 'teacher';
    }

    public function reCalculateGrade()
    {
        $this->load(['subject.program' => fn($q) => $q->withoutGlobalScopes()]);
        if(! $this->subject->is_practical_exam_hurdle) {
            $isPassed = $this->full_mark >= $this->subject->pass_mark;
        } else {
            $isPassed = $this->practical_final_mark >= ($this->subject->practical_final_mark / 2) && $this->full_mark >= $this->subject->pass_mark;
        }
        $this->updateQuietly([
            'is_passed' => $isPassed
        ]);
    }

    public function getFullMarkAttribute()
    {
        return $this->mid_mark + $this->final_mark + $this->practical_final_mark;
    }

    public function subjectType()
    {
        return $this->belongsTo(SubjectType::class, 'subject_type');
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function timetable_contents()
    {
        return $this->hasMany(TimetableContent::class, ['subject_id', 'group'], ['subject_id', 'group']);
    }

    public function term()
    {
        return $this->belongsTo(Term::class);
    }

    public function appeal()
    {
        return $this->hasOne(Appeal::class);
    }

    public function secondAttempt()
    {
        return $this->hasOne(SecondAttempt::class);
    }

    public function getGradeAttribute()
    {
        return $this->transformMarksToGrade();
    }

    public function transformMarksToGrade()
    {
        if (is_null($this->full_mark)) return '';

        if ($this->full_mark >= 85) {
            return 'ممتاز';
        } elseif ($this->full_mark >= 75) {
            return 'جيد جدا';
        } elseif ($this->full_mark >= 65) {
            return 'جيد';
        } elseif ($this->full_mark >= 50) {
            return 'مقبول';
        } elseif ($this->full_mark >= 35) {
            return 'ضعيف';
        } else {
            return 'ضعيف جدا';
        }
    }
}
