<?php

namespace Modules\Sms\Http\Controllers;

use App\Scopes\InDescendingEntities;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;
use Modules\Sms\Services\StudentTerms\StudentTermsService;
use Modules\Sms\Transformers\Timetables\TimetableResource;

class StudentTimetableController extends Controller
{
    private StudentTermsService $studentTermsService;

    public function __construct(StudentTermsService $studentTermsService)
    {
        $this->studentTermsService = $studentTermsService;
    }

    public function index(Student $student, Term $term)
    {
        if ($term->status != Term::ACTIVE_STATUS) {
            return $student->program()->first()?->subjects()->withoutGlobalScope(InDescendingEntities::class)
                ->select('id', 'program_id', 'code', 'title')
                ->get();
        }
        $term->load('timetable');
        if (!$term->timetable) return response()->json(null);
        $timetable = $this->studentTermsService->getAvailableTimetable($student->id, $term->timetable->id, false);

        return new TimetableResource($timetable);
    }
}
