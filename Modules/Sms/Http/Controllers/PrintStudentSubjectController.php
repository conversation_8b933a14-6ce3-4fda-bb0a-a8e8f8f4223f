<?php

namespace Modules\Sms\Http\Controllers;

use App\Scopes\InDescendingEntities;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;
use Modules\Sms\Services\StudentTerms\StudentTermsService;
use niklasrav<PERSON><PERSON>\LaravelPdf\Facades\Pdf;

class PrintStudentSubjectController extends Controller
{
    private StudentTermsService $studentTermsService;

    public function __construct(StudentTermsService $studentTermsService)
    {
        $this->studentTermsService = $studentTermsService;
    }

    public function index(Student $student, Term $term)
    {
        $studentSubjects = $this->studentTermsService->getSubjects($student->id, $term->id);

        $timetable = $term->timetable()->firstOrFail();

        $studentSubjects = $studentSubjects->sortBy('created_at');

        $timetables = $timetable->contents()->whereIn('subject_id', $studentSubjects->pluck('subject_id'))->get();

        $student->load(['program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)->with('entity.parent')]);

        $pdf = Pdf::loadView('sms::pdf.student-subjects', compact('studentSubjects', 'student', 'term', 'timetables'));
        return $pdf->stream($student->id . '-receipt.pdf');
    }
}
