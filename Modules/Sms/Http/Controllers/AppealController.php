<?php

namespace Modules\Sms\Http\Controllers;

use App\Http\Resources\SimpleSubjectResource;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Appeal;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Transformers\Student\AppealResource;

class AppealController extends Controller
{
    public function index(Request $request, Term $term, Subject $subject)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $studentSubjects = StudentSubject::where('term_id', $term->id)
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->pluck('id');

        $appeals = Appeal::whereIn('student_subject_id', $studentSubjects)
            ->with('studentSubject.student')
            ->get();

        return response()->json([
            'appeals' => AppealResource::collection($appeals),
            'subject' => new SimpleSubjectResource($subject),
        ]);
    }


    public function confirm(Request $request, Term $term, Subject $subject)
    {
        $request->validate([
            'group' => ['required'],
        ]);

        $studentSubjects = StudentSubject::where('term_id', $term->id)
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->pluck('id');

        $appeals = Appeal::whereIn('student_subject_id', $studentSubjects)
            ->get();

        foreach ($appeals as $appeal) {
            $appeal->update([
                'status' => Appeal::CLOSED_STATUS,
            ]);

            $appeal->studentSubject->update([
                // 'mid_mark' => $appeal->mid_mark,
                // 'practical_final_mark' => $appeal->practical_final_mark,
                'final_mark' => $appeal->final_mark,
            ]);
        }

        return response()->json(['message' => 'success']);
    }
}
