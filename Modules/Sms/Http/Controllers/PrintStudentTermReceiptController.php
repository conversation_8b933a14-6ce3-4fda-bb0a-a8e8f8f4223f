<?php


namespace Modules\Sms\Http\Controllers;


use App\Http\Controllers\Controller;
use App\Scopes\InDescendingEntities;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;
use niklasravnsborg\LaravelPdf\Facades\Pdf;

class PrintStudentTermReceiptController extends Controller
{
    public function index(Student $student, Term $term)
    {
        $student->load(['program' => fn ($q) => $q->withoutGlobalScope(InDescendingEntities::class)->with('entity.parent')]);

        $pdf = Pdf::loadView('sms::pdf.student-subject-receipt', compact('student', 'term'));
        return $pdf->stream($student->id . '-receipt.pdf');
    }
}
