<?php

namespace Modules\Sms\Http\Controllers;

use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Services\StudentTerms\StudentTermsService;
use Spatie\SimpleExcel\SimpleExcelReader;
use Spatie\SimpleExcel\SimpleExcelWriter;

class ImportGradesController extends Controller
{

    private StudentTermsService $studentTermsService;

    public function __construct(StudentTermsService $studentTermsService)
    {
        $this->studentTermsService = $studentTermsService;
    }

    public function index()
    {
        SimpleExcelWriter::streamDownload('import-grades.xlsx')
            ->noHeaderRow()
            ->addRow(['رقم القيد', 'درجة الأعمال', 'درجة النهائي'])
            ->toBrowser();
    }

    public function store(Request $request)
    {

        $request->validate([
            // 'file' => 'required|file|mimes:xlsx,xls',
            'subject_id' => ['required', Rule::exists(config('database.default') . '.subjects', 'id')],
            'term_id' => ['required', Rule::exists(config('database.sms_connection') . '.terms', 'id')],
            'group' => ['required', 'string', 'max:1'],
            'is_second_attempt' => ['required', 'boolean'],

            'student_grades' => ['required', 'array'],
            'student_grades.*.student_id' => ['required', 'numeric'],
            'student_grades.*.mid_mark' => ['required', 'numeric'],
            'student_grades.*.final_mark' => ['required', 'numeric'],
        ]);

        $addedGrades = [];
        $availableGrades = [];
        $notFoundStudents = [];

        $term = Term::find($request->term_id);
        $subject = Subject::find($request->subject_id);

        foreach ($request->student_grades as $studentGrade) {
            $student = Student::find($studentGrade['student_id']);
            if (!$student) {
                $notFoundStudents[] = $studentGrade['student_id'];
                continue;
            }

            if (StudentSubject::where('term_id', $term->id)
                ->where('subject_id', $subject->id)
                ->where('student_id', $student->id)
                ->exists()
            ) {
                $availableGrades[] = $studentGrade['student_id'];
                continue;
            }

            $addedSubject = $this->studentTermsService->addSubject(
                $student->id,
                $term->id,
                $subject->id,
                $request->get('group')
            );

            $addedSubject->update([
                'mid_mark' => $studentGrade['mid_mark'],
                'final_mark' => $studentGrade['final_mark'],
                'is_second_attempt' => $request->get('is_second_attempt'),
                'is_paid' => 1,
            ]);

            $addedGrades[] = $studentGrade['student_id'];
        }


        return response()->json([
            'added' => $addedGrades,
            'already_exists' => $availableGrades,
            'not_found' => $notFoundStudents
        ]);
    }
}
