<?php

namespace Modules\Sms\Http\Controllers\Teacher;

use App\Http\Resources\SimpleSubjectResource;
use App\Models\Subject;
use App\Scopes\InDescendingEntities;
use Illuminate\Http\Request;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Services\Timetables\TimetableService;
use Modules\Sms\Transformers\Student\StudentResource;
use ni<PERSON>sravnsborg\LaravelPdf\Facades\Pdf;
use Spatie\SimpleExcel\SimpleExcelWriter;

class SubjectController
{
    private TimetableService $timetableService;

    public function __construct(TimetableService $timetableService)
    {
        $this->timetableService = $timetableService;
    }

    public function show(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        $students = $this->getStudents($subject, $request->get('group'), $term->id);

        return response()->json([
            'students' => StudentResource::collection($students),
            'subject' => new SimpleSubjectResource($subject),
        ]);
    }

    private function getStudents(Subject $subject, $group, $termId, $onlyPaid = false, $withGrade = false, $withoutWithdraw = true, $onlyNotPassed = false)
    {
        $lecturerId = auth()->user()->lecturer()->firstOrFail()->id;

        $timetableContent = TimetableContent::with('timetable')
            ->whereHas('timetable', fn($q) => $q->where('term_id', $termId))
            ->where('subject_id', $subject->id)
            ->where('group', $group)
            ->where('lecturer_id', $lecturerId)
            ->firstOrFail();

        return $this->timetableService->getStudentsOfSubject($subject->id, $timetableContent->timetable?->term_id, $group, $onlyPaid, $withGrade, $withoutWithdraw, false, $onlyNotPassed);
    }

    public function export(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();
        // $term = Term::active()->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id);

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer
        ]);
        return $pdf->stream($subject->code . '-' . $request->get('group') . '.pdf');
    }

    public function exportStudentsExcel(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id, false, true);

        $data = [
            [
                'رقم القيد',
                'الاسم',
                'درجة النصفي',
                'درجة النهائي'
            ]
        ];

        foreach ($students as $student) {
            $data[] = [
                $student->id,
                $student->user->full_name,
                $student->studentSubjects->first()?->mid_mark,
                $student->studentSubjects->first()?->final_mark,
            ];
        }

        SimpleExcelWriter::streamDownload('students-' . $subject->code . '-' . $request->get('group') . '-' . now()->toDateTimeString('minute') . '.xlsx')
            ->noHeaderRow()
            ->addRows($data)
            ->toBrowser();
    }

    private function getData(Request $request, Subject $subject, $termId, $onlyPaid = false, $withGrade = false, $withoutWithdraw = true, $onlyNotPassed = false): array
    {
        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);

        $lecturer = auth()->user()->lecturer()->with('user')->firstOrFail();

        $hall = TimetableContent::with('hall')
            ->whereHas('timetable', fn($q) => $q->where('term_id', $termId))
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('lecturer_id', $lecturer->id)
            ->firstOrFail()
            ->hall;

        $students = $this->getStudents($subject, $request->get('group'), $termId, $onlyPaid, $withGrade, $withoutWithdraw, $onlyNotPassed);

        $department = $subject->program->entity;
        $school = $department->parent()->first();
        return array($lecturer, $students, $department, $school, $hall);
    }

    public function exportAttendance(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id, true);

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'attendance' => true,
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer
        ]);
        return $pdf->stream('الحضور-' . $subject->code . '-' . $request->get('group') . '.pdf');
    }

    public function exportExamsAttendance(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        // $term = Term::active()->firstOrFail();
        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id, true, true, false);

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students-exams-attendance', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer,
            'term' => $term,
            'hall' => $hall
        ]);
        return $pdf->stream('حضور الامتحان-' . $subject->code . '-' . $request->get('group') . '.pdf');
    }

    public function exportExamsAttendanceSecondAttempt(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        // $term = Term::active()->firstOrFail();
        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id, true, true, false, true);

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students-exams-attendance', [
            'title' => 'كشف حضور امتحان الدور الثاني',
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer,
            'term' => $term,
            'hall' => $hall
        ]);
        return $pdf->stream('حضور الامتحان الدور الثاني-' . $subject->code . '-' . $request->get('group') . '.pdf');
    }

    public function exportGrades(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        // $term = Term::active()->firstOrFail();
        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        list($lecturer, $students, $department, $school, $hall) = $this->getData($request, $subject, $term->id, true, true, false);

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students-grades', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer,
            'term' => $term
        ]);
        return $pdf->stream('كشف درجات مقرر-' . $subject->code . '-' . $request->get('group') . '-' . $subject->getTranslation('title', 'ar', false) . '.pdf');
    }

    public function exportAppeals(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);

        $lecturer = auth()->user();

        $students = Student::whereHas(
            'studentSubjects',
            fn($q) => $q->where('subject_id', $subject->id)
                ->where('group', $request->get('group'))
                ->where('term_id', $term->id)
                ->where('is_paid', true)
                ->whereHas('appeal')
        )->join(
            config('database.connections.main_db.database') . '.users',
            'users.id',
            '=',
            config('database.connections.sms_db.database') . '.students.user_id'
        )
            ->orderByRaw('CONCAT(first_name_ar, " ", second_name_ar, " ", third_name_ar, " ", last_name_ar)')
            ->select('students.*')
            ->get();

        $department = $subject->program->entity;
        $school = $department->parent()->first();

        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students-appeals', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer,
        ]);

        return $pdf->stream($subject->code . '-' . $request->get('group') . '.pdf');
    }

    public function exportSecondAttemptGrades(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);

        $lecturer = auth()->user()->lecturer()->with('user')->firstOrFail();


        $lecturerId = auth()->user()->lecturer()->firstOrFail()->id;

        $timetableContent = TimetableContent::with('timetable')
            ->whereHas('timetable', fn($q) => $q->where('term_id', $term->id))
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('lecturer_id', $lecturerId)
            ->firstOrFail();

        $students = $this->timetableService->getStudentsOfSubject(
            $subject->id, $timetableContent->timetable?->term_id, $request->get('group'),
            true, true, true, false, true
        );

        $department = $subject->program->entity;
        $school = $department->parent()->first();


        $pdf = Pdf::loadView('sms::reports.lecturer.subject-students-second-attempt-grades', [
            'students' => $students,
            'subject' => $subject,
            'group' => $request->get('group'),
            'department' => $department,
            'school' => $school,
            'lecturer' => $lecturer,
            'term' => $term
        ]);
        return $pdf->stream('كشف-درجات-دور-الثاني-' . $subject->code . '-' . $request->get('group') . '-' . $subject->getTranslation('title', 'ar', false) . '.pdf');
    }
}
