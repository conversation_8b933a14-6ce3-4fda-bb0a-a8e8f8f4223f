<?php

namespace Modules\Sms\Http\Controllers\Teacher;

use App\Http\Resources\SimpleSubjectResource;
use App\Models\Subject;
use App\Scopes\InDescendingEntities;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\ValidationException;
use Modules\Sms\Entities\SecondAttempt;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Transformers\Student\SecondAttemptResource;

class SecondAttemptController extends Controller
{
    public function index(Request $request, Subject $subject)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        $studentSubjects = StudentSubject::where('term_id', $term->id)
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('is_second_attempt', true)
            ->pluck('id');

        $secondAttempts = SecondAttempt::whereIn('student_subject_id', $studentSubjects)
            ->with('studentSubject.student')
            ->get();

        return response()->json([
            'second_attempts' => SecondAttemptResource::collection($secondAttempts),
            'subject' => new SimpleSubjectResource($subject),
        ]);
    }

    public function update(Request $request, Subject $subject)
    {
        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        $request->validate([
            'group' => ['required'],
            'second_attempts' => ['required', 'array'],
            'second_attempts.*.id' => ['required', 'exists:' . config('database.sms_connection') . '.second_attempts,id'],
            'second_attempts.*.status' => ['required', 'in:approved,rejected'],
            'second_attempts.*.note' => ['sometimes', 'nullable', 'string'],
        ]);

        $studentSubjects = StudentSubject::where('term_id', $term->id)
            ->where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->pluck('id');

        $secondAttempts = SecondAttempt::whereIn('student_subject_id', $studentSubjects)
            ->whereIn('id', collect($request->get('second_attempts'))->pluck('id'))
            ->get();

        if (count($secondAttempts) != count($request->get('second_attempts'))) {
            return response()->json(['message' => 'خطأ في البيانات المدخلة'], 422);
        }

        foreach ($request->get('second_attempts') as $secondAttemptData) {
            $secondAttempt = $secondAttempts->firstWhere('id', $secondAttemptData['id']);
            
            if ($secondAttempt) {
                $secondAttempt->status = $secondAttemptData['status'];
                $secondAttempt->note = $secondAttemptData['note'] ?? $secondAttempt->note;
                $secondAttempt->save();
                
                // If rejected, revert the changes
                if ($secondAttemptData['status'] === SecondAttempt::REJECTED_STATUS) {
                    $studentSubject = $secondAttempt->studentSubject;
                    $studentSubject->update([
                        'mid_mark' => $secondAttempt->old_mid_mark,
                        'practical_final_mark' => $secondAttempt->old_practical_final_mark,
                        'final_mark' => $secondAttempt->old_final_mark,
                    ]);
                    $studentSubject->reCalculateGrade();
                }
            }
        }

        return response()->json(['message' => 'success']);
    }
}
