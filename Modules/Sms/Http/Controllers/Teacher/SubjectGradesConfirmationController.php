<?php

namespace Modules\Sms\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Http\Resources\SimpleSubjectResource;
use App\Models\Subject;
use App\Rules\GradesSumRule;
use App\Scopes\InDescendingEntities;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Services\StudentTerms\StudentTermsService;
use Modules\Sms\Services\Timetables\TimetableService;
use Modules\Sms\Transformers\Student\StudentGradeResource;

class SubjectGradesConfirmationController extends Controller
{
    private TimetableService $timetableService;

    public function __construct(TimetableService $timetableService)
    {
        $this->timetableService = $timetableService;
    }

    public function index(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required'],
            'failed_only' => ['sometimes', 'nullable', 'boolean']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();
        // $term = Term::active()->firstOrFail();

        $failedOnly = $request->boolean('failed_only');
        $students = $this->getStudents($subject, $request->get('group'), $term->id, $failedOnly);

        return response()->json([
            'students' => StudentGradeResource::customCollection($students, true),
            'subject' => new SimpleSubjectResource($subject),
        ]);
    }

    private function getStudents(Subject $subject, $group, $termId, $failedOnly = false)
    {
        $lecturerId = auth()->user()->lecturer()->firstOrFail()->id;

        $timetableContent = TimetableContent::with('timetable')
            ->whereHas('timetable', fn($q) => $q->where('term_id', $termId))
            ->where('subject_id', $subject->id)
            ->where('group', $group)
            ->where('lecturer_id', $lecturerId)
            ->firstOrFail();

        return $this->timetableService->getStudentsOfSubject($subject->id, $timetableContent->timetable?->term_id, $group, true, true, true, true, $failedOnly);
    }

    public function confirm(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        if (
            StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->whereNotNull('confirmed_by')
            ->exists()
        ) {
            abort(403);
        }

        StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->whereNull('confirmed_by')
            ->update([
                'confirmed_by' => 'teacher',
            ]);

        return response()->json(['message' => 'success']);
    }

    public function update(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required'],
            'students' => ['required', 'array', new GradesSumRule()],

            'students.*.id' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.student_subjects', 'student_id')
                    ->where('is_paid', 1)
                    ->where('subject_id', $subject->id)
                    ->where('group', $request->get('group'))
            ],
            'students.*.mid_mark' => ['required', 'numeric', 'min:0'],
            'students.*.final_mark' => ['required', 'numeric', 'min:0'],
            'students.*.practical_final_mark' => ['required', 'numeric', 'min:0'],
            'students.*.incomplete' => ['sometimes', 'nullable', 'boolean'],
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        if (
            StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->whereNotNull('confirmed_by')
            ->exists()
        ) {
            abort(403);
        }

        StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->where('is_paid', 0)
            ->whereNull('confirmed_by')
            ->whereNull('grade')
            ->get()
            ->each(function ($s) {
                $s->update([
                    'mid_mark' => 0,
                    'final_mark' => 0,
                ]);
                $s->reCalculateGrade();
            });

        foreach ($request->get('students') as $student) {

            $grade = isset($student['incomplete']) && $student['incomplete'] ? 'I' : null;

            (new StudentTermsService())->updateSubject($student['id'], $term->id, $subject->id, $request->get('group'), $student['mid_mark'], $student['final_mark'], $student['practical_final_mark'], $grade, true, true);
        }

        return response()->json(['message' => 'success']);
    }

    public function updateSecondAttempt(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required'],
            'students' => ['required', 'array'],

            'students.*.id' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.student_subjects', 'student_id')
                    ->where('is_paid', 1)
                    ->where('subject_id', $subject->id)
                    ->where('group', $request->get('group'))
            ],
            'students.*.final_mark' => ['required', 'numeric', 'min:0'],
        ]);

        $subject->load(['program' => fn($q) => $q->with('entity')->withoutGlobalScope(InDescendingEntities::class)]);
        $term = $subject->program->entity->terms()->active()->whereType($subject->program->duration_unit)->firstOrFail();

        foreach ($request->get('students') as $student) {
            $studentSubject = StudentSubject::where('student_id', $student['id'])
                ->where('subject_id', $subject->id)
                ->where('group', $request->get('group'))
                ->where('term_id', $term->id)
                ->where('is_paid', 1)
                ->where('is_passed', false)
                ->whereNull('confirmed_by')
                ->first();

            if ($studentSubject) {
                $studentSubject->update([
                    'final_mark' => $student['final_mark'],
                    'is_second_attempt' => true
                ]);
                $studentSubject->reCalculateGrade();
            }
        }

        return response()->json(['message' => 'success']);
    }
}
