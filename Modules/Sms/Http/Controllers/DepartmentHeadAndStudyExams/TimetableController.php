<?php

namespace Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams;

use App\Http\Controllers\Controller;
use Modules\Sms\Entities\Timetable;
use Modules\Sms\Services\Timetables\TimetableService;
use Modules\Sms\Transformers\Timetables\TimetableResource;

class TimetableController extends Controller
{

    private TimetableService $timetableService;

    public function __construct(TimetableService $timetableService)
    {
        $this->timetableService = $timetableService;
    }

    public function index()
    {
        $timetables = $this->timetableService->getAll();
        return TimetableResource::collection($timetables);
    }

    public function show(Timetable $timetable)
    {
        $timetable->load('term');

        return TimetableResource::make($timetable);
    }
}
