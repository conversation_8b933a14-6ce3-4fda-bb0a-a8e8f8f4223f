<?php

namespace Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams;

use App\Http\Resources\SimpleSubjectResource;
use App\Services\Subjects\SubjectService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;

class SubjectController extends Controller
{

    private SubjectService $subjectService;

    public function __construct(SubjectService $subjectService)
    {
        $this->subjectService = $subjectService;
    }

    public function index(Request $request)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $request->validate([
            'program_id' => ['required', Rule::exists(config('database.default') . '.programs', 'id')->whereIn('entity_id', $entitiesIds)]
        ]);
        return SimpleSubjectResource::collection($this->subjectService->subjectsOfProgram($request->get('program_id'), true));
    }
}
