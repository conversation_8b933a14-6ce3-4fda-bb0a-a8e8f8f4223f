<?php

namespace Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams;

use App\Http\Controllers\Controller;
use App\Models\EntityType;
use App\Models\Program;
use App\Scopes\InDescendingEntities;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\SecondAttempt;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Transformers\SecondAttempt\SecondAttemptSubjectResource;

class SecondAttemptSubjectController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'sort_key' => ['sometimes', 'nullable', Rule::in([
                'subject_code',
                'second_attempts_count',
                'group'
            ])],
            'sort_type' => ['sometimes', 'nullable', Rule::in(['asc', 'desc'])],
            'search' => ['sometimes', 'nullable', 'string'],
        ]);

        $currentEntity = app('current_entity');
        if ($currentEntity->type == EntityType::DEPARTMENT) {
            $currentEntity = $currentEntity->parent;
        }
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $programsIds = Program::whereIn('entity_id', $entitiesIds)->get()->pluck('id');

        $activeTerms = Term::active()
            ->whereIn('entity_id', $entitiesIds)
            ->pluck('id');

        $search = $request->get('search');
        $sortKey = $request->get('sort_key', 'subject_code');
        $sortType = $request->get('sort_type', 'asc');

        $query = TimetableContent::query()
            ->select([
                'timetable_contents.subject_id',
                'timetable_contents.group',
                'timetable_contents.program_id'
            ])
            ->selectRaw('COUNT(second_attempts.id) as second_attempts_count')
            ->join('timetables', 'timetables.id', '=', 'timetable_contents.timetable_id')
            ->join('student_subjects', function ($join) {
                $join->on('student_subjects.subject_id', '=', 'timetable_contents.subject_id')
                     ->on('student_subjects.group', '=', 'timetable_contents.group')
                     ->on('student_subjects.term_id', '=', 'timetables.term_id');
            })
            ->join('second_attempts', 'second_attempts.student_subject_id', '=', 'student_subjects.id')
            ->whereIn('timetables.term_id', $activeTerms)
            ->whereIn('timetable_contents.program_id', $programsIds)
            ->where('second_attempts.is_confirmed', false)
            ->with(['subject', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)])
            ->groupBy([
                'timetable_contents.subject_id',
                'timetable_contents.group',
                'timetable_contents.program_id'
            ])
            ->having('second_attempts_count', '>', 0);

        if ($search) {
            $query->whereHas('subject', function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('title->ar', 'like', "%{$search}%")
                  ->orWhere('title->en', 'like', "%{$search}%");
            });
        }

        switch ($sortKey) {
            case 'subject_code':
                $query->join('subjects', 'subjects.id', '=', 'timetable_contents.subject_id')
                      ->orderBy('subjects.code', $sortType);
                break;
            case 'second_attempts_count':
                $query->orderBy('second_attempts_count', $sortType);
                break;
            case 'group':
                $query->orderBy('timetable_contents.group', $sortType);
                break;
            default:
                $query->orderBy('timetable_contents.subject_id', $sortType);
        }

        $subjects = $query->get();

        return SecondAttemptSubjectResource::collection($subjects);
    }
}
