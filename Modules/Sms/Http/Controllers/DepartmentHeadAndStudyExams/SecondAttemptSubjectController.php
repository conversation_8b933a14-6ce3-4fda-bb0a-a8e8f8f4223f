<?php

namespace Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams;

use App\Http\Controllers\Controller;
use App\Models\EntityType;
use App\Models\Program;
use App\Scopes\InDescendingEntities;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\SecondAttempt;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Transformers\SecondAttempt\SecondAttemptSubjectResource;

class SecondAttemptSubjectController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'sort_key' => ['sometimes', 'nullable', Rule::in([
                'subject_code',
                'second_attempts_count',
                'group'
            ])],
            'sort_type' => ['sometimes', 'nullable', Rule::in(['asc', 'desc'])],
            'search' => ['sometimes', 'nullable', 'string'],
        ]);

        $currentEntity = app('current_entity');
        if ($currentEntity->type == EntityType::DEPARTMENT) {
            $currentEntity = $currentEntity->parent;
        }
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $programsIds = Program::whereIn('entity_id', $entitiesIds)->get()->pluck('id');

        $activeTerms = Term::active()
            ->whereIn('entity_id', $entitiesIds)
            ->pluck('id');

        $search = $request->get('search');
        $sortKey = $request->get('sort_key', 'subject_code');
        $sortType = $request->get('sort_type', 'asc');

        $query = TimetableContent::query()
            ->join('timetables', 'timetables.id', '=', 'timetable_contents.timetable_id')
            ->leftJoin('student_subjects', function ($join) {
                $join->on('timetable_contents.subject_id', '=', 'student_subjects.subject_id');
                $join->on('timetable_contents.group', '=', 'student_subjects.group');
                $join->on('timetables.term_id', '=', 'student_subjects.term_id');
            })
            ->leftJoin('second_attempts', 'student_subjects.id', '=', 'second_attempts.student_subject_id')
            ->whereIn('timetables.term_id', $activeTerms)
            ->whereIn('timetable_contents.program_id', $programsIds)
            ->where('second_attempts.is_confirmed', false)
            ->with(['subject', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)])
            ->groupBy([
                'timetable_contents.subject_id',
                'timetable_contents.group',
                'timetable_contents.program_id'
            ])
            ->select([
                'timetable_contents.subject_id',
                'timetable_contents.group',
                'timetable_contents.program_id',
                \DB::raw('COUNT(second_attempts.id) as second_attempts_count')
            ])
            ->having('second_attempts_count', '>', 0);

        if ($search) {
            $query->whereHas('subject', function ($q) use ($search) {
                $q->where('code', 'like', '%' . $search . '%')
                  ->orWhere(\DB::raw('lower(title)'), 'LIKE', '%' . mb_strtolower($search) . '%');
            });
        }

        switch ($sortKey) {
            case 'subject_code':
                $query->whereHas('subject', function ($q) use ($sortType) {
                    $q->orderBy('code', $sortType);
                });
                break;
            case 'second_attempts_count':
                $query->orderBy('second_attempts_count', $sortType);
                break;
            case 'group':
                $query->orderBy('timetable_contents.group', $sortType);
                break;
            default:
                $query->orderBy('timetable_contents.subject_id', $sortType);
        }

        $results = $query->paginate();

        return SecondAttemptSubjectResource::collection($results);
    }
}
