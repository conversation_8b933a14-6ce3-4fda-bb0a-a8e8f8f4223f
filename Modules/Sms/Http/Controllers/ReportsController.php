<?php

namespace Modules\Sms\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Entity;
use App\Models\EntityType;
use App\Models\Hall;
use App\Models\Program;
use App\Models\Subject;
use App\Scopes\InDescendingEntities;
use DB;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentGraduation;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\StudentTerm;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\Timetable;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Services\TranscriptHelper;
use niklasravnsborg\LaravelPdf\Facades\Pdf;
use Spatie\SimpleExcel\SimpleExcelWriter;

class ReportsController extends Controller
{
    public function availableHalls()
    {
        $days = [1 => 'السبت', 2 => 'الأحد', 3 => 'الإثنين', 4 => 'الثلاثاء', 5 => 'الإربعاء', 6 => 'الخميس'];
        $times = ['09:00:00', '12:00:00', '15:00:00'];
        $hallsData = [];
        foreach ($days as $dayIndex => $day) {
            foreach ($times as $time) {
                $hallsIds = TimetableContent::where('start_time', $time)->where('day', $dayIndex)
                    ->select(['start_time', 'day', 'hall_id'])
                    ->pluck('hall_id')
                    ->toArray();
                Hall::whereNotIn('id', $hallsIds)
                    ->with('building')
                    ->get()
                    ->each(function ($hall) use ($time, $dayIndex, $days, &$hallsData) {
                        $hallsData[] = ['day' => $days[$dayIndex], 'time' => $time, 'hall' => $hall->title, 'building' => $hall->building->title];
                    });
            }
        }
        $pdf = Pdf::loadView('sms::reports.available-halls', ['halls' => $hallsData]);
        return $pdf->stream('القاعات الشاغرة.pdf');
    }

    public function renewedStudents()
    {
        $schools = $this->getSchoolsForRenewedStudents();

        $pdf = Pdf::loadView('sms::reports.renewed-students', ['schools' => $schools]);
        return $pdf->stream('احصائيات تجديد القيد.pdf');
    }

    private function getSchoolsForRenewedStudents()
    {
        $subQuery = $this->getStudentsSubQuery()
            ->getQuery();

        $programs = Program::select(['id', 'entity_id', 'title'])
            ->selectSub($subQuery, 's_count')
            ->get();

        $programs->each->setAppends([]);

        $schools = Entity::where('type', EntityType::SCHOOL)->select(['title', 'id', 'type'])->get();
        $departments = Entity::where('type', EntityType::DEPARTMENT)->select(['title', 'id', 'type', 'parent_entity_id'])->get();

        foreach ($departments as &$department) {
            $department->s_count = $programs->where('entity_id', '=', $department->id)->sum('s_count');
            $department->programs = $programs->where('entity_id', '=', $department->id);
        }

        foreach ($schools as &$school) {
            $school->s_count = $departments->where('parent_entity_id', '=', $school->id)->sum('s_count');
            $school->departments = $departments->where('parent_entity_id', '=', $school->id);
        }

        return $schools;
    }

    private function getStudentsSubQuery()
    {
        return Student::join(
            config('database.connections.sms_db.database') . '.student_terms',
            config('database.connections.sms_db.database') . '.students.id',
            '=',
            config('database.connections.sms_db.database') . '.student_terms.student_id'
        )
            ->where(config('database.connections.sms_db.database') . '.student_terms.status', StudentTerm::RENEWAL_STATUS)
            // ->where(config('database.connections.sms_db.database') . '.student_terms.term_id', $activeTerm->id)
            ->whereHas('terms', fn($q) => $q->where('terms.status', Term::ACTIVE_STATUS))
            ->whereRaw(DB::raw('students.program_id = ' . config('database.connections.main_db.database') . '.programs.id'))
            ->selectRaw(DB::raw('count(students.id) as s_count'));
    }

    public function renewedStudentsExcel()
    {
        $schools = $this->getSchoolsForRenewedStudents();

        $array = [
            [
                'الكلية',
                'العدد',
                'القسم',
                'العدد',
                'البرنامج الدراسي',
                'العدد',

            ]
        ];

        foreach ($schools as $school) {
            $array[] = [
                $school->getTranslation('title', 'ar', false),
                $school->s_count
            ];
            foreach ($school->departments as $department) {
                $array[] = [
                    '',
                    '',
                    $department->getTranslation('title', 'ar', false),
                    $department->s_count
                ];
                foreach ($department->programs as $program) {
                    $array[] = [
                        '',
                        '',
                        '',
                        '',
                        $program->getTranslation('title', 'ar', false),
                        $program->s_count
                    ];
                }
            }
        }

        SimpleExcelWriter::streamDownload('احصائيات تجديد القيد.xlsx')
            ->noHeaderRow()
            ->addRows($array)
            ->toBrowser();
    }

    public function detailedRenewedStudents()
    {
        $libyanSubQuery =
            $this->getStudentsSubQuery()
            ->join(
                config('database.connections.main_db.database') . '.users',
                config('database.connections.main_db.database') . '.users.id',
                '=',
                config('database.connections.sms_db.database') . '.students.user_id'
            )
            ->where(config('database.connections.main_db.database') . '.users.nationality_id', 'ly')
            ->getQuery();

        $libyanMaleSubQuery =
            $this->getStudentsSubQuery()
            ->join(
                config('database.connections.main_db.database') . '.users',
                config('database.connections.main_db.database') . '.users.id',
                '=',
                config('database.connections.sms_db.database') . '.students.user_id'
            )
            ->where(config('database.connections.main_db.database') . '.users.nationality_id', 'ly')
            ->where(config('database.connections.main_db.database') . '.users.gender', 1)
            ->getQuery();

        $nonLibyanSubQuery =
            $this->getStudentsSubQuery()
            ->join(
                config('database.connections.main_db.database') . '.users',
                config('database.connections.main_db.database') . '.users.id',
                '=',
                config('database.connections.sms_db.database') . '.students.user_id'
            )
            ->where(config('database.connections.main_db.database') . '.users.nationality_id', '!=', 'ly')
            ->getQuery();

        $nonLibyanMaleSubQuery =
            $this->getStudentsSubQuery()
            ->join(
                config('database.connections.main_db.database') . '.users',
                config('database.connections.main_db.database') . '.users.id',
                '=',
                config('database.connections.sms_db.database') . '.students.user_id'
            )
            ->where(config('database.connections.main_db.database') . '.users.nationality_id', '!=', 'ly')
            ->where(config('database.connections.main_db.database') . '.users.gender', 1)
            ->getQuery();


        $programs = Program::select(['id', 'entity_id', 'title'])
            ->selectSub($libyanSubQuery, 'libyan_count')
            ->selectSub($libyanMaleSubQuery, 'libyan_male_count')
            ->selectSub($nonLibyanSubQuery, 'non_libyan_count')
            ->selectSub($nonLibyanMaleSubQuery, 'non_libyan_male_count')
            ->get();

        $programs->each(function (&$program) {
            $program->setAppends([]);
            $program->total_count = $program->libyan_count + $program->non_libyan_count;
            $program->libyan_female_count = $program->libyan_count - $program->libyan_male_count;
            $program->non_libyan_female_count = $program->non_libyan_count - $program->non_libyan_male_count;
            $program->total_male_count = $program->libyan_male_count + $program->non_libyan_male_count;
            $program->total_female_count = $program->libyan_female_count + $program->non_libyan_female_count;
        });

        $schools = Entity::where('type', EntityType::SCHOOL)->select(['title', 'id', 'type'])->get();
        $departments = Entity::where('type', EntityType::DEPARTMENT)->select(['title', 'id', 'type', 'parent_entity_id'])->get();

        foreach ($departments as &$department) {
            $department->libyan_count = $programs->where('entity_id', '=', $department->id)->sum('libyan_count');
            $department->non_libyan_count = $programs->where('entity_id', '=', $department->id)->sum('non_libyan_count');

            $department->libyan_male_count = $programs->where('entity_id', '=', $department->id)->sum('libyan_male_count');
            $department->libyan_female_count = $programs->where('entity_id', '=', $department->id)->sum('libyan_female_count');
            $department->non_libyan_male_count = $programs->where('entity_id', '=', $department->id)->sum('non_libyan_male_count');
            $department->non_libyan_female_count = $programs->where('entity_id', '=', $department->id)->sum('non_libyan_female_count');

            $department->total_count = $programs->where('entity_id', '=', $department->id)->sum('total_count');
            $department->total_male_count = $programs->where('entity_id', '=', $department->id)->sum('total_male_count');
            $department->total_female_count = $programs->where('entity_id', '=', $department->id)->sum('total_female_count');

            $department->programs = $programs->where('entity_id', '=', $department->id);
        }


        foreach ($schools as &$school) {

            $school->libyan_count = $departments->where('parent_entity_id', '=', $school->id)->sum('libyan_count');
            $school->non_libyan_count = $departments->where('parent_entity_id', '=', $school->id)->sum('non_libyan_count');

            $school->libyan_male_count = $departments->where('parent_entity_id', '=', $school->id)->sum('libyan_male_count');
            $school->libyan_female_count = $departments->where('parent_entity_id', '=', $school->id)->sum('libyan_female_count');
            $school->non_libyan_male_count = $departments->where('parent_entity_id', '=', $school->id)->sum('non_libyan_male_count');
            $school->non_libyan_female_count = $departments->where('parent_entity_id', '=', $school->id)->sum('non_libyan_female_count');

            $school->total_count = $departments->where('parent_entity_id', '=', $school->id)->sum('total_count');
            $school->total_male_count = $departments->where('parent_entity_id', '=', $school->id)->sum('total_male_count');
            $school->total_female_count = $departments->where('parent_entity_id', '=', $school->id)->sum('total_female_count');

            $school->departments = $departments->where('parent_entity_id', '=', $school->id);
        }

        $pdf = Pdf::loadView('sms::reports.detailed-renewed-students', ['schools' => $schools]);
        return $pdf->stream('احصائيات تفصيلية عن تجديد القيد.pdf');
    }

    public function libyanNonLibyanStudents()
    {
        $programs = Program::with([
            'students' => fn($q) => $q
                ->whereHas('user', fn($q) => $q->where('nationality_id', '!=', 'ly'))
                ->whereHas(
                    'studentTerms',
                    fn($q) => $q->whereHas('term', fn($q) => $q->where('status', Term::ACTIVE_STATUS))
                        ->where('status', StudentTerm::RENEWAL_STATUS)
                )
                ->with(['user.nationality'])
        ])
            ->select(['id', 'entity_id', 'title'])
            ->get();

        $programs->each->setAppends([]);

        $schools = Entity::where('type', EntityType::SCHOOL)->select(['title', 'id', 'type'])->get();
        $departments = Entity::where('type', EntityType::DEPARTMENT)
            ->select(['title', 'id', 'type', 'parent_entity_id'])
            ->get();

        foreach ($departments as &$department) {
            $department->programs = $programs->where('entity_id', '=', $department->id);
        }

        foreach ($schools as &$school) {
            $school->departments = $departments->where('parent_entity_id', '=', $school->id);
        }

        $pdf = Pdf::loadView('sms::reports.non-libyan-students', ['schools' => $schools]);
        return $pdf->stream('احصائيات الطلبة الغير ليبيين.pdf');
    }

    public function timetableContent(Timetable $timetable, Request $request)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $programsIds = Program::whereIn('entity_id', $entitiesIds)
            ->when($request->has('programType'), fn($q) => $q->where('type', $request->get('programType')))
            ->when($request->has('program'), fn($q) => $q->where('id', $request->get('program')))
            ->get()->pluck('id');

        $school = $currentEntity->type == EntityType::SCHOOL ? $currentEntity : $currentEntity->parent()->first();
        $department = $currentEntity->type == EntityType::DEPARTMENT ? $currentEntity : null;

        $timetable->load([
            'contents' => fn($q) => $q
                ->whereIn('program_id', $programsIds)
                ->when($request->has('lecturer'), fn($q) => $q->where('lecturer_id', $request->get('lecturer')))
                ->when($request->has('hall'), fn($q) => $q->where('hall_id', $request->get('hall')))
                ->with(['subject', 'hall', 'lecturer.user', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)])
        ]);

        $pdf = Pdf::loadView('sms::reports.timetable', ['timetable' => $timetable, 'school' => $school, 'department' => $department]);
        return $pdf->stream('جدول الحصص.pdf');
    }

    public function graduatedStudentsPerTerm()
    {
        $programsIds = Program::get()->pluck('id');
        $graduatedStudentsPerTerm = StudentGraduation::query()
            ->whereHas('student', fn($q) => $q->whereIn('program_id', $programsIds))
            ->join('terms', 'terms.id', '=', 'students_graduations.term_id')
            ->selectRaw(DB::raw('terms.title, terms.year, count(*) as graduations_count'))
            ->groupBy('terms.title', 'terms.year')
            ->orderByDesc('terms.year', 'terms.title')
            ->get();

        $pdf = Pdf::loadView('sms::reports.graduated-students-per-term', ['gradutedStudentsPerTerm' => $graduatedStudentsPerTerm]);
        return $pdf->stream('احصائيات الخريجين لكل فصل دراسي .pdf');
    }

    public function topStudents()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        // $term = Term::active()->first() ?? Term::latest('id')->first();

        $latestSubjects = StudentSubject::select('subject_id', 'student_id', DB::raw('MAX(term_id) as max_term_id'))
            ->whereNotNull('grade')
            ->whereNotIn('grade', ['W', 'I', 'T'])
            ->where('is_gpa', true)
            ->where('subject_type', '!=', Subject::SUPPORTIVE)
            ->groupBy('student_id', 'subject_id');

        $students = Student::join('student_subjects', 'students.id', '=', 'student_subjects.student_id')
            ->joinSub($latestSubjects, 'latest_subjects', function ($join) {
                $join->on('students.id', '=', 'latest_subjects.student_id');
                $join->on('latest_subjects.subject_id', '=', 'student_subjects.subject_id');
                $join->on('latest_subjects.max_term_id', '=', 'student_subjects.term_id');
            })
            ->join('grades', 'grades.id', '=', 'student_subjects.grade')
            ->select(
                'students.*',
                DB::raw('sum(case when grades.pass = true then student_subjects.credit else 0 end) as passed_credits'),
                DB::raw('round(sum(grades.points * student_subjects.credit) / NULLIF(sum(student_subjects.credit), 0), 2) as gpa')
            )
            ->whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            // ->whereHas('studentTerms', fn ($q) => $q->where('term_id', $term->id)->where('status', StudentTerm::RENEWAL_STATUS))
            ->where('status', '=', Student::ACTIVE_STATUS)
            ->groupBy('students.id')
            ->having('passed_credits', '>=', 30)
            // ->having('gpa', '>=', 3)
            ->orderByDesc('gpa')
            ->orderBy('students.id')
            ->limit(100)
            ->get();

        $pdf = Pdf::loadView('sms::reports.top-students', ['students' => $students]);
        return $pdf->stream('الطلبة الأوائل.pdf');
    }

    public function studentsWithOneCredit()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        // $term = Term::active()->first() ?? Term::latest('id')->first();

        $students = Student::with('program.entity.parent')
            ->join('student_subjects', 'students.id', '=', 'student_subjects.student_id')
            ->whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->whereHas('studentTerms', fn($q) => $q->whereHas('term', fn($q) => $q->active() ?? $q->latest('id')))
            ->whereIn('students.id', function ($query) {
                $query->select('students.id')
                    ->from('students')
                    ->join('student_subjects', 'students.id', '=', 'student_subjects.student_id')
                    ->whereIn('grade', TranscriptHelper::getPassedMarks())
                    ->where('status', '!=', Student::GRADUATE_STATUS)
                    ->where('subject_type', '!=', Subject::SUPPORTIVE)
                    ->groupBy('students.id')
                    ->havingRaw('SUM(student_subjects.credit) < 27');
            })
            ->select('students.*')
            ->groupBy('student_id')
            ->havingRaw('count(*) = 1')
            ->get()
            ->sortBy(['program.entity.parent.id', 'program.entity.id', 'program.id']);

        $pdf = Pdf::loadView('sms::reports.students-with-one-credit', ['students' => $students]);
        return $pdf->stream('طلبة بمادة دراسية واحدة.pdf');
    }

    public function studentsTermStatuses(Request $request)
    {
        $request->validate([
            'status' => ['required', Rule::in(['total withdraw', 'partial withdraw', StudentTerm::DEFAULT_STATUS, StudentTerm::RENEWAL_STATUS, StudentTerm::SUSPENDED_REGISTRATION_STATUS, StudentTerm::DISCONTINUOUS_STATUS])],
        ]);

        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $array = [
            [
                'رقم القيد',
                'الاسم',
                'الكلية',
                'القسم',
                'البرنامج الدراسي',
            ]
        ];

        /*
         * total withdraw means any student that has suspended registration status and all his subjects are W
         * partial withdraw means student has at least one subject with grade W and didn't change term status to suspended registration
         * suspended registration means any student that has suspended registration status and didn't withdraw from any subject
         *
        */

        Student::nonGraduated()
            ->with('user.entity.parent', 'program')
            ->whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->when(
                in_array($request->get('status'), [StudentTerm::DEFAULT_STATUS, StudentTerm::RENEWAL_STATUS, StudentTerm::DISCONTINUOUS_STATUS, StudentTerm::SUSPENDED_REGISTRATION_STATUS]),
                fn($q) => $q->whereHas(
                    'studentTerms',
                    fn($q2) => $q2->where('status', $request->get('status'))
                        ->whereHas(
                            'term',
                            fn($q3) => $q3
                                ->when(
                                    $request->get('term_id'),
                                    fn($q4) => $q4->where('id', $request->get('term_id')),
                                    fn($q) => $q->active() // default
                                )
                        )
                )
            )
            ->chunk(2000, function ($students) use (&$array) {
                foreach ($students as $student) {
                    $studentId = $student->id;
                    $user = $student->user;

                    $array[] = [
                        $studentId,
                        $user->getFullNameAr(),
                        $user->entity?->parent->title,
                        $user->entity?->title,
                        $student->program?->title,
                    ];
                }
            });

        SimpleExcelWriter::streamDownload("{$request->get('status')}-students-" . now()->toDateTimeString('minute') . '.xlsx')
            ->noHeaderRow()
            ->addRows($array)
            ->toBrowser();
    }

    public function studentsUngraduratedWithThesis()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        // $term = Term::active()->first() ?? Term::latest('id')->first();

        $students = Student::with('program.entity.parent')
            ->whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->whereHas('theses', fn($q) => $q->where('is_passed', false))
            ->whereHas('studentTerms', fn($q) => $q->whereHas('term', fn($q) => $q->active() ?? $q->latest('id')))
            ->get()
            ->sortBy(['program.entity.parent.id', 'program.entity.id', 'program.id']);

        // file size is too big so we need to increase memory limit
        ini_set("memory_limit", '1024M');

        $pdf = Pdf::loadView('sms::reports.students-ungraduated-with-thesis', ['students' => $students]);

        return $pdf->stream('طلبة-الذين-لديهم-رسالة-ولم-يتخرجوا-بعد.pdf');
    }

    public function fileWithdrawalForm(Student $student)
    {
        // $student->load(['program' => fn ($q) => $q->withoutGlobalScope(InDescendingEntities::class)->with('entity.parent')]);
        $student->load(['program']);

        $pdf = Pdf::loadView('sms::pdf.file-withdrawal-form', compact('student'));
        return $pdf->stream('نموذج-سحب-ملف.pdf');
    }

    public function nonePaidStudents()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $array = [
            [
                'رقم القيد',
                'الأسم',
                'الكلية',
                'القسم',
                'البرنامج الدراسي',
            ]
        ];

        Student::query()
            ->with(['program.entity.parent'])
            ->whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->whereHas(
                'studentSubjects',
                fn($q) => $q->where('is_paid', 0)
                    ->whereHas('term', fn($q) => $q->active())
            )
            ->whereDoesntHave(
                'studentSubjects',
                fn($q) => $q->where('is_paid', 1)
                    ->whereHas('term', fn($q) => $q->active())
            )
            ->chunk(2000, function ($students) use (&$array) {
                foreach ($students as $student) {
                    $studentId = $student->id;
                    $user = $student->user;

                    $array[] = [
                        $studentId,
                        $user->getFullNameAr(),
                        $student->program?->entity?->parent->title,
                        $student->program?->entity?->title,
                        $student->program?->title,
                    ];
                }
            });

        SimpleExcelWriter::streamDownload("none-paid-students-" . now()->toDateTimeString('minute') . '.xlsx')
            ->noHeaderRow()
            ->addRows($array)
            ->toBrowser();
    }

    public function termResults(Term $term, Request $request)
    {
        ini_set("pcre.backtrack_limit", "5000000");
        ini_set("memory_limit", '2048M');
        set_time_limit(600);

        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        abort_if($term->status != Term::COMPLETED_STATUS && auth()->user()->type == 'student', 404);

        $students = Student::whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->whereHas(
                'terms',
                fn($q) => $q->where('terms.id', $term->id)->where('student_terms.status', StudentTerm::RENEWAL_STATUS)
            )
            ->when(auth()->user()->type == 'student', function ($q) {
                $student = auth()->user()->student()->with('program')->firstOrFail();
                return $q->where('id', $student->id);
            })
            ->when($request->get('student_id'), fn($q) => $q->where('id', $request->get('student_id')))
            ->with(['program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)->with('entity.parent')])
            ->get();


        $studentsData = collect([]);

        foreach ($students as $student) {
            list($subjects, $passedCredits, $gpa, $notices) = (new TranscriptHelper())->getStudentGPAData($student, null, $term->id);

            $studentsData->push([
                'student' => $student,
                'program' => $student->program->title,
                'subjects' => $subjects,
                'term_title' => __('sms::reports.seasons.' . $term->title) . '-' . $term->year,
            ]);
        }

        $pdf = Pdf::loadView('sms::pdf.term-result', compact('studentsData', 'term'));
        return $pdf->stream('نتائج-الفصل   .pdf');
    }
}
