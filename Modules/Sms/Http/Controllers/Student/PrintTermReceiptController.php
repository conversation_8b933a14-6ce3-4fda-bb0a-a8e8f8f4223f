<?php


namespace Modules\Sms\Http\Controllers\Student;


use App\Scopes\InDescendingEntities;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Term;
use niklasravnsborg\LaravelPdf\Facades\Pdf;

class PrintTermReceiptController extends Controller
{
    public function index(Term $term)
    {

        $student = auth()->user()->student()->firstOrFail();

        $student->load(['user', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)->with('entity.parent')]);

        $pdf = Pdf::loadView('sms::pdf.student-subject-receipt', compact('student', 'term'));
        return $pdf->stream($student->id . '-receipt.pdf');
    }
}
