<?php

namespace Modules\Sms\Http\Controllers\Student;

use App\Models\ProgramType;
use App\Models\Subject;
use Carbon\Carbon;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\StudentTerm;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\TermStage;
use Modules\Sms\Entities\Timetable;
use Modules\Sms\Http\Requests\Student\EnrollmentRequest;
use Modules\Sms\Services\StudentTerms\StudentTermsService;
use Modules\Sms\Services\TranscriptHelper;
use Modules\Sms\Transformers\Student\StudentSubjectResource;

class EnrollmentController extends Controller
{
    private StudentTermsService $studentTermsService;

    public function __construct(StudentTermsService $studentTermsService)
    {
        $this->studentTermsService = $studentTermsService;
    }

    public function store(EnrollmentRequest $request)
    {
        $student = auth()->user()->student()
            ->with('program.programType')
            ->firstOrFail();

        $department = auth()->user()->entity()->firstOrFail();

        $studentProgram = $student->program;

        $term = Term::active()->where('entity_id', $department->parent_entity_id)->whereType($student->program->duration_unit)->with([
            'timetable.contents' => fn($q) => $q->where(function ($q) use ($studentProgram) {
                $q->where('timetable_contents.program_id', $studentProgram->id)
                    ->when($studentProgram->program_based_on_id, fn($q) => $q->orWhere('timetable_contents.program_id', $studentProgram->program_based_on_id));
            })
        ])
            ->firstOrFail();

        abort_if(! $term->hasEnabledStage(TermStage::ENROLLMENT), 403);

//        $notices = (new TranscriptHelper())->calculateNotices($student, $term?->id);
//
//        $minNotices = 3;
//
//        if ($student->program->type == ProgramType::DOCTORATE) {
//            $minNotices = 1;
//        }

        //        if ($notices == 3) {
        //            abort(403, "لقد تحصلت على 3 إنذارات، يرجى مراجعة القسم الدراسي.");
        //        }

//        if ($notices > $minNotices) {
//            abort(403, "وفقا للائحة الأكاديمية تجاوزت عدد الإنذارات المسموح به، لذلك لا يمكنك التنزيل. ");
//        }

        $subject = Subject::where(function ($q) use ($studentProgram) {
            $q->where('program_id', $studentProgram->id)
                ->when($studentProgram->program_based_on_id, fn($q) => $q->orWhere('program_id', $studentProgram->program_based_on_id));
        })
            ->where('is_active', true)
            ->findOrFail($request->get('subject_id'));

        abort_if(! $this->hasActiveTerm($student, $term), 404);

        abort_if($this->hasEnrolled($student, $term, $subject), 403);

        //abort if student didnt pass the prerequisites of the subject
        // abort_if(!$this->studentTermsService->hasPassedPrerequisites($student, $subject), 403);

        $studentSubjects = $this->studentTermsService->getSubjects($student->id, $term->id);

        if ($this->reachedMaxCredits($student, $term, $subject)) {
            return response()->json([
                'message' => __(
                    'sms::enrollment.max_subjects',
                    ['number' => $student->program->programType->max_enroll_subjects ?? 0]
                )
            ]);
        }

        if ($this->hasCollision(
            $term->timetable,
            $studentSubjects,
            $request->get('subject_id'),
            $request->get('group')
        )) {
            return response()->json([
                'message' => __('sms::enrollment.collision')
            ]);
        }

        $subjectSlot = $term->timetable->contents
            ->where('subject_id', '=', $subject->id)
            ->where('group', '=', $request->get('group'))
            ->first();

        if (! $this->hasEmptyPlaces($term->id, $subject->id, $request->get('group'), $subjectSlot->capacity)) {
            return response()->json([
                'message' => __('sms::enrollment.no_space')
            ]);
        }

        $subject = $this->studentTermsService->addSubject(
            $student->id,
            $term->id,
            $subject->id,
            $request->get('group')
        );

        //change student status to renewal if its default
        if ($student->studentTerms()->where('term_id', $term->id)->where(
            'status',
            StudentTerm::DEFAULT_STATUS
        )->exists()) {
            $this->studentTermsService->changeStudentTermStatus($student->id, $term->id, StudentTerm::RENEWAL_STATUS);
        }

        return response()->json(['message' => 'success', 'subject' => new StudentSubjectResource($subject)]);
    }

    private function hasActiveTerm($student, Term $term)
    {
        if (
            ! $student->studentTerms()->where('term_id', $term->id)->exists() ||
            $student->studentTerms()
            ->where('term_id', $term->id)->where(fn($q) => $q->where(
                'status',
                StudentTerm::DISCONTINUOUS_STATUS
            )->orWhere(
                'status',
                StudentTerm::SUSPENDED_REGISTRATION_STATUS
            ))->exists()
        ) {
            return false;
        }
        return true;
    }

    private function hasEnrolled(Student $student, Term $term, Subject $subject)
    {
        return $student->studentSubjects()
            ->where('subject_id', $subject->id)
            ->where(function($q) use ($term) {
                $q->where('is_passed', 1)->orWhere('term_id', $term->id);
            })
            ->exists();
    }

    private function reachedMaxCredits(Student $student, Term $term, Subject $subject)
    {
        $maxEnrollmentCredits = $term->settings['enrollment_subjects'];
        return $student->studentSubjects()->where(
            'term_id',
            $term->id
        )
                ->sum('credit') + $subject->credits >= ($maxEnrollmentCredits ?? 0); // the name says max_enroll_subjects, but it should work for credits
    }

    private function hasCollision(Timetable $timetable, $studentSubjects, $subjectId, $group)
    {
        $contents = $timetable->contents
            ->where('subject_id', $subjectId)
            ->where('group', $group);

        foreach ($contents as $content) {
            foreach ($studentSubjects as $studentSubject) {
                $studentSlots = $timetable->contents->where('subject_id', $studentSubject->subject_id)->where(
                    'group',
                    $studentSubject->group
                );
                foreach ($studentSlots as $slot) {
                    if (
                        Carbon::createFromTimeString($content->start_time)->lt(Carbon::createFromTimeString($slot->end_time))
                        && Carbon::createFromTimeString($content->end_time)->gt(Carbon::createFromTimeString($slot->start_time))
                        && $content->day == $slot->day
                    ) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private function hasEmptyPlaces($termId, $subjectId, $group, $count)
    {
        return StudentSubject::where('term_id', $termId)
            ->where('subject_id', $subjectId)
            ->where('group', $group)
            ->count() < $count;
    }

    public function destroy(Subject $subject)
    {
        $student = auth()->user()->student()->firstOrFail();
        $department = auth()->user()->entity()->firstOrFail();

        // abort_if($subject->program_id !== $student->program_id, 404);

        $term = Term::active()->where('entity_id', $department->parent_entity_id)->whereType($student->program->duration_unit)->with([
            'timetable.contents' => fn($q) => $q
                ->where(function ($q) use ($student) {
                    $q->where('program_id', $student->program_id)
                        ->when($student->program->program_based_on_id, fn($q) => $q->orWhere('program_id', $student->program->program_based_on_id));
                })
        ])
            ->firstOrFail();

        abort_unless($term->hasEnabledStage(TermStage::ENROLLMENT) || $term->hasEnabledStage(TermStage::DROP), 403);

        abort_if(
            $student
                ->studentSubjects()
                ->where('term_id', $term->id)
                ->where('subject_id', $subject->id)
                ->whereNotNull('confirmed_by')
                ->exists(),
            403,
            'لا يمكنك حذف هذه المادة لأنه تم رصد درجتها.'
        );

        //abort if student doesnt have term or term is suspended or discontinuous
        abort_if(! $this->hasActiveTerm($student, $term), 404);

        if ($term->hasEnabledStage(TermStage::DROP) && $this->reachedMinSubjects($student, $term)) {
            return response()->json([
                'message' => __('sms::enrollment.min_subjects')
            ]);
        }

        $this->studentTermsService->deleteSubject($student->id, $term->id, $subject->id);

        return response()->json(['message' => 'success']);
    }

    private function reachedMinSubjects(Student $student, Term $term)
    {
        return $student->studentSubjects()->where('term_id', $term->id)
            ->whereRaw('NOT `grade` <=> \'W\'')
            ->count() <= $student->program->programType->min_enroll_subjects;
    }
}
