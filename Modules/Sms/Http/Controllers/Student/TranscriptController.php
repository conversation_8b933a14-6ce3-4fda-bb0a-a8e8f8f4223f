<?php

namespace Modules\Sms\Http\Controllers\Student;

use App\Scopes\InDescendingEntities;
use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;
use Modules\Sms\Services\TranscriptHelper;
use niklasravnsborg\LaravelPdf\Facades\Pdf;

class TranscriptController extends Controller
{
    public function index($id = null)
    {
        if ($id) {
            $currentEntity = app('current_entity');
            $entityTree = $currentEntity->childrenAndSelf();
            $entitiesIds = $currentEntity->getIds($entityTree);
            $student = Student::whereHas('user', fn($q) => $q->whereIn('entity_id', $entitiesIds))
                ->findOrFail($id);
        } else {
            $student = auth()->user()->student()->firstOrFail();
        }

        $student->load([
            'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)
        ]);

//        $activeTerm = $student->activeTerm()->first();
//
//        $terms = $student->terms()->get();

//        list($subjects, $passedCredits, $gpa, $notices) = (new TranscriptHelper())->getStudentGPAData($student, $activeTerm?->id);
        list($terms, $passedCredits, $gpa, $notices, $studyPlan) = $this->getTranscriptData($student);

        $pdf = Pdf::loadView('sms::pdf.transcript', compact('student', 'gpa', 'notices', 'passedCredits', 'terms', 'studyPlan'));
        return $pdf->stream($student->id . '-transcript.pdf');
    }

    private function getTranscriptData(Student $student)
    {
        $student->load(['user', 'program' => fn($q) => $q->with('programType', 'entity.parent')->withoutGlobalScope(InDescendingEntities::class)]);

        $activeTerm = $student->activeTerm()->first();
        $studyPlan = $activeTerm->study_plan;

        list($subjects, $passedCredits, $gpa, $notices) = (new TranscriptHelper())->getStudentGPAData($student, $activeTerm?->id);

        $allSubjects = collect([]);
        $terms = collect($subjects)->groupBy('term_id')->map(function ($subjects, $termId) use ($activeTerm, &$allSubjects) {
            $termName = $subjects->first()['term_name'] ?? '';
            $termYear = $subjects->first()['term_year'] ?? '';

            $subjects->each(function ($subject) use (&$allSubjects) {
                $allSubjects->push($subject);
            });

            list($x, $totalGPA) = (new TranscriptHelper())->getGPA($allSubjects);
            list($x, $termGPA) = (new TranscriptHelper())->getGPA($subjects);


            return [
                'id' => $termId,
                'name' => $termName . ' - ' . $termYear,
                'subjects' => $subjects->toArray(),
                'total_gpa' => $totalGPA,
                'term_gpa' => $termGPA,
                'term_credits' => $subjects->where('is_passed', true)->sum('credits'),
                'total_credits' => $allSubjects->where('is_passed', true)->sum('credits'),
            ];
        });

        return [$terms, $passedCredits, $gpa, $notices, $studyPlan];
    }
}
