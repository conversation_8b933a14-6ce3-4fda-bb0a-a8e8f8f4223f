<?php

namespace Modules\Sms\Http\Controllers\Student;

use Illuminate\Routing\Controller;
use Modules\Sms\Entities\Appeal;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\TermStage;
use Modules\Sms\Transformers\Student\StudentSubjectResource;

class AppealsController extends Controller
{
    public function store(StudentSubject $studentSubject)
    {
        $student = auth()->user()->student()->firstOrFail();
        $term = $studentSubject->term()->firstOrFail();

        // check if student is the owner of the subject
        abort_if($studentSubject->student_id != $student->id, 403, 'غير مسموح لك بالطعن في هذه المادة.');

        // check if term is active 
        abort_if($term->status != Term::ACTIVE_STATUS, 403, 'لا يمكنك تقديم طعن في هذا الفصل.');

        abort_if(! $term->hasEnabledStage(TermStage::APPEALS), 403, 'لا يمكنك تقديم طعن في هذه المادة.');

        // check if user have more than 2 appeals
        $appealsCount = Appeal::whereHas('studentSubject', function ($query) use ($student, $term) {
            $query->where('student_id', $student->id)->where('term_id', $term->id);
        })->count();

        abort_if($appealsCount >= 2, 403, 'لا يمكنك تقديم أكثر من طعنين في نفس الفصل.');

        // check if student already appealed this subject
        abort_if($studentSubject->appeal()->exists(), 403, 'لقد قمت بالطعن في هذه المادة مسبقاً.');

        $studentSubject->appeal()->create([
            'old_mid_mark' => $studentSubject->mid_mark,
            'old_practical_final_mark' => $studentSubject->practical_final_mark,
            'old_final_mark' => $studentSubject->final_mark,
            'mid_mark' => $studentSubject->mid_mark,
            'practical_final_mark' => $studentSubject->practical_final_mark,
            'final_mark' => $studentSubject->final_mark,
        ]);

        $studentSubject->load('appeal');

        return response()->json([
            'message' => 'تم تقديم الطعن بنجاح.',
            'subject' => new StudentSubjectResource($studentSubject),
        ]);
    }
}
