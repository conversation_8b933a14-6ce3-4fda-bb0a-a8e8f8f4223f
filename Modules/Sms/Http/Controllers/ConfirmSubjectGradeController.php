<?php

namespace Modules\Sms\Http\Controllers;

use App\Models\EntityType;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Term;

class ConfirmSubjectGradeController extends Controller
{
    public function store(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required'],
            'term_id' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.terms', 'id')
            ],
        ]);

        $term = Term::active()
            ->where('entity_id', $subject->program->entity->parent_entity_id)
            ->whereType($subject->program->duration_unit)
            ->findOrFail($request->get('term_id'));

        if (app('current_entity')->type == EntityType::SCHOOL) {

            if (StudentSubject::where('subject_id', $subject->id)
                ->where('group', $request->get('group'))
                ->where('term_id', $term->id)
                ->where('confirmed_by', 'department')
                ->exists()
            ) {
                StudentSubject::where('subject_id', $subject->id)
                    ->where('group', $request->get('group'))
                    ->where('term_id', $term->id)
                    ->update([
                        'confirmed_by' => 'school',
                    ]);
            }
        }

        if (app('current_entity')->type == EntityType::DEPARTMENT) {

            if (StudentSubject::where('subject_id', $subject->id)
                ->where('group', $request->get('group'))
                ->where('term_id', $term->id)
                ->where('confirmed_by', 'teacher')
                ->exists()
            ) {
                StudentSubject::where('subject_id', $subject->id)
                    ->where('group', $request->get('group'))
                    ->where('term_id', $term->id)
                    ->update([
                        'confirmed_by' => 'department',
                    ]);
            }
        }

        return response()->json(['message' => 'success']);
    }

    public function destroy(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required']
        ]);

        $term = Term::active()
            ->where('entity_id', $subject->program->entity->parent_entity_id)
            ->whereType($subject->program->duration_unit)
            ->firstOrFail();

        // if (
        //     app('current_entity')->type == EntityType::SCHOOL && StudentSubject::where('subject_id', $subject->id)
        //     ->where('group', $request->get('group'))
        //     ->where('term_id', $term->id)
        //     ->where('confirmed_by', 'school')
        //     ->exists()
        // ) {

        //     abort(403);
        // }

        if (
            app('current_entity')->type == EntityType::DEPARTMENT &&
            StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->where('confirmed_by', 'department')
            ->exists()
        ) {

            abort(403);
        }


        StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->update([
                'confirmed_by' => null,
            ]);

        return response()->json(['message' => 'success']);
    }

    public function confirmSecondAttempt(Subject $subject, Request $request)
    {
        $request->validate([
            'group' => ['required'],
            'term_id' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.terms', 'id')
            ],
            'second_attempt_ids' => ['required', 'array'],
            'second_attempt_ids.*' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.second_attempts', 'id')
            ],
        ]);

        $term = Term::active()
            ->where('entity_id', $subject->program->entity->parent_entity_id)
            ->whereType($subject->program->duration_unit)
            ->findOrFail($request->get('term_id'));

        // Get all second attempts for this subject and group
        $studentSubjectIds = StudentSubject::where('subject_id', $subject->id)
            ->where('group', $request->get('group'))
            ->where('term_id', $term->id)
            ->pluck('id');

        $secondAttempts = \Modules\Sms\Entities\SecondAttempt::whereIn('student_subject_id', $studentSubjectIds)
            ->whereIn('id', $request->get('second_attempt_ids'))
            ->where('is_confirmed', false)
            ->with('studentSubject')
            ->get();

        if ($secondAttempts->isEmpty()) {
            return response()->json(['message' => 'No valid second attempts found to confirm'], 422);
        }

        foreach ($secondAttempts as $secondAttempt) {
            // Update the SecondAttempt record to confirmed
            $secondAttempt->update(['is_confirmed' => true]);

            // Update the StudentSubject record with the new marks
            $studentSubject = $secondAttempt->studentSubject;
            $studentSubject->update([
                'mid_mark' => $secondAttempt->mid_mark,
                'practical_final_mark' => $secondAttempt->practical_final_mark,
                'final_mark' => $secondAttempt->final_mark,
                'is_second_attempt' => true
            ]);

            // Recalculate the grade
            $studentSubject->reCalculateGrade();
        }

        return response()->json(['message' => 'success']);
    }
}
