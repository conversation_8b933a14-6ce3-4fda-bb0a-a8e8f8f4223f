<?php

namespace Modules\Sms\Http\Controllers\GeneralRegistrar;

use App\Models\EntityType;
use App\Models\Program;
use App\Models\Subject;
use DB;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\StudentTerm;
use Modules\Sms\Entities\Term;

class TransferStudentController extends Controller
{
    public function internal(Student $student, Request $request)
    {
        $entity = app('current_entity');
        $isUniversity = $entity->type == EntityType::UNIVERSITY;
        $program = Program::findOrFail($request->get('program_id'));

        $request->validate([
            'password' => ['required', 'current_password'],
            'program_id' => ['required', Rule::exists(config('database.default') . '.programs', 'id')],
            'subjects' => ['nullable', 'array'],
            'subjects.*.subject_id' => [
                'required',
                Rule::exists(config('database.default') . '.subjects', 'id')
            ],
            'subjects.*.transfer_to' => [
                'required',
                Rule::exists(config('database.default') . '.subjects', 'id')->where('program_id', $request->get('program_id'))
            ],
            'subjects.*.term_id' => [
                Rule::requiredIf($isUniversity),
                Rule::when($isUniversity, [
                    Rule::exists(config('database.sms_connection') . '.terms', 'id')->where('entity_id', $program->entity->parent_entity_id)
                ])
            ],
        ]);

        $subjectsIds = collect($request->get('subjects'))->pluck('transfer_to')->toArray();

        $activeTerm = $student->activeTerm()->first();

        DB::connection(config('database.sms_connection'))->transaction(function () use ($isUniversity, $activeTerm, $request, $student, $subjectsIds, $program) {
            if ($activeTerm) {
                foreach ($student->studentSubjects()->where('term_id', $activeTerm->id)->get() as $subject) {
                    $subject->delete();
                }
            }

            if ($isUniversity) {
                $student->terms()->detach();
            }

            foreach ($request->get('subjects') as $subject) {
                $newSubject = Subject::findOrFail($subject['transfer_to']);
                $oldSubject = Subject::findOrFail($subject['subject_id']);
                $studentSubjects = $student->studentSubjects()->where('subject_id', $subject['subject_id'])->latest()->get();

                if ($studentSubjects->where('is_passed')->count() <= 0) {
                    abort(400, 'لا يمكن معادلة مادة غير ناجحة');
                }

                $term = null;

                if ($isUniversity) {
                    $term = Term::findOrFail($subject['term_id']);

                    $student->studentTerms()->firstOrCreate([
                        'term_id' => $term->id,
                    ], [
                        'status' => StudentTerm::RENEWAL_STATUS
                    ]);
                }

                if ($newSubject->program->entity->parent_entity_id != $oldSubject->program->entity->parent_entity_id) {
                    $transferType = StudentSubject::TRANSFERRED_FROM_FACULTY;
                } else {
                    $transferType = StudentSubject::TRANSFERRED_FROM_DEPARTMENT;
                }

                $sj = $studentSubjects->where('is_passed', true)->first();
                $newStudentSubject = $sj->replicate();

//                $studentSubjects->each(function ($subject) {
//                    $subject->delete();
//                });

                $newStudentSubject->fill([
                    'subject_id' => $newSubject->id,
                    'subject_code' => $newSubject->code,
                    'subject_title_ar' => $newSubject->getTranslation('title', 'ar', false),
                    'subject_title_en' => $newSubject->getTranslation('title', 'en', false),
                    'subject_type' => $newSubject->type,
                    'credit' => $newSubject->credits,
                    'term_id' => $isUniversity ? $term->id : $newStudentSubject->term_id,
                    'transfer_type' => $transferType,
                ]);

                $newStudentSubject->save();
            }

            foreach ($student->studentSubjects()->whereNotIn('subject_id', $subjectsIds)->get() as $subject) {
                $subject->update([
                    'is_gpa' => 0,
                ]);
            }

            $student->update([
                'program_id' => $program->id,
            ]);
        });

        $student->user()->update([
            'entity_id' => $program->entity_id,
        ]);

        return response()->json(['message' => 'success']);
    }

    public function outer(Student $student, Request $request)
    {
        $program = $student->program;

        $request->validate([
            'password' => ['required', 'current_password'],
            'subjects' => ['required', 'array'],
            'subjects.*.subject_id' => [
                'required',
                Rule::exists(config('database.default') . '.subjects', 'id')->where('program_id', $student->program_id)
            ],
            'subjects.*.term_id' => [
                'required',
                Rule::exists(config('database.sms_connection') . '.terms', 'id')->where('entity_id', $program->entity->parent_entity_id)
            ],
            'subjects.*.mid_mark' => [
                'required',
                'numeric'
            ],
            'subjects.*.final_mark' => [
                'required',
                'numeric'
            ],
        ]);

        if ($student->studentSubjects()->exists()) {
            abort(400, 'الطالب يملك موادا مسجلة بالفعل');
        }

        DB::connection(config('database.sms_connection'))->transaction(function () use ($student, $request) {

            foreach ($request->get('subjects') as $subject) {
                $student->studentTerms()->firstOrCreate([
                    'term_id' => $subject['term_id'],
                ], [
                    'status' => StudentTerm::RENEWAL_STATUS
                ]);

                $subjectObject = Subject::findOrFail($subject['subject_id']);
                $term = Term::findOrFail($subject['term_id']);
                $student->studentSubjects()->create([
                    'student_id' => $student->id,
                    'subject_id' => $subjectObject->id,
                    'term_id' => $term->id,
                    'term_year' => $term->year,
                    'term_title' => $term->title,
                    'credit' => $subjectObject->credits,
                    'subject_code' => $subjectObject->code,
                    'subject_title_ar' => $subjectObject->getTranslation('title', 'ar', false),
                    'subject_title_en' => $subjectObject->getTranslation('title', 'en', false),
                    'subject_type' => $subjectObject->type,
                    'is_paid' => 1,
                    'confirmed_by' => 'school',
                    'mid_mark' => $subject['mid_mark'],
                    'final_mark' => $subject['final_mark'],
                    'transfer_type' => StudentSubject::TRANSFERRED_FROM_UNIVERSITY,
                    'is_gpa' => 1,
                    'is_passed' => 1
                ]);
            }
        });
    }
}
