<?php

namespace Modules\Sms\Http\Requests\Student;

use App\Http\Requests\User\UserCustomRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\OldStudent;
use Modules\Sms\Entities\Student;

class StoreStudentRequest extends FormRequest
{

    public function type()
    {
        return 'student';
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
        $oldStudent = OldStudent::where('id', $this->student_id)->first();

        return $oldStudent && $this->normalizeSingleName($oldStudent->first_name_ar) == $this->normalizeSingleName($this->first_name_ar);
    }

    use UserCustomRules;

    private function normalizeSingleName($name)
    {
        $problemchars = array(
            " ",
            "أ",
            "إ",
            "آ",
            "ى",
            "ة",
            "ؤ",
        );
        $solutionchars = array(
            "",  //remove space
            "ا",
            "ا",
            "ا",
            "ي",
            "ه",
            "و"
        );

        return str_replace($problemchars, $solutionchars, $name);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->storeUserRules(), [
            'student_id' => ['required', 'integer', Rule::unique(config('database.sms_connection') . '.students', 'id')],
            'program_id' => [
                'required', 'integer',
                Rule::exists(config('database.default') . '.programs', 'id')
                    ->where(function ($query) {
                        $query->where('entity_id', $this->entity_id);
                    })
            ],
            'address' => ['required', 'max:255'],
            'mother_name' => ['required', 'max:255'],
            'birth_place' => ['required', 'max:255'],
            'martial_status' => ['required', Rule::exists(config('database.default') . '.martial_statuses', 'id')],
            'sponsor' => ['sometimes', 'nullable', 'string', 'max:255'],
            'enrollment_type' => ['required', Rule::in([Student::FULL_TIME_ENROLLMENT_TYPE, Student::PART_TIME_ENROLLMENT_TYPE])],
            // 'birth_place_en' => ['sometimes', 'nullable', 'max:255'],
            // 'work_place' => ['required', 'max:255'],
            // 'qualification_id' => ['sometimes', 'nullable', 'integer', Rule::exists(config('database.default') . '.qualifications', 'id')],
            // 'qualification_university' => ['sometimes', 'nullable', 'string', 'max:255'],
            // 'general_major' => ['sometimes', 'nullable', 'string', 'max:255'],
            // 'specialization' => ['sometimes', 'nullable', 'string', 'max:255'],
        ]);
    }
}
