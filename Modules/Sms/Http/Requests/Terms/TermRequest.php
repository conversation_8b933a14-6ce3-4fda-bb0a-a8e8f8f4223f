<?php

namespace Modules\Sms\Http\Requests\Terms;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Term;

class TermRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => ['required', Rule::in(['summer', 'spring', 'fall', 'academic year'])],
            'year' => ['required', 'date_format:Y'],
            'study_plan' => ['required', Rule::in([Term::SEMESTER_PLAN, Term::YEAR_PLAN])],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
