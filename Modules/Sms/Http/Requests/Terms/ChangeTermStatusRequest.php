<?php

namespace Modules\Sms\Http\Requests\Terms;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Term;

class ChangeTermStatusRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            //            Term::ACTIVE_STATUS =>
        ];
        return [
            'password' => ['required', 'current_password'],
            'status' => [
                'required',
                //                Rule::in([Term::ACTIVE_STATUS])
                Rule::when($this->route('term')->status == Term::ACTIVE_STATUS, [
                    Rule::in([Term::COMPLETED_STATUS])
                ]),
                Rule::when($this->route('term')->status == Term::PENDING_STATUS, [
                    Rule::in([Term::ACTIVE_STATUS])
                ])
            ]
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
