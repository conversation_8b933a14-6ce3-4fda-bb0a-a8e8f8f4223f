<?php

namespace Modules\Sms\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Sms\Entities\Thesis;

class StudentThesisRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title_ar' => ['required', 'string'],
            'title_en' => ['required', 'string'],
            'thesis_field' => ['sometimes', 'nullable', 'string'],
            'teacher_1' => ['required', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'teacher_2' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'teacher_3' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'teacher_4' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'examiner_1' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'examiner_2' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'examiner_3' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'examiner_4' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.lecturers', 'id')],
            'reg_decision_no' => ['sometimes', 'nullable', 'string', 'max:255'],
            'register_semester' => ['sometimes', 'nullable', Rule::exists(config('database.sms_connection') . '.terms', 'id')],
            'register_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'discussion_decision_no' => ['sometimes', 'nullable', 'string', 'max:255'],
            'discussion_expected_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'discussion_date' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
            'discussion_semester' => ['sometimes', 'nullable', Rule::exists(config('database.sms_connection') . '.terms', 'id')],
            'notes' => ['sometimes', 'nullable', 'string'],
            'hall_id' => ['sometimes', 'nullable', Rule::exists(config('database.default') . '.halls', 'id')],
            'proposal_status' => ['required', Rule::in([Thesis::PENDING_STATUS, Thesis::REJECTED_STATUS, Thesis::ACCEPTED_STATUS])],
            'thesis_grade' => ['sometimes', 'nullable', Rule::in(['passed', 'passed but require updates', 'rediscussion', 'failed'])],
            'is_passed' => ['sometimes', 'nullable', 'boolean']
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
