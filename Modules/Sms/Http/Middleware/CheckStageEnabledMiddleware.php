<?php

namespace Modules\Sms\Http\Middleware;

use App\Helpers\EntitiesHelper;
use App\Helpers\Utils;
use Closure;
use Illuminate\Http\Request;

class CheckStageEnabledMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$stages)
    {
        $currentEntityId = EntitiesHelper::getEntityId();

        if (auth()->check() && (auth()->user()->is_superadmin || auth()->user()->hasRole('study and exams', $currentEntityId))) {
            return $next($request);
        }

        foreach ($stages as $stage) {
            if (Utils::isStageEnabled($stage)) {
                return $next($request);
            }
        }
        abort(404);
    }
}
