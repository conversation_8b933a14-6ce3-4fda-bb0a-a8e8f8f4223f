<?php

namespace Modules\Sms\Services;

use App\Models\ProgramType;
use App\Models\Subject;
use Illuminate\Support\Collection;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\Term;

class TranscriptHelper
{
    public static function calculateGrade($mark, $termId, $programType = null)
    {
        if ($programType == ProgramType::DOCTORATE) {
            if ($mark >= 90) {
                return 'A';
            } else if ($mark >= 85) {
                return 'A-';
            } else if ($mark >= 80) {
                return 'B+';
            } else if ($mark >= 75) {
                return 'B';
            } else if ($mark >= 71) {
                return 'B-';
            } else {
                return 'F';
            }
        } else {
            $newGradeTerm = Term::where('title', 'spring')->where('year', 2022)->first();
            if ($newGradeTerm && $termId >= $newGradeTerm?->id) {
                if ($mark >= 90) {
                    return 'A';
                } else if ($mark >= 85) {
                    return 'A-';
                } else if ($mark >= 80) {
                    return 'B+';
                } else if ($mark >= 75) {
                    return 'B';
                } else if ($mark >= 71) {
                    return 'B-';
                } else if ($mark >= 68) {
                    return 'C+';
                } else if ($mark >= 65) {
                    return 'C';
                } else if ($mark >= 60) {
                    return 'C-';
                } else if ($mark >= 55) {
                    return 'D+';
                } else if ($mark >= 50) {
                    return 'D';
                } else {
                    return 'F';
                }
            } else {
                if ($mark >= 85) {
                    return 'A';
                } else if ($mark >= 81) {
                    return 'A-';
                } else if ($mark >= 78) {
                    return 'B+';
                } else if ($mark >= 75) {
                    return 'B';
                } else if ($mark >= 71) {
                    return 'B-';
                } else if ($mark >= 68) {
                    return 'C+';
                } else if ($mark >= 65) {
                    return 'C';
                } else if ($mark >= 60) {
                    return 'C-';
                } else if ($mark >= 55) {
                    return 'D+';
                } else if ($mark >= 50) {
                    return 'D';
                } else {
                    return 'F';
                }
            }
        }
    }

    public function getStudentGPAData($student, $exceptTerm = null, $onlyTerm = null): array
    {
        $subjectName = 'subject_title_' . app()->getLocale();

        $transcriptItems = $student->studentSubjects()
            ->when($exceptTerm, fn($q) => $q->where('term_id', '!=', $exceptTerm))
            ->when($onlyTerm, fn($q) => $q->where('term_id', $onlyTerm))
            ->where('is_gpa', 1)
            ->orderBy('term_id')
            ->orderBy($subjectName)
            ->get();

        $subjects = collect([]);

        foreach ($transcriptItems as $item) {

            $lastSubject = $subjects->where('id', $item->subject_id)->last();

            $subjects->push([
                'id' => $item->subject_id,
                'subject_name' => $item->$subjectName,
                'code' => $item->subject_code,
                'credits' => $item->credit,
                'subject_type' => $item->subject_type,
                'term_id' => $item->term_id,
                'term_name' => __('sms::reports.seasons.' . $item->term_title),
                'term_year' => $item->term_year,
                'grade' => $item->grade,
                'is_gpa' => $item->is_gpa,
                'full_mark' => $item->full_mark,
                'points' => $item->full_mark * $item->credit,
                'attempts' => $lastSubject ? $lastSubject['attempts'] + 1 : 1,
                'is_passed' => $item->is_passed,
                'transfer_type' => $item->transfer_type,
            ]);
        }

        list($passedCredits, $gpa) = $this->getGPA($subjects);

        $notices = $this->calculateNotices($student, $exceptTerm);

        return array($subjects, $passedCredits, $gpa, $notices);
    }

    // do the same as getStudentGPAData but for specific term
    public function getStudentTermResult($student, $term)
    {

        $subjectName = 'subject_title_' . app()->getLocale();

        $transcriptItems = $student->studentSubjects()
            ->where('term_id', $term)
            ->orderBy('subject_title_ar')
            ->orderBy('subject_title_en')
            ->get();

        $subjects = collect([]);

        foreach ($transcriptItems as $item) {

            $lastSubject = $subjects->where('id', $item->subject_id)->last();

            $subjects->push([
                'id' => $item->subject_id,
                'subject_name' => $item->$subjectName,
                'code' => $item->subject_code,
                'credits' => $item->credit,
                'subject_type' => $item->subject_type,
                'term_id' => $item->term_id,
                'term_name' => __('sms::reports.seasons.' . $item->term_title),
                'term_year' => $item->term_year,
                'grade' => $item->grade,
                'is_gpa' => $item->is_gpa,
                'full_mark' => $item->full_mark,
                'points' => $item->full_mark * $item->credits,
                'attempts' => $lastSubject ? $lastSubject['attempts'] + 1 : 1,
                'is_passed' => $item->is_passed,
            ]);
        }

        list($passedCredits, $gpa) = $this->getGPA($subjects);

        $notices = $this->calculateNotices($student);

        return array($subjects, $passedCredits, $gpa, $notices);
    }

    public static function getSubjectTypes()
    {
        return [
            Subject::GENERAL => [
                'char' => 'ع',
                'name' => 'عامة'
            ],
            Subject::COMPULSORY => [
                'char' => 'س',
                'name' => 'أساسية'
            ],
            Subject::ELECTIVE => [
                'char' => 'خ',
                'name' => 'إختياري'
            ],
            Subject::SUPPORTIVE => [
                'char' => 'د',
                'name' => 'داعمة'
            ],
        ];
    }

    public static function markCredits($grade)
    {
        $points = [
            'A' => 4,
            'A-' => 3.7,
            'B+' => 3.5,
            'B' => 3,
            'B-' => 2.7,
            'C+' => 2.5,
            'C' => 2,
            'C-' => 1.7,
            'D+' => 1.5,
            'D' => 1,
            'F' => 0
        ];
        return $points[$grade] ?? 0;
    }

    public function getGPA(Collection $subjects): array
    {
        $totalSubjects = $subjects->whereNotNull('grade')
            ->where('is_gpa', true)
            ->sortByDesc('term_id')
            ->unique('id');

        $passedCredits = $totalSubjects
            ->filter(fn($subject) => $subject['grade'] != 'T')
            ->where('is_passed', true)
            ->sum('credits');

        if ($passedCredits > 0) {
            $gpa = number_format($totalSubjects->sum('points') / $totalSubjects->sum('credits'), 2, '.', '');
        } else {
            $gpa = 0;
        }

        $passedCredits += $totalSubjects->where('grade', 'T')->where('is_passed', true)->sum('credits');
        return array($passedCredits, $gpa);
    }

    public static function getPassedMarks()
    {
        return ['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C'];
    }

    public function calculateNotices(Student $student, $exceptTerm = null)
    {
        return 0;

        $subjects = $student->studentSubjects()
            ->when($exceptTerm, fn($q) => $q->where('term_id', '!=', $exceptTerm))
            ->whereNotNull('grade')
            ->whereNotIn('grade', ['W', 'I', 'T'])
            ->orderBy('term_id')
            ->orderBy('subject_title_ar')
            ->orderBy('subject_title_en')
            ->get();

        $noticesCount = $student->notices()->sum('value');

        $latestSubjects = collect([]);
        $notices = collect([]);

        $termsSubjects = $subjects
            ->groupBy('term_id');

        foreach ($termsSubjects as $term => $termSubjects) {

            foreach ($termSubjects as $subject) {
                if ($subject->subject_type == Subject::SUPPORTIVE && TranscriptHelper::isFail($subject->grade)) {
                    $notices->push(
                        [
                            'term' => $term,
                            'subject' => $subject->subject_id,
                            'gpa' => false
                        ]
                    );
                }

                if ($subject->subject_type != Subject::SUPPORTIVE && $latestSubjects->where('subject_id', $subject->subject_id)->first() && TranscriptHelper::isFail($subject->grade)) {
                    $notices->push(
                        [
                            'term' => $term,
                            'subject' => $subject->subject_id,
                            'gpa' => false
                        ]
                    );
                }

                $latestSubjects = $latestSubjects
                    ->reject(fn($item) => $item['subject_id'] == $subject->subject_id);

                $latestSubjects->push([
                    'subject_id' => $subject->subject_id,
                    'credit' => $subject->credit,
                    'grade' => $subject->grade,
                    'subject_type' => $subject->subject_type,
                    'term_id' => $subject->term_id,
                ]);

                if (TranscriptHelper::isPass($subject->grade)) {
                    $notices = $notices->reject(fn($item) => isset($item['subject']) && $item['subject'] == $subject->subject_id);
                }
            }

            $totalCredits = $latestSubjects
                ->where('subject_type', '!=', Subject::SUPPORTIVE)
                ->sum('credit');
            $totalPoints = $latestSubjects->where('subject_type', '!=', Subject::SUPPORTIVE)
                ->sum(fn($subject) => $subject['credit'] * TranscriptHelper::markCredits($subject['grade']));
            $totalGPA = $totalCredits ? $totalPoints / $totalCredits : 0;

            if ($termSubjects->where('subject_type', Subject::SUPPORTIVE)->count() != $termSubjects->count()) {
                if ($totalGPA < 3) {
                    $notices->push(
                        [
                            'gpa' => true,
                            'term' => $term,
                        ]
                    );
                } else {
                    $notices = $notices
                        ->reject(fn($item) => (bool)$item['gpa'] == true);
                }
            }
        }

        return $noticesCount + $notices->unique('term')->count();
    }

    public static function isFail($grade)
    {
        return in_array($grade, ['F', 'D', 'D+', 'C-']);
    }

    public static function isPass($grade)
    {
        return !self::isFail($grade);
    }
}
