<?php


namespace Modules\Sms\Services\Students;


use App\Helpers\EntitiesHelper;
use App\Notifications\EmailVerificationNotification;
use App\Repositories\Users\UserRepositoryInterface;
use Illuminate\Support\Facades\Hash;
use Modules\Sms\DTO\StudentData;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentTerm;
use Modules\Sms\Entities\Term;
use Modules\Sms\Entities\Thesis;
use Modules\Sms\Http\Requests\Student\StoreStudentRequest;
use Modules\Sms\Http\Requests\Student\UpdateStudentRequest;
use Modules\Sms\Http\Requests\StudentThesisRequest;
use Modules\Sms\Repositories\Students\StudentRepositoryInterface;

class StudentService
{
    /**
     * @var StudentRepositoryInterface
     */
    private $studentRepository;
    /**
     * @var UserRepositoryInterface
     */
    private $userRepository;

    /**
     * StudentService constructor.
     * @param StudentRepositoryInterface $studentRepository
     * @param UserRepositoryInterface $userRepository
     */
    public function __construct(StudentRepositoryInterface $studentRepository, UserRepositoryInterface $userRepository)
    {

        $this->studentRepository = $studentRepository;
        $this->userRepository = $userRepository;
    }

    public function getAll()
    {
        return $this->studentRepository->getAll();
    }

    public function getPagination(StudentData $studentData)
    {
        return $this->studentRepository->getPagination($studentData);
    }

    public function show(Student $student)
    {
        return $this->studentRepository->show($student->id);
    }

    public function changeStatus($studentId, $status)
    {
        $student = Student::find($studentId);
        $user = $student->user;
        $this->userRepository->changeStatus($user->id, $status);
        return ['message' => 'success', 'code' => 200];
    }

    public function addThesis(Student $student, StudentThesisRequest $request)
    {
        $thesisData = $request->validated();
        $thesisData['thesis_type'] = $student->program()->first()?->type;
        $student->theses()->create($thesisData);

        return ['message' => 'success', 'code' => 200];
    }

    public function create(StoreStudentRequest $request, $status = 'inactive', $emailVerified = false)
    {

        $validatedData = collect($request->validated());
        $validatedData->put('type', 'student');
        $validatedData->put('status', $status);
        $validatedData->put('password', Hash::make($validatedData->get('password')));
        if ($emailVerified) {
            $validatedData->put('email_verified_at', now());
        }
        $userData = $validatedData->except([
            'student_id',
            'program_id',
            'mother_name',
            'birth_place',
            'birth_place_en',
            'work_place',
            'martial_status',
            'sponsor',
            'qualification_id',
            'qualification_university',
            'general_major',
            'specialization',
            'enrollment_type',
        ])->toArray();
        $studentData = $validatedData->only([
            'student_id',
            'program_id',
            'mother_name',
            'birth_place',
            'birth_place_en',
            'work_place',
            'martial_status',
            'sponsor',
            'qualification_id',
            'qualification_university',
            'general_major',
            'specialization',
            'enrollment_type'
        ]);
        $studentData = $studentData->merge(['id' => $studentData->get('student_id')]);
        $studentData = $studentData->except(['student_id'])->toArray();
        //        $studentData = [
        //            'id' => $studentData->get('student_id'),
        //            'program_id' => $studentData->get('program_id'),
        //            'mother_name' => $studentData->get('mother_name'),
        //            'birth_place' => $studentData->get('birth_place'),
        //            'work_place' => $studentData->get('work_place'),
        //            'martial_status' => $studentData->get('martial_status'),
        //            'sponsor' => $studentData->get('sponsor'),
        //        ];
        $user = $this->userRepository->create($userData);
        $student = $this->studentRepository->create($user, $studentData);
        $user->notify(new EmailVerificationNotification());

        $department = $user->entity()->first();

        $term = Term::where('entity_id', $department->parent_entity_id)->whereType($student->program->duration_unit)->active()->first();
        if ($term) {
            $student->terms()->attach([
                $term->id => [
                    'status' => StudentTerm::DEFAULT_STATUS
                ]
            ]);
        }
        return ['message' => 'success', 'code' => 200, 'id' => $student->id];
    }

    public function updateThesis(Student $student, Thesis $thesis, StudentThesisRequest $request)
    {
        $thesisData = $request->validated();
        $thesis->update($thesisData);

        return ['message' => 'success', 'code' => 200];
    }

    public function update(UpdateStudentRequest $request, Student $student)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $validatedData = collect($request->validated());
        if ($request->has('password')) {
            $validatedData->put('password', Hash::make($validatedData->get('password')));
        }
        $userData = $validatedData->when(
            auth()->user()->is_superadmin || auth()->user()->hasRole('general registrar|school registrar|general registrar data entry', $currentEntityId),
            fn($collection) => $collection->except([
                'student_id',
                'program_id',
                'mother_name',
                'birth_place',
                'birth_place_en',
                'work_place',
                'martial_status',
                'sponsor',
                'qualification_id',
                'qualification_university',
                'general_major',
                'specialization',
                'enrollment_type'
            ]),
            fn($collection) => $collection->only([
                'password',
                'national_id',
                'phone_number',
                'address',
                'dob',
                'personal_id'
            ])
        )->toArray();

        $this->userRepository->update($student->user->id, $userData);

        if (auth()->user()->hasRole('general registrar|school registrar|general registrar data entry', $currentEntityId)) {
            $studentData = $validatedData->only([
                'program_id',
                'mother_name',
                'birth_place',
                'birth_place_en',
                'work_place',
                'martial_status',
                'sponsor',
                'qualification_id',
                'qualification_university',
                'general_major',
                'specialization',
                'enrollment_type'
            ])->toArray();

            $studentData = array_merge($studentData, ['id' => $validatedData->get('student_id')]);
        } else {
            $studentData = $validatedData->only([
                'mother_name',
                'martial_status',
                'birth_place',
                'birth_place_en',
                'work_place',
                'qualification_id',
                'qualification_university',
                'general_major',
                'specialization',
                'enrollment_type'
            ])->toArray();
        }
        $this->studentRepository->update($student->id, $studentData);

        return ['message' => 'success', 'code' => 200];
    }

    public function deleteThesis(Student $student, Thesis $thesis)
    {
        $thesis->delete();

        return ['message' => 'success', 'code' => 200];
    }

    public function delete(Student $student)
    {
        $this->studentRepository->delete($student->id);
        return ['message' => 'success', 'code' => 200];
    }
}
