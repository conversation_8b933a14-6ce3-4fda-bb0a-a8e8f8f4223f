<?php


namespace Modules\Sms\Services\Timetables;


use App\Models\EntityType;
use App\Models\Program;
use App\Models\Subject;
use App\Scopes\InDescendingEntities;
use DB;
use Modules\Sms\Entities\Student;
use Modules\Sms\Entities\StudentSubject;
use Modules\Sms\Entities\Timetable;
use Modules\Sms\Entities\TimetableContent;
use Modules\Sms\Http\Requests\Timetable\TimetableContentRequest;
use Modules\Sms\Transformers\Timetables\TimetableContentResource;

class TimetableService
{
    public function getAll()
    {
        $currentEntity = app('current_entity');
        if ($currentEntity->type == EntityType::DEPARTMENT) {
            $currentEntity = $currentEntity->parent;
        }
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        return Timetable::with('term')
            ->whereHas('term', fn($q) => $q->whereIn('entity_id', $entitiesIds))
            ->orderByDesc('id')->get();
    }

    public function getContent($id)
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $programsIds = Program::whereIn('entity_id', $entitiesIds)->get()->pluck('id');
        return Timetable::with([
            'contents' => fn($q) => $q
                ->whereIn('program_id', $programsIds)
                ->with(['subject', 'hall', 'lecturer.user', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)])
        ])->find($id);
    }

    public function createContent($timetableId, TimetableContentRequest $request)
    {
        $data = $request->validated();
        $subject = Subject::find($request->get('subject_id'));
        $data['program_id'] = $subject->program_id;
        $timetable = Timetable::find($timetableId);
        if ($oldContent = $timetable->contents()->where('subject_id', $subject->id)->where('group', $data['group'])->first()) {
            $data['capacity'] = $oldContent->capacity;
        }
        $content = $timetable->contents()->create($data);
        $content->load(['subject', 'hall', 'lecturer.user', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)]);
        return ['message' => 'success', 'code' => 200, 'content' => new TimetableContentResource($content)];
    }

    public function deleteContent($contentId)
    {
        $content = TimetableContent::with('timetable')->find($contentId);

        if (
            !$this->hasMoreGroups($content, $content->subject_id, $content->group)
            && StudentSubject::where('term_id', $content->timetable->term_id)
                ->where('subject_id', $content->subject_id)
                ->where('group', $content->group)
                ->exists()
        ) {
            return ['message' => 'students_registered', 'code' => 200];
        }

        //        if(! $this->hasMoreGroups($content, $content->subject_id, $content->group)) {
        //            StudentSubject::where('term_id', $content->timetable->term_id)
        //                ->where('subject_id', $content->subject_id)
        //                ->where('group', $content->group)
        //                ->delete();
        //        }

        $content->delete();

        return ['message' => 'success', 'code' => 200];
    }

    private function hasMoreGroups(TimetableContent $content, $subjectId, $group)
    {
        return
            TimetableContent::where('id', '!=', $content->id)
                ->where('timetable_id', $content->timetable_id)
                ->where('subject_id', $subjectId)
                ->where('group', $group)
                ->exists();
    }

    public function updateContent($contentId, TimetableContentRequest $request)
    {
        $data = $request->validated();
        $subject = Subject::find($request->get('subject_id'));
        $data['program_id'] = $subject->program_id;
        $content = TimetableContent::with('timetable')->find($contentId);

        if (!$this->hasMoreGroups($content, $content->subject_id, $content->group)) {
            StudentSubject::where('term_id', $content->timetable->term_id)
                ->where('subject_id', $content->subject_id)
                ->where('group', $content->group)
                ->update([
                    'group' => $request->get('group')
                ]);
        }

        $content->update($data);

        TimetableContent::where('timetable_id', $content->timetable_id)
            ->where('subject_id', $content->subject_id)
            ->where('group', $content->group)
            ->where('id', '!=', $content->id)
            ->update([
                'capacity' => $content->capacity
            ]);

        $content->load(['subject', 'hall', 'lecturer.user', 'program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class)]);
        return ['message' => 'success', 'code' => 200, 'content' => new TimetableContentResource($content)];
    }

    public function getSubjects($timetableId, $request)
    {
        $timetable = Timetable::findOrFail($timetableId);
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);
        $programsIds = Program::whereIn('entity_id', $entitiesIds)->get()->pluck('id');

        $search = $request->get('search');
        $sortKey = $request->get('sort_key');
        $sortType = $request->get('sort_type') ?? 'asc';

        $schoolConfirmedQuery = DB::raw('SUM(DISTINCT CASE WHEN confirmed_by = "school" THEN 1 ELSE 0 END) as school_confirmed');
        $departmentConfirmedQuery = DB::raw('SUM(DISTINCT CASE WHEN confirmed_by = "school" or confirmed_by = "department" THEN 1 ELSE 0 END) as department_confirmed');
        $teacherConfirmedQuery = DB::raw('SUM(DISTINCT CASE WHEN confirmed_by IS NOT NULL THEN 1 ELSE 0 END) as teacher_confirmed');

        return TimetableContent::where('timetable_id', $timetableId)
            ->join('timetables', 'timetables.id', '=', 'timetable_contents.timetable_id')
            ->leftJoin('student_subjects', function ($join) {
                $join->on('timetable_contents.subject_id', '=', 'student_subjects.subject_id');
                $join->on('timetable_contents.group', '=', 'student_subjects.group');
                $join->on('timetables.term_id', '=', 'student_subjects.term_id');
            })
            ->leftJoin('appeals', 'student_subjects.id', '=', 'appeals.student_subject_id')
            ->whereIn('program_id', $programsIds)
            ->where('timetable_id', $timetable->id)
            ->with(['program' => fn($q) => $q->withoutGlobalScope(InDescendingEntities::class), 'subject'])
            ->when($search, fn($q) => $q->whereHas('subject', fn($q) => $q->where(function ($q) use ($search) {
                return $q->where('code', 'like', '%' . $search . '%')->orWhere(DB::raw('lower(title)'), "LIKE", "%" . mb_strtolower($search) . "%");
            })))
            ->groupBy(['timetable_contents.subject_id', 'timetable_contents.group', 'capacity'])
            ->select([
                'timetable_contents.subject_id',
                'timetable_contents.group',
                'capacity',
                'program_id',
                DB::raw('count(distinct student_id) as students_count'),
                $schoolConfirmedQuery,
                $departmentConfirmedQuery,
                $teacherConfirmedQuery,
                DB::raw('CASE WHEN COUNT(appeals.id) > 0 THEN 1 ELSE 0 END AS has_appeals'),
            ])
            ->when($sortKey, fn($q) => $q->orderBy($sortKey, $sortType))
            ->orderBy('timetable_contents.group')
            ->paginate($request->get('per_page') ?? 20);
    }

    public function getStudentsOfSubject($subjectId, $term_id, $group, $onlyPaid = false, $withGrades = false, $withoutWithdraw = true, $withAppeals = false, $onlyNotPassed = false)
    {
        return Student::whereHas(
            'studentSubjects',
            fn($q) => $q->where('subject_id', $subjectId)
                ->where('group', $group)
                ->where('term_id', $term_id)
                ->when($onlyPaid, fn($q) => $q->where('is_paid', true))
                ->when($onlyNotPassed, fn($q) => $q->where(function($query) {
                    $query->where('is_passed', false)
                          ->orWhere('is_second_attempt', true);
                }))
        )
            ->when($withoutWithdraw, function ($q) use ($group, $term_id, $subjectId) {
                return $q->whereDoesntHave(
                    'studentSubjects',
                    fn($q) => $q->where('grade', 'W')
                        ->where('subject_id', $subjectId)
                        ->where('group', $group)
                        ->where('term_id', $term_id)
                );
            })
            ->when($withGrades, fn($q) => $q->with([
                'studentSubjects' => fn($q) => $q->where('subject_id', $subjectId)
                    ->where('group', $group)
                    ->where('term_id', $term_id)
                    ->when($onlyPaid, fn($q) => $q->where('is_paid', true))
                    ->when($onlyNotPassed, fn($q) => $q->where(function($query) {
                        $query->where('is_passed', false)
                              ->orWhere('is_second_attempt', true);
                    }))
                    ->when($withAppeals, function ($q) {
                        return $q->with('appeal');
                    })

            ]))
            ->join(config('database.connections.main_db.database') . '.users', 'users.id', '=', config('database.connections.sms_db.database') . '.students.user_id')
            ->orderByRaw('CONCAT(first_name_ar, " ", second_name_ar, " ", third_name_ar, " ", last_name_ar)')
            ->select('students.*')
            ->get();
    }
}
