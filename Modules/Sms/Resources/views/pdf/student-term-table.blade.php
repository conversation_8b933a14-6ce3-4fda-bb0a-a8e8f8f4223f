<table style="margin: 40px auto">
    <thead>
        <tr>
            <th colspan="5" style="text-align: center">
                نتيجة الطالب: {{ $student->user->full_name }}
            </th>
            <th>رقم القيد</th>
            <th>{{ $student->id }}</th>
        </tr>
        <tr>
            <th colspan="5" style="text-align: center">
                {{ $term }}
            </th>
            <th colspan="2" style="text-align: center">
                {{ now()->format('Y/m/d') }}
            </th>
        </tr>
        <tr>
            <th>المادة</th>
            <th>رمز المادة</th>
            <th>الدرجة</th>
            <th>عدد الوحدات</th>
            <th>عدد النقاط</th>
            <th>نتيجة المادة</th>
            <th>ملاحظة</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($subjects as $subject)
            <tr>
                <td>{{ $subject['subject_name'] }}</td>
                <td>{{ $subject['code'] }}</td>
                <td>{{ $subject['full_mark'] }}</td>
                <td>{{ $subject['credits'] }}</td>
                <td>{{ $subject['points'] }}</td>
                <td>{{ $subject['is_passed'] ? 'ناجح' : 'راسب' }}</td>
                <td>{{ $subject['transfer_type'] ? 'معادلة' : '' }}</td>
            </tr>
        @endforeach
    </tbody>
    <tfoot>
        <tr>
            <td>
                الوحدات المسجلة
            </td>
            <td colspan="2">
                {{ collect($subjects)->sum('credits') }}
            </td>
            <td></td>
            <td colspan="2">
                إجمالي النقاط
            </td>
            <td>
                {{ collect($subjects)->sum('points') }}
            </td>
        </tr>
        <tr>
            <td>
                الوحدات المنجزة
            </td>
            <td colspan="2">
                {{ collect($subjects)->where('is_passed')->sum('credits') }}
            </td>
            <td></td>
            <td colspan="2">
                المعدل الفصلي
            </td>
            <td>
                {{ collect($subjects)->sum('credits') > 0 ? number_format(collect($subjects)->sum('points') / collect($subjects)->sum('credits'), 2, '.', '') : '-' }}
            </td>
        </tr>
    </tfoot>
</table>
