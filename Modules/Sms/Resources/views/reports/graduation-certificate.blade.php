<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title></title>

    <style>
        body {
            font-family: 'XBRiyaz';
            /* color: #555; */
            text-align: right;
            direction: rtl;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }

        .certificate td:nth-child(odd) {
            width: 1px;
            white-space: nowrap;
        }

        .certificate table {
            border-collapse: collapse;
        }

        .certificate table,
        .certificate td,
        .certificate th {
            border: 1px solid #555;
            padding: 5px;
        }

        .certificate table th {
            background-color: #ddd;
            white-space: nowrap;
        }

        .certificate p {
            line-height: 1;
        }

        .certificate .line {
            padding: 5px;
            margin-bottom: 5px;
        }

        .certificate .line p {
            margin: 0;
            padding: 0;
        }

        .certificate .line-content {
            float: right;
            width: 50%;
            padding: 0;
            margin: 0;
        }

        .certificate .line span {
            border-bottom: 2px dotted #AAA;
            display: inline-block;
            padding-right: 10px;
            padding-left: 10px;
        }

        .certificate .en-line-content {
            direction: ltr;
            text-align: left;
        }

        @page certificate {
            margin-top: 60mm;
            margin-footer: 15mm;
            footer: cert-page-footer;
        }

        .certificate {
            page: certificate;
        }

        /* final transcript */

        .final_transcript_style h4 {
            font-size: 16px;
            margin-bottom: 0px;
        }

        .final_transcript_style table {
            margin: 5px 0 5px 0;
            padding: 5px;
        }

        .final_transcript_style table.table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
        }

        .final_transcript_style table.table th.heading {
            background: #ddd;
            font-weight: bold;
            padding: 5px;
            text-align: center;
        }

        .final_transcript_style table.table,
        .final_transcript_style table.table td,
        .final_transcript_style table.table th {
            border: 1px solid #555;
            padding: 5px;
        }

        .final_transcript_style table.table th {
            white-space: nowrap;
        }

        .final_transcript_style table.marks-table {
            border-collapse: collapse;
            text-align: center;
            font-size: 11px;
        }

        .final_transcript_style table.marks-table,
        .final_transcript_style table.marks-table td,
        .final_transcript_style table.marks-table th {
            border: 1px solid black;
            padding: 2.5px 10px;
        }

        .final_transcript {
            page: final_transcript;
        }

        @page final_transcript {
            margin-header: 52mm;
            margin-footer: 15mm;
            header: page-header;
            footer: page-footer;
            margin-top: 85mm;
            margin-bottom: 80mm;
        }

        @page :first {
            resetpagenum: 1;
        }
    </style>
</head>

<body>

    <div class="certificate">

        <h2
            style="text-align: center; margin: 0 25% 40px 25%; padding-bottom: 20px; border-bottom: 2px solid; width: 50%">
            إفادة تخرج / Graduation Certificate
        </h2>

        <div style="clear: both;"></div>

        <div>
            <div class="line" style="background-color: #ddd;">
                <p class="line-content"><b>تشهد ،</b> الأكاديمية الليبية بأن
                    :</p>
                <p class="en-line-content line-content"><b>HereBy ,</b> The Libyan
                    Academy
                    Certifies That :</p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content">
                    <b>الطالب :</b>
                    <span>{{ $student->user->first_name_ar }}</span>
                    @if (request()->get('second_name_ar') != 'false')
                        <span>{{ $student->user->second_name_ar }}</span>
                    @endif
                    @if (request()->get('third_name_ar') != 'false')
                        <span>{{ $student->user->third_name_ar }}</span>
                    @endif
                    <span>{{ $student->user->last_name_ar }}</span>
                </p>
                <p class="en-line-content line-content">
                    <b>Student:</b>
                    <span>{{ $student->user->first_name_en }}</span>
                    @if (request()->get('second_name_en') != 'false')
                        <span>{{ $student->user->second_name_en }}</span>
                    @endif
                    @if (request()->get('third_name_en') != 'false')
                        <span>{{ $student->user->third_name_en }}</span>
                    @endif
                    <span>{{ $student->user->last_name_en }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>الجنسية :</b>
                    <span>{{ $student->user->nationality->getTranslation('title', 'ar', false) }}</span>
                </p>
                <p class="en-line-content line-content"><b>Nationality:</b>
                    <span>{{ $student->user->nationality->getTranslation('title', 'en', false) }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>مكان الميلاد :</b> <span>{{ $student->birth_place }}</span></p>
                <p class="en-line-content line-content"><b>Place Of Birth:</b>
                    <span>{{ $student->birth_place_en }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>تاريخ الميلاد :</b>
                    <span>{{ \Carbon\Carbon::createFromFormat('Y-m-d', $student->user->dob)->format('Y/m/d') }}</span>
                </p>
                <p class="en-line-content line-content"><b>Date Of Birth:</b>
                    <span>{{ \Carbon\Carbon::createFromFormat('Y-m-d', $student->user->dob)->format('Y/m/d') }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>قد تحصل على :</b>
                    <span>
                        @if ($student->is_diploma)
                            {{ \App\Models\ProgramType::DIPLOMA_AR }}
                        @else
                            {{ $student->program->programType->getTranslation('title', 'ar', false) }}
                        @endif
                    </span>
                </p>
                <p class="en-line-content line-content"><b>Has Been Awarded:</b>
                    <span>
                        @if ($student->is_diploma)
                            {{ \App\Models\ProgramType::DIPLOMA_EN }}
                        @else
                            {{ $student->program->programType->getTranslation('title', 'en', false) }}
                        @endif
                    </span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>الكلية :</b>
                    <span>{{ Str::replace(['كلية', 'الكلية'], ['', ''], $student->program->entity->parent->getTranslation('title', 'ar', false)) }}</span>
                </p>
                <p class="en-line-content line-content"><b>School:</b>
                    <span>{{ Str::replace(['school of', 'School of'], ['', ''], $student->program->entity->parent->getTranslation('title', 'en', false)) }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>في قسم :</b>
                    <span>{{ Str::replace(['قسم', 'القسم'], ['', ''], $student->program->entity->getTranslation('title', 'ar', false)) }}</span>
                </p>
                <p class="en-line-content line-content"><b>Department:</b>
                    <span>{{ Str::replace(['department of', 'Department of'], ['', ''], $student->program->entity->getTranslation('title', 'en', false)) }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>شعبة :</b>
                    <span>{{ Str::replace(['شعبة', 'الشعبة'], ['', ''], $student->program->getTranslation('title', 'ar', false)) }}</span>
                </p>
                <p class="en-line-content line-content"><b>Division:</b>
                    <span>{{ $student->program->getTranslation('title', 'en', false) }}</span>
                </p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>بمعدل تراكمي :</b><span> {{ $gpa }} من اصل 4.0 نقاط</span></p>
                <p class="en-line-content line-content"><b>GPA:</b> <span>{{ $gpa }} Out Of 4.0</span></p>
            </div>
            <div class="clearfix"></div>

            <div class="line">
                <p class="line-content"><b>في فصل :</b>
                    @if ($lastTerm)
                        <span>{{ __('sms::reports.seasons.' . $lastTerm?->title, [], 'ar') }}
                            {{ $lastTerm?->year }}</span>
                    @endif
                </p>
                <p class="en-line-content line-content"><b>In:</b>
                    @if ($lastTerm)
                        <span>{{ __('sms::reports.seasons.' . $lastTerm?->title, [], 'en') }}
                            {{ $lastTerm?->year }}</span>
                    @endif
                </p>
            </div>
            <div class="clearfix"></div>

            @if (!$student->is_diploma)
                <div class="line">
                    <p class="line-content"><b>نظام الدراسة :</b> <span>
                            @if ($hasPassedThesis)
                                مقررات دراسية وبحث
                            @elseif ($hasThesis)
                                مقررات دراسية وامتحان شامل
                            @else
                                مقررات دراسية فقط
                            @endif
                        </span></p>
                    <p class="en-line-content line-content"><b>Study Track Option:</b>
                        <span>
                            @if ($hasPassedThesis)
                                Course Work And Research
                            @elseif ($hasThesis)
                                Course Work And Overall Exam
                            @else
                                Course Work Only
                            @endif
                        </span>
                    </p>
                </div>
            @endif
            <div class="clearfix"></div>

            @if ($hasPassedThesis)
                <div class="line">
                    <p style="float: right; width: 20%; margin:0; padding: 0;"><b>عنوان الرسالة :</b></p>
                    <p style="float: right; width: 60%; text-align: center;  margin:0; padding: 0; line-height: 2;">
                        <span>{{ $thesis->title_ar }}</span>
                        @if ($thesis->title_ar != '')
                            <br />
                        @endif
                        @if ($thesis->title_ar != $thesis->title_en)
                            <span>{{ $thesis->title_en }}</span>
                        @endif
                    </p>
                    <p style="float: right; width: 20%; text-align: left; margin:0; padding: 0;">
                        <b>
                            :Dissertation Title
                        </b>
                    </p>
                </div>
                <div class="clearfix"></div>
            @endif

        </div>

        <div style="margin-top: 30px;">
            <div style="float: right; width:50%; text-align: center;">
                <p style="padding-bottom: 10px">....................................................</p>
                <p style=""></p>
                <p style="; font-weight: bold;">{{ $generalRegistrarName ?? '-' }}</p>
                <p style="">المسجل العام</p>
                <p style="">General Registrar</p>
            </div>
            <div style="float: right; width:50%; text-align: center;">
                <p style="padding-bottom: 10px">....................................................</p>
                <p style="">يعتمد</p>
                <p style="; font-weight: bold;">{{ $academyPresidentName ?? '-' }}</p>
                <p style="">رئيس الأكاديمية</p>
                <p style="">The Academy President</p>
            </div>
            <div style="clear: both; padding-bottom: 40px;"></div>
        </div>

        <htmlpagefooter name="cert-page-footer">
            <div style="float: right; width: 80%; padding-top: 60px;">صدرت بتاريخ: {{ now()->format('Y/m/d') }}</div>
            <div style="float: right; width: 20%; text-align: center">
                @php $qrcode = date('d') . date('m') . $student->id . date('y') @endphp
                <img src="/qrcode?data={{ $qrcode }}&size=65x65" height="65" width="65" />
                <p style="margin: 0;">{{ $qrcode }}</p>
            </div>
        </htmlpagefooter>

    </div>

    <div class="final_transcript final_transcript_style">
        <htmlpageheader name="page-header">
            <div class="final_transcript_style">
                <div style="text-align: center">
                    <h4>
                        كشف درجات الطالب:
                        {{ $student->user->first_name_ar }}
                        @if (request()->get('second_name_ar') != 'false')
                            {{ $student->user->second_name_ar }}
                        @endif
                        @if (request()->get('third_name_ar') != 'false')
                            {{ $student->user->third_name_ar }}
                        @endif
                        {{ $student->user->last_name_ar }}
                    </h4>
                </div>
                <div style="clear: both; border-bottom: 2px solid; padding: 5px 0;"></div>
                <div>
                    <table>
                        <tbody>
                            <tr>
                                <td style="width:50%">
                                    المعدل التراكمي العام: <b>{{ $gpa }}</b>
                                </td>
                                <td>
                                    الدرجة العلمية: <b>
                                        @if ($student->is_diploma)
                                            {{ \App\Models\ProgramType::DIPLOMA_AR }}
                                        @else
                                            {{ $student->program->programType->title }}
                                        @endif
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    الساعات المنجزة: <b>{{ $passedCredits }}</b>
                                </td>
                                <td>
                                    القسم:
                                    <b>{{ Str::replace(['قسم', 'القسم'], ['', ''], $student->program->entity->getTranslation('title', 'ar', false)) }}</b>
                                </td>
                            </tr>
                            <tr>
                                @if (!$student->is_diploma)
                                    <td>
                                        نظام الدراسة:
                                        <b>
                                            @if ($hasPassedThesis)
                                                مقررات دراسية وبحث
                                            @elseif ($hasThesis)
                                                مقررات دراسية وامتحان شامل
                                            @else
                                                مقررات دراسية فقط
                                            @endif
                                        </b>
                                    </td>
                                @endif
                                <td>
                                    التخصص:
                                    <b>{{ Str::replace(['شعبة', 'الشعبة'], ['', ''], $student->program->getTranslation('title', 'ar', false)) }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="clear: both;"></div>
                </div>
            </div>
        </htmlpageheader>

        @foreach ($terms as $term)
            <table class="table" style="width: 100%">
                <thead>
                    <tr>
                        <th class="heading" colspan="7">الفصل الدراسي ( {{ $term['name'] }} )</th>
                    </tr>
                    <tr>
                        <th style="width: 5%">م</th>
                        <th style="width: 35%">اسم المادة</th>
                        <th style="width: 13%">عدد الساعات</th>
                        <th style="width: 10%">التقدير</th>
                        <th style="width: 7%">النقاط</th>
                        <th style="width: 15%">المعدل الفصلي</th>
                        <th style="width: 15%">المعدل العام</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($term['subjects'] as $subject)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td style="text-align: right">{{ $subject['subject_name'] }}</td>
                            <td>{{ $subject['credits'] }}</td>
                            <td dir="ltr">{{ $subject['grade'] }}</td>
                            <td>{{ $subject['points'] }}</td>
                            @if ($loop->first)
                                <td rowspan="{{ count($term['subjects']) }}"><b>{{ $term['term_gpa'] }}</b></td>
                                <td rowspan="{{ count($term['subjects']) }}"><b>{{ $term['total_gpa'] }}</b></td>
                            @endif
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endforeach
        {{-- <div style="clear: both;"></div> --}}

        <htmlpagefooter name="page-footer">
            <div class="final_transcript_style">

                <div style="padding-bottom: 20px;">
                    <div style="float: right; width:50%; text-align: center;">
                        <p style="padding-bottom: 10px">....................................................</p>
                        <p></p>
                        <p style="font-weight: bold; margin: 0;">{{ $generalRegistrarName ?? '-' }}
                        </p>
                        <p style="margin: 0;">المسجل العام</p>
                        <p style="margin: 0;">General Registrar</p>
                    </div>
                    <div style="float: right; width:50%; text-align: center;">
                        <p style="padding-bottom: 10px">....................................................</p>
                        <p style="font-weight: bold; margin: 0;">{{ $academyPresidentName ?? '-' }}
                        </p>
                        <p style="margin: 0;">رئيس الأكاديمية </p>
                        <p style="margin: 0;">The Academy President</p>
                    </div>
                    <div style="clear: both;"></div>
                </div>

                <div style="padding-top: 5px; border-top: 2px solid;" />
                <div style="float: left; width: 20%; padding-top: 10px; text-align: center">
                    @php $qrcode = date('d') . date('m') . $student->id . date('y') @endphp
                    <img src="/qrcode?data={{ $qrcode }}" width="65" height="65" />
                    <p style="margin: 0;">{{ $qrcode }}</p>
                </div>
                <div style="float: right; width: 80%">

                    <div style="font-size: 12px;">
                        <p style="margin:0; padding: 0;">ملحوظة : الحد الأدنى للتخرج (3.00) من أصل (4.00) نقاط.</p>
                        <p style="margin:0; padding: 0;">
                            ملحوظة : الرمز T يعني مادة منتقل بها من جامعة أخرى وأعتمدت من قبل الأكاديمية.
                        </p>
                        <table class="marks-table">
                            <tbody>
                                <tr>
                                    <td>الرمز</td>
                                    <td>A</td>
                                    <td>A-</td>
                                    <td>B+</td>
                                    <td>B</td>
                                    <td>B-</td>
                                    <td>C+</td>
                                    <td>C</td>
                                    <td>C-</td>
                                    <td>D+</td>
                                    <td>D</td>
                                    <td>F</td>
                                    <td>I</td>
                                    <td>W</td>
                                </tr>
                                <tr>
                                    <td>النقاط</td>
                                    <td>4</td>
                                    <td>3.7</td>
                                    <td>3.5</td>
                                    <td>3</td>
                                    <td>2.7</td>
                                    <td>2.5</td>
                                    <td>2</td>
                                    <td>1.7</td>
                                    <td>1.5</td>
                                    <td>1</td>
                                    <td>0</td>
                                    <td>غير مكمل</td>
                                    <td>منسحب</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div style="float: right; width: 50%; font-size: 12px;">الصفحة: {PAGENO} من {nbpg}</div>
            </div>
        </htmlpagefooter>
    </div>


</body>

</html>
