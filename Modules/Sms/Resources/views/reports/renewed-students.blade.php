@extends('sms::layouts.report', ['title' => 'احصائيات تجديد القيد'])

@section('content')
    <table style="width: 100%">
        <thead>
            <tr>
                <th>ر.ت.</th>
                <th>الكلية</th>
                <th>مجموع الكلية</th>
                <th>القسم</th>
                <th>مجموع القسم</th>
                <th>الشعبة</th>
                <th>مجموع الشعبة</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($schools as $school)
                @foreach ($school->departments as $department)
                    @foreach ($department->programs as $program)
                        <tr>

                            @if ($loop->first && $loop->parent->first)
                                <th rowspan="{{ $school->departments->sum(fn($d) => $d->programs->count()) }}">
                                    {{ $loop->parent->parent->iteration }}
                                </th>
                                <td rowspan="{{ $school->departments->sum(fn($d) => $d->programs->count()) }}">
                                    {{ $school->getTranslation('title', 'ar', false) }}
                                </td>
                                <td rowspan="{{ $school->departments->sum(fn($d) => $d->programs->count()) }}">
                                    {{ $school->s_count }}
                                </td>

                            @endif

                            @if ($loop->first)
                                <td rowspan="{{ $department->programs->count() }}">{{ $department->getTranslation('title', 'ar', false) }}</td>
                                <td rowspan="{{ $department->programs->count() }}">{{ $department->s_count }}</td>

                            @endif

                            <td>{{ $program->getTranslation('title', 'ar', false) }}</td>

                            <td>{{ $program->s_count }}</td>
                        </tr>
                    @endforeach
                @endforeach
            @endforeach

        </tbody>
    </table>
@endsection
