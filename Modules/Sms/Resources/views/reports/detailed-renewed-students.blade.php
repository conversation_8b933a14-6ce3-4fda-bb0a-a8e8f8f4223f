@extends('sms::layouts.report', ['title' => 'احصائيات تجديد القيد'])

@push('css')
    <style>
        table {
            margin-bottom: 2rem;
        }

        td {
            text-align: center
        }

        td.total {
            font-weight: bold
        }

    </style>
@endpush

@section('content')
    <table style="width: 100%">
        <thead>
            <tr>
                <th colspan="11">
                    @lang('website::global.app_name')
                </th>
            </tr>
            <tr>
                <th rowspan="2">ر.ت.</th>
                <th rowspan="2">الاسم</th>
                <th colspan="3">الكل</th>
                <th colspan="3">الليبيين</th>
                <th colspan="3">الغيير اليبيين</th>
            </tr>
            <tr>
                <th>المجموع</th>
                <th>الذكور</th>
                <th>الإناث</th>
                <th>المجموع</th>
                <th>الذكور</th>
                <th>الإناث</th>
                <th>المجموع</th>
                <th>الذكور</th>
                <th>الإناث</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($schools as $school)
                <tr>
                    <th>{{ $loop->iteration }}</th>
                    <td>{{ $school->getTranslation('title', 'ar', false) }}</td>
                    <td class="total">{{ $school->total_count }}</td>
                    <td>{{ $school->total_male_count }}</td>
                    <td>{{ $school->total_female_count }}</td>
                    <td class="total">{{ $school->libyan_count }}</td>
                    <td>{{ $school->libyan_male_count }}</td>
                    <td>{{ $school->libyan_female_count }}</td>
                    <td class="total">{{ $school->non_libyan_count }}</td>
                    <td>{{ $school->non_libyan_male_count }}</td>
                    <td>{{ $school->non_libyan_female_count }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <pagebreak />

    @foreach ($schools as $school)
        <table style="width: 100%">
            <thead>
                <tr>
                    <th colspan="11">
                        {{ $school->getTranslation('title', 'ar', false) }}
                    </th>
                </tr>
                <tr>
                    <th rowspan="2">ر.ت.</th>
                    <th rowspan="2">الاسم</th>
                    <th colspan="3">الكل</th>
                    <th colspan="3">الليبيين</th>
                    <th colspan="3">الغيير اليبيين</th>
                </tr>
                <tr>
                    <th>المجموع</th>
                    <th>الذكور</th>
                    <th>الإناث</th>
                    <th>المجموع</th>
                    <th>الذكور</th>
                    <th>الإناث</th>
                    <th>المجموع</th>
                    <th>الذكور</th>
                    <th>الإناث</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($school->departments as $department)
                    <tr>
                        <th>{{ $loop->iteration }}</th>
                        <td>{{ $department->getTranslation('title', 'ar', false) }}</td>
                        <td class="total">{{ $department->total_count }}</td>
                        <td>{{ $department->total_male_count }}</td>
                        <td>{{ $department->total_female_count }}</td>
                        <td class="total">{{ $department->libyan_count }}</td>
                        <td>{{ $department->libyan_male_count }}</td>
                        <td>{{ $department->libyan_female_count }}</td>
                        <td class="total">{{ $department->non_libyan_count }}</td>
                        <td>{{ $department->non_libyan_male_count }}</td>
                        <td>{{ $department->non_libyan_female_count }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        @foreach ($school->departments as $department)
            <table style="width: 100%">
                <thead>
                    <tr>
                        <th colspan="11">
                            {{ $department->getTranslation('title', 'ar', false) }}
                        </th>
                    </tr>
                    <tr>
                        <th rowspan="2">ر.ت.</th>
                        <th rowspan="2">الاسم</th>
                        <th colspan="3">الكل</th>
                        <th colspan="3">الليبيين</th>
                        <th colspan="3">الغيير اليبيين</th>
                    </tr>
                    <tr>
                        <th>المجموع</th>
                        <th>الذكور</th>
                        <th>الإناث</th>
                        <th>المجموع</th>
                        <th>الذكور</th>
                        <th>الإناث</th>
                        <th>المجموع</th>
                        <th>الذكور</th>
                        <th>الإناث</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($department->programs as $program)
                        <tr>
                            <th>{{ $loop->iteration }}</th>
                            <td>{{ $program->getTranslation('title', 'ar', false) }}</td>
                            <td class="total">{{ $program->total_count }}</td>
                            <td>{{ $program->total_male_count }}</td>
                            <td>{{ $program->total_female_count }}</td>
                            <td class="total">{{ $program->libyan_count }}</td>
                            <td>{{ $program->libyan_male_count }}</td>
                            <td>{{ $program->libyan_female_count }}</td>
                            <td class="total">{{ $program->non_libyan_count }}</td>
                            <td>{{ $program->non_libyan_male_count }}</td>
                            <td>{{ $program->non_libyan_female_count }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endforeach

        @if (!$loop->last)
            <pagebreak />
        @endif

    @endforeach
@endsection
