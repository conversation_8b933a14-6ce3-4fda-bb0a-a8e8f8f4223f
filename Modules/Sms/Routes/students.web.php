<?php

Route::group([
    'prefix' => '/sms',
    'middleware' => ['auth:sanctum', 'type:department', 'user_type:student', 'in_user_entity'],
], function () {

    Route::get('/reports/suspend-registration', [\Modules\Sms\Http\Controllers\Student\ReportsController::class, 'suspendRegistration']);
    Route::get('/reports/withdraw', [\Modules\Sms\Http\Controllers\Student\ReportsController::class, 'withdraw']);
    Route::get('/reports/incomplete', [\Modules\Sms\Http\Controllers\Student\ReportsController::class, 'incomplete']);
    Route::get('/reports/file-withdrawal-form', [\Modules\Sms\Http\Controllers\Student\ReportsController::class, 'fileWithdrawalForm']);
});
