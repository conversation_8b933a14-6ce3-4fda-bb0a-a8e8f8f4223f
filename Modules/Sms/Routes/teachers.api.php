<?php

Route::group([
    'prefix' => '/lecturer-panel',
    'middleware' => ['auth:sanctum', 'type:department', 'user_type:lecturer', 'in_user_entity'],
], function () {
    Route::get('/active-timetable', [\Modules\Sms\Http\Controllers\Teacher\ActiveTimetableController::class, 'index']);
    Route::get('/active-term', [\Modules\Sms\Http\Controllers\Teacher\ActiveTermSubjectsController::class, 'index']);

    Route::get('/subjects/{subject}', [\Modules\Sms\Http\Controllers\Teacher\SubjectController::class, 'show']);

    Route::get('/subjects/{subject}/grades', [\Modules\Sms\Http\Controllers\Teacher\SubjectGradesConfirmationController::class, 'index']);
    Route::post('/subjects/{subject}/grades', [\Modules\Sms\Http\Controllers\Teacher\SubjectGradesConfirmationController::class, 'confirm']);
    Route::put('/subjects/{subject}/grades', [\Modules\Sms\Http\Controllers\Teacher\SubjectGradesConfirmationController::class, 'update']);
    Route::put('/subjects/{subject}/second-attempt', [\Modules\Sms\Http\Controllers\Teacher\SubjectGradesConfirmationController::class, 'updateSecondAttempt']);

    Route::get('/subjects/{subject}/appeals', [\Modules\Sms\Http\Controllers\Teacher\AppealController::class, 'index']);
    Route::put('/subjects/{subject}/appeals', [\Modules\Sms\Http\Controllers\Teacher\AppealController::class, 'update']);

    Route::get('/subjects/{subject}/second-attempts', [\Modules\Sms\Http\Controllers\Teacher\SecondAttemptController::class, 'index']);
    Route::put('/subjects/{subject}/second-attempts', [\Modules\Sms\Http\Controllers\Teacher\SecondAttemptController::class, 'update']);
});
