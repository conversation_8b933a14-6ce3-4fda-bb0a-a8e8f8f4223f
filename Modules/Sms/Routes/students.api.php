<?php

use Modules\Sms\Http\Controllers\Student\ActiveTimetableController;
use Modules\Sms\Http\Controllers\Student\AppealsController;
use Modules\Sms\Http\Controllers\Student\EnrollmentController;
use Modules\Sms\Http\Controllers\Student\NoticeController;
use Modules\Sms\Http\Controllers\Student\TermController;
use Modules\Sms\Http\Controllers\Student\TermSubjectController;
use Modules\Sms\Http\Controllers\Student\TimetableController;

Route::group([
    'prefix' => '/sms',
    'middleware' => ['auth:sanctum', 'type:department', 'user_type:student', 'in_user_entity'],
], function () {

    Route::get('/active-timetable', [ActiveTimetableController::class, 'index']);

    Route::get('/student-terms', [TermController::class, 'index']);

    Route::get('/student-terms/{term}/subjects', [TermSubjectController::class, 'index']);

    Route::get('/notices', [NoticeController::class, 'index']);

    Route::get('/timetable', [TimetableController::class, 'index']);

    Route::post('/student-subjects/{student_subject}/appeal', [AppealsController::class, 'store']);

    Route::post('/enrollment', [EnrollmentController::class, 'store']);
    Route::delete('/enrollment/{subject}', [EnrollmentController::class, 'destroy']);
});
