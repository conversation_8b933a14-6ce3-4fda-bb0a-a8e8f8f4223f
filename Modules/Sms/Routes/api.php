<?php

use App\Helpers\EntitiesHelper;
use Modules\Sms\Http\Controllers\AppealController;
use Modules\Sms\Http\Controllers\CheckStageController;
use Modules\Sms\Http\Controllers\ConfirmSubjectGradeController;
use Modules\Sms\Http\Controllers\DashboardController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\BuildingController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\LecturerController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentRegistrationController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentWithdrawController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\SubjectController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\TermSubjectController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\TimetableContentController;
use Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\TimetableController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\StudentsGraduationController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\StudentSubjectController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\StudentThesisController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\TermController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\TermSettingController;
use Modules\Sms\Http\Controllers\GeneralRegistrar\TransferStudentController;
use Modules\Sms\Http\Controllers\NoticeTypeController;
use Modules\Sms\Http\Controllers\Student\NoticeController;
use Modules\Sms\Http\Controllers\StudentApprovalController;
use Modules\Sms\Http\Controllers\StudentController;
use Modules\Sms\Http\Controllers\StudentTermController;
use Modules\Sms\Http\Controllers\StudentTimetableController;
use Modules\Sms\Http\Controllers\TermDetailsController;
use Modules\Sms\Http\Controllers\TermStageController;
use Modules\Sms\Http\Middleware\StudentTermIsRenewed;

$currentEntityId = EntitiesHelper::getEntityId();

Route::get('/sms/stages-statuses', [CheckStageController::class, 'index'])
    ->middleware('auth:sanctum');

Route::group([
    'prefix' => '/sms',
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|general registrar data entry|school registrar,' . $currentEntityId
    ]
], function () {
    Route::post('/students', [StudentController::class, 'store']);
    Route::put('/students/{student}/mark-graduated', [StudentsGraduationController::class, 'update']);
    Route::put('/students/{student}/mark-ungraduated', [StudentsGraduationController::class, 'ungraduate']);
});

Route::group([
    'prefix' => '/sms',
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|study and exams|department head|general registrar data entry|school registrar|monitor,' . $currentEntityId
    ]
], function () {
    Route::get('/dashboard', [DashboardController::class, 'index']);
});

Route::group([
    'prefix' => '/sms',
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|study and exams|department head|general registrar data entry|school registrar|monitor,' . $currentEntityId
    ]
], function () {
    Route::get('/students', [StudentController::class, 'index']);
    Route::get('/students/{student}', [StudentController::class, 'show']);
    Route::put('/students/{student}', [StudentController::class, 'update']);
    Route::get('/students/{student}/subjects', [StudentSubjectController::class, 'allSubjects']);
    Route::get('/students/{student}/terms', [StudentTermController::class, 'index']);
    Route::get('/students/{id}/notices', [NoticeController::class, 'index']);
    Route::get('/notice-types', [NoticeTypeController::class, 'index']);
    Route::get('/graduation-requests', [StudentsGraduationController::class, 'index']);
    Route::get('/graduated-students', [StudentsGraduationController::class, 'graduatedStudents']);
    Route::get('/students/{student}/terms/{term}/subjects', [StudentSubjectController::class, 'index']);
    Route::get('/students/{student}/theses', [StudentThesisController::class, 'index']);
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|study and exams|department head|school registrar|general registrar data entry|monitor,' . $currentEntityId
    ]
], function () {
    Route::resource('/timetables', TimetableController::class);
    Route::get('/terms', [TermController::class, 'index']);
    Route::get('/terms/{term}', [TermController::class, 'show']);

    Route::group([
        'prefix' => '/sms',
    ], function () {
        Route::get('/buildings', [BuildingController::class, 'index']);
        Route::get('/subjects', [SubjectController::class, 'index']);
        Route::get('/lecturers', [LecturerController::class, 'index']);

        Route::get('/terms/{term}/subjects', [TermSubjectController::class, 'index']);
        Route::get('/terms/{term}/subjects/{subject}', [TermSubjectController::class, 'show']);

        Route::get('/students/{student}/terms/{term}/timetable', [StudentTimetableController::class, 'index']);

        Route::put('/students/{student}/change-status', [StudentApprovalController::class, 'update']);

        Route::post('/terms/{term}/subjects/{subject}/disenroll', [TermSubjectController::class, 'disenroll']);
    });
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'type:school',
        'role:study and exams,' . $currentEntityId
    ]
], function () {
    Route::post('/terms', [TermController::class, 'store']);
    Route::get('/term-stages', [TermStageController::class, 'index']);
    Route::put('/terms/{term}', [TermController::class, 'update']);
    Route::delete('/terms/{term}', [TermController::class, 'destroy']);
    Route::put('/terms/{term}/change-status', [TermController::class, 'changeStatus']);
    Route::get('/terms/{term}/settings', [TermSettingController::class, 'index']);
    Route::put('/terms/{term}/settings', [TermSettingController::class, 'update']);
    Route::post('/terms/{term}/delete-none-paid-students', [TermController::class, 'deleteNonPaidStudents']);

    Route::resource('terms.stages', TermDetailsController::class)->scoped();

    Route::post('import-grades', [\Modules\Sms\Http\Controllers\ImportGradesController::class, 'store']);

    Route::get('/sms/terms/{term}/subjects/{subject}/appeals', [AppealController::class, 'index']);
    Route::post('/sms/terms/{term}/subjects/{subject}/confirm-appeals', [AppealController::class, 'confirm']);
});

Route::post('/register/student', [\Modules\Sms\Http\Controllers\Auth\StudentRegisterController::class, 'register'])->middleware('type:university');

Route::group([
    'middleware' => [
        'auth:sanctum',
    ],
    'prefix' => '/sms'
], function () use($currentEntityId) {
    Route::post('/students/{student}/internal-transfer', [TransferStudentController::class, 'internal'])->middleware('role:general registrar|study and exams,' . $currentEntityId);
    Route::post('/students/{student}/outer-transfer', [TransferStudentController::class, 'outer'])->middleware('role:general registrar,' . $currentEntityId);
    Route::delete('/students/{student}', [StudentController::class, 'destroy'])->middleware('role:general registrar,' . $currentEntityId);
});

// general registrar in university level only
Route::group(
    [
        'middleware' => [
            'type:school',
            'auth:sanctum',
            'role:study and exams|school registrar,' . $currentEntityId
        ]
    ],
    function () {
        Route::group(
            ['prefix' => '/sms'],
            function () {
                Route::post('/students/{student}/terms/{term}/subjects', [StudentSubjectController::class, 'store']);
                Route::put(
                    '/students/{student}/terms/{term}/subjects/{subject}',
                    [StudentSubjectController::class, 'update']
                );
                Route::delete(
                    '/students/{student}/terms/{term}/subjects/{subject_id}',
                    [StudentSubjectController::class, 'destroy']
                );
                Route::put(
                    '/students/{student}/terms/{term}/replace-subject',
                    [StudentSubjectController::class, 'replace']
                );
                Route::put(
                    '/students/{student}/terms/{term}/replace-subject-group',
                    [StudentSubjectController::class, 'replaceSubjectGroup']
                );

                Route::post('/students/{student}/terms', [StudentTermController::class, 'store']);
                Route::put('/students/{student}/terms/{term}', [StudentTermController::class, 'update']);
                Route::delete('/students/{student}/terms/{term}', [StudentTermController::class, 'destroy']);

                Route::post('/students/{student}/notices', [NoticeController::class, 'store']);
            }
        );
    }
);


Route::group([
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|school registrar|general registrar data entry,' . $currentEntityId
    ],
    'prefix' => '/sms'
], function () {
    Route::get('/students/{student}/theses/{thesis}', [StudentThesisController::class, 'show']);
    Route::post('/students/{student}/theses', [StudentThesisController::class, 'store']);
    Route::put('/students/{student}/theses/{thesis}', [StudentThesisController::class, 'update']);
    Route::delete('/students/{student}/theses/{thesis}', [StudentThesisController::class, 'destroy']);
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'role:study and exams|department head,' . $currentEntityId
    ]
], function () use ($currentEntityId) {
    Route::get('/timetables/{timetable}/content', [TimetableContentController::class, 'index']);

    Route::group([
        'prefix' => '/sms'
    ], function () {
        Route::post('/subjects/{subject}/confirm', [ConfirmSubjectGradeController::class, 'store']);
        Route::delete('/subjects/{subject}/confirm', [ConfirmSubjectGradeController::class, 'destroy']);
        Route::post('/subjects/{subject}/confirm-second-attempt', [ConfirmSubjectGradeController::class, 'confirmSecondAttempt']);
    });
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'role:general registrar|school registrar,' . $currentEntityId
    ]
], function () use ($currentEntityId) {
    Route::put(
        '/sms/students/{student}/terms/{term}/subjects/{subject}/mark-as-paid',
        [StudentSubjectController::class, 'markAsPaid']
    );
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'type:department',
        'role:department head,' . $currentEntityId
    ]
], function () use ($currentEntityId) {
    Route::resource('timetables.content', TimetableContentController::class)
        ->only('store', 'update', 'destroy')
        ->scoped();

    Route::post('/timetables/{timetable}/content', [TimetableContentController::class, 'store']);
    Route::put('/timetables/{timetable}/content/{content}', [TimetableContentController::class, 'update']);
    Route::delete('/timetables/{timetable}/content/{content}', [TimetableContentController::class, 'destroy']);

    Route::group([
        'prefix' => '/sms',

    ], function () {
        Route::post(
            '/students/{student}/subjects',
            [\Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentSubjectController::class, 'store']
        );
        Route::delete(
            '/students/{student}/subjects/{subject}',
            [\Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentSubjectController::class, 'destroy']
        );
        Route::put(
            '/students/{student}/replace-subject',
            [\Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentSubjectController::class, 'replace']
        );
    });

    Route::put('/sms/students/{student}/replace-subject-group', [
        \Modules\Sms\Http\Controllers\DepartmentHeadAndStudyExams\StudentSubjectController::class,
        'replaceSubjectGroup'
    ]);
});

Route::group([
    'middleware' => [
        'auth:sanctum',
        'role:school registrar,' . $currentEntityId,
        StudentTermIsRenewed::class
    ],
    'prefix' => '/sms',
], function () use ($currentEntityId) {
    Route::group([], function () {
        Route::delete('/students/{student}/subjects/{subject}/withdraw', [StudentWithdrawController::class, 'destroy']);
    });

    Route::group([], function () {
        Route::delete('/students/{student}/total-withdraw', [StudentWithdrawController::class, 'totalWithdraw']);
    });

    Route::group([], function () {
        Route::delete('/students/{student}/suspend-registration', [StudentRegistrationController::class, 'destroy']);
    });
});

//Generating Emails Routes
//Route::post('/request-academic-email', [\Modules\Sms\Http\Controllers\AcademicEmailController::class, 'requestEmail']);
