<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTimetableContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->create('timetable_contents', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('timetable_id');
            $table->unsignedInteger('subject_id');
            $table->char('group', 1);
            $table->tinyInteger('lecture_type');
            $table->unsignedInteger('lecturer_id');
            $table->unsignedInteger('hall_id');
            $table->tinyInteger('capacity');
            $table->time('start_time');
            $table->time('end_time');
            $table->tinyInteger('day');
            $table->timestamps();

            $table->foreign('timetable_id')->references('id')->on('timetables')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('subject_id')->references('id')->on(config('database.connections.main_db.database') . '.subjects')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('lecturer_id')->references('id')->on(config('database.connections.main_db.database') . '.lecturers')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('hall_id')->references('id')->on(config('database.connections.main_db.database') . '.halls')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('sms_db')->dropIfExists('timetable_contents');
    }
}
