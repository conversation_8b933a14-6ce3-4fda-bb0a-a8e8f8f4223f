<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RestrictForeignKeysInTimetableContents extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('timetable_contents', function (Blueprint $table) {

            $table->dropForeign('timetable_contents_lecturer_id_foreign');
            $table->dropForeign('timetable_contents_program_id_foreign');
            $table->dropForeign('timetable_contents_subject_id_foreign');
            $table->dropForeign('timetable_contents_timetable_id_foreign');
            $table->dropForeign('timetable_contents_hall_id_foreign');


            $table->foreign('timetable_id')->references('id')->on('timetables')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('subject_id')->references('id')->on(config('database.connections.main_db.database') . '.subjects')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('lecturer_id')->references('id')->on(config('database.connections.main_db.database') . '.lecturers')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('hall_id')->references('id')->on(config('database.connections.main_db.database') . '.halls')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('program_id')->references('id')->on(config('database.connections.main_db.database') . '.programs')->onDelete('restrict')->onUpdate('restrict');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
