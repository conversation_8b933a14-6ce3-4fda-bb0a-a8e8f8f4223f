<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTermDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->create('term_details', function (Blueprint $table) {
            $table->unsignedInteger('term_id');
            $table->unsignedInteger('stage_id');
            $table->string('title');
            $table->date('start_date');
            $table->date('end_date');
            $table->timestamps();

            $table->foreign('term_id')->references('id')->on('terms')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('stage_id')->references('id')->on('term_stages')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('sms_db')->dropIfExists('term_details');
    }
}
