<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddQualificationsColumnsToStudentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('students', function (Blueprint $table) {
            $table->unsignedInteger('qualification_id')->nullable()->after('sponsor');
            $table->string('qualification_university')->nullable()->after('qualification_id');
            $table->string('general_major')->nullable()->after('qualification_university');
            $table->string('specialization')->nullable()->after('general_major');

            $table->foreign('qualification_id')->references('id')->on(config('database.connections.main_db.database') . '.qualifications');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('')->table('students', function (Blueprint $table) {

        });
    }
}
