<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangeTablesTitlesToJsonFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('terms', function (Blueprint $table) {
            $table->json('title')->change();
        });

        Schema::connection(config('database.sms_connection'))->table('term_stages', function (Blueprint $table) {
            $table->json('title')->change();
        });

        Schema::connection(config('database.sms_connection'))->table('timetables', function (Blueprint $table) {
            $table->json('title')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
