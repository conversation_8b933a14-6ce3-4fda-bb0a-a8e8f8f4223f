<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSecondAttemptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->create('second_attempts', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('student_subject_id');

            $table->double('old_mid_mark');
            $table->double('old_practical_final_mark');
            $table->double('old_final_mark');

            $table->double('mid_mark')->nullable();
            $table->double('practical_final_mark')->nullable();
            $table->double('final_mark')->nullable();

            $table->text('note')->nullable();
            $table->string('status')->default('pending');

            $table->timestamps();

            $table->foreign('student_subject_id')->references('id')->on('student_subjects')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.sms_connection'))->dropIfExists('second_attempts');
    }
}
