<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTranscriptDataToStudentSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('student_subjects', function (Blueprint $table) {
            $table->string('term_title')->nullable()->after('term_id');
            $table->string('term_year')->nullable()->after('term_title');
            $table->string('subject_title_ar')->nullable()->after('subject_id');
            $table->string('subject_title_en')->nullable()->after('subject_title_ar');
            $table->string('subject_code')->nullable()->after('subject_title_en');
            $table->unsignedTinyInteger('subject_type')->nullable()->after('subject_code');
            $table->string('credit')->after('subject_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
