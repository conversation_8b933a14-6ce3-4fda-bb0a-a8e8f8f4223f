<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPracticalFinalMarkToStudentSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.sms_connection'))->table('student_subjects', function (Blueprint $table) {
            $table->double('practical_final_mark')->after('mid_mark')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.sms_connection'))->table('student_subjects', function (Blueprint $table) {
            $table->dropColumn('practical_final_mark');
        });
    }
}
