<?php

namespace Modules\Sms\Transformers\Terms;

use Illuminate\Http\Resources\Json\JsonResource;

class TermSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'title' => $this->title,
            'year' => $this->year,
            'settings' => $this->settings,
            'timetable_settings' => [
                'start_time' => $this->timetable->start_time->format('H:i:s'),
                'end_time' => $this->timetable->end_time->format('H:i:s'),
                'duration' => $this->timetable->duration
            ],
        ];
    }
}
