<?php

namespace Modules\Sms\Transformers\Terms;

use App\Http\Resources\Entity\SimpleEntityResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TermResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'year' => $this->year,
            'status' => $this->status,
            'study_plan' => $this->study_plan,
            'entity' => SimpleEntityResource::make($this->whenLoaded('entity')),
            'created_at' => $this->created_at?->format('Y-m-d H:i'),
            'stages' => $this->when($this->relationLoaded('details'), function() {
                return $this->details->map(function ($d) {
                    return [
                        'id' => $d->stage_id,
                        'title' => $d->stage->getTranslations('title'),
                        'is_active' => $d->isActive(),
                    ];
                });
            })

        ];
    }
}
