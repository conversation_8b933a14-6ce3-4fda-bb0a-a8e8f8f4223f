<?php

namespace Modules\Sms\Transformers\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\Appeal;
use Modules\Sms\Entities\Student;

/** @mixin Appeal */
class AppealResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'old_mid_mark' => $this->old_mid_mark,
            'old_practical_final_mark' => $this->old_practical_final_mark,
            'old_final_mark' => $this->old_final_mark,
            'mid_mark' => $this->mid_mark,
            'practical_final_mark' => $this->practical_final_mark,
            'final_mark' => $this->final_mark,
            'note' => $this->note,
            'status' => $this->status,
            'student' => $this->when($this->relationLoaded('studentSubject') && $this->studentSubject->relationLoaded('student'), function () {
                return [
                    'id' => $this->studentSubject->student->id,
                    'name' => $this->studentSubject->student->user->full_name,
                ];
            }),
        ];
    }
}
