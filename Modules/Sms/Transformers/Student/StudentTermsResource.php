<?php

namespace Modules\Sms\Transformers\Student;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentTermsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'term_title' => $this->title,
            'year' => $this->year,
            'term_status' => $this->status,
            'student_status' => $this->pivot->status,
            'study_plan' => $this->study_plan
        ];
    }
}
