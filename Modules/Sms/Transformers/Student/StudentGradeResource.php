<?php

namespace Modules\Sms\Transformers\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentGradeResource extends JsonResource
{

    private static $withGrades;

    public static function customCollection($resource, $withGrades): AnonymousResourceCollection
    {
        //you can add as many params as you want.
        self::$withGrades = $withGrades;
        return parent::collection($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        $allowMarks = self::$withGrades && $this->relationLoaded('studentSubjects');

        return [
            'id' => $this->id,
            'first_name_ar' => $this->user->first_name_ar,
            'second_name_ar' => $this->user->second_name_ar,
            'third_name_ar' => $this->user->third_name_ar,
            'last_name_ar' => $this->user->last_name_ar,
            'first_name_en' => $this->user->first_name_en,
            'second_name_en' => $this->user->second_name_en,
            'third_name_en' => $this->user->third_name_en,
            'last_name_en' => $this->user->last_name_en,
            'entity_id' => $this->user->entity_id,
            'program_id' => $this->program_id,
            'is_paid' => $this->studentSubjects->first()?->is_paid,
            'mid_mark' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->mid_mark),
            'final_mark' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->final_mark),
            'practical_final_mark' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->practical_final_mark),
            'is_second_attempt' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->is_second_attempt),
            'full_mark' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->full_mark),
            'confirmed_by' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->confirmed_by),
            'grade' => $this->when($allowMarks, fn() => $this->studentSubjects->first()?->grade),
            'has_appeal' => $this->when($allowMarks, $this->studentSubjects->first()?->relationLoaded('appeal') ? $this->studentSubjects->first()?->appeal != null : false),
            'has_second_attempt' => $this->when($allowMarks, $this->studentSubjects->first()?->relationLoaded('secondAttempt') ? $this->studentSubjects->first()?->secondAttempt != null : false)
        ];
    }
}
