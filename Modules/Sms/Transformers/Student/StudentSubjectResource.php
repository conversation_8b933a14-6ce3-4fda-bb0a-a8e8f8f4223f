<?php

namespace Modules\Sms\Transformers\Student;

use App\Models\EntityType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\StudentSubject;

/** @mixin StudentSubject */
class StudentSubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        $currentEntity = app('current_entity');
        return [
            'id' => $this->subject_id,
            'student_subject_id' => $this->id,
            'group' => $this->group,
            'subject_name' => [
                'ar' => $this->subject_title_ar,
                'en' => $this->subject_title_en,
            ],
            'subject_code' => $this->subject_code,
            'is_paid' => $this->is_paid,
            'mid_mark' => $this->mid_mark,
            'final_mark' => $this->when($this->isConfirmedBySchool(), fn() => $this->final_mark),
            'practical_final_mark' => $this->when($this->isConfirmedBySchool(), fn() => $this->practical_final_mark),
            'full_mark' => $this->when($this->isConfirmedBySchool(), fn() => $this->full_mark),
            'grade' => $this->when($this->isConfirmedBySchool() || $this->grade == 'W', fn() => $this->grade),
            'confirmed_by' => $this->when($currentEntity->type == EntityType::UNIVERSITY, fn() => $this->confirmed_by),
            'is_second_attempt' => $this->is_second_attempt,
            'subject_type' => $this->subject_type,
            'appeal' => new AppealResource($this->whenLoaded('appeal')),
            'second_attempt' => new SecondAttemptResource($this->whenLoaded('secondAttempt')),
            'transfer_type' => $this->transfer_type
        ];
    }
}
