<?php

namespace Modules\Sms\Transformers\Student;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\SecondAttempt;

/** @mixin SecondAttempt */
class SecondAttemptResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'is_confirmed' => $this->is_confirmed,
            'final_mark' => $this->final_mark,
        ];
    }
}
