<?php

namespace Modules\Sms\Transformers\Student;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\SecondAttempt;

/** @mixin SecondAttempt */
class SecondAttemptResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'student_subject_id' => $this->student_subject_id,
            'old_mid_mark' => $this->old_mid_mark,
            'old_practical_final_mark' => $this->old_practical_final_mark,
            'old_final_mark' => $this->old_final_mark,
            'mid_mark' => $this->mid_mark,
            'practical_final_mark' => $this->practical_final_mark,
            'final_mark' => $this->final_mark,
            'note' => $this->note,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'student' => new StudentResource($this->whenLoaded('studentSubject.student')),
        ];
    }
}
