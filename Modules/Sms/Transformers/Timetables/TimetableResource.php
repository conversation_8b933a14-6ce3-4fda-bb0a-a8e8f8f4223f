<?php

namespace Modules\Sms\Transformers\Timetables;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\Timetable;

/** @mixin Timetable */
class TimetableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'term_title' => $this->term->title,
            'term_id' => $this->term->id,
            'year' => $this->term->year,
            'term_status' => $this->term->status,
            'term_study_plan' => $this->term->study_plan,
            'settings' => $this->term->settings,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'duration' => $this->duration,
            'created_at' => $this->created_at->format('Y-m-d H:i'),
            'content' => TimetableContentResource::collection($this->whenLoaded('contents'))
        ];
    }
}
