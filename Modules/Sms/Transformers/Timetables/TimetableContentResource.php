<?php

namespace Modules\Sms\Transformers\Timetables;

use App\Http\Resources\HallResource;
use App\Http\Resources\Lecturer\SimpleLecturerResource;
use App\Http\Resources\Program\SimpleProgramResource;
use App\Http\Resources\SimpleSubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\TimetableContent;

/** @mixin TimetableContent */
class TimetableContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'timetable_id' => $this->timetable_id,
            'subject_id' => $this->subject_id,
            'group' => $this->group,
            'lecturer_id' => $this->lecturer_id,
            'hall_id' => $this->hall_id,
            'capacity' => $this->capacity,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'day' => $this->day,
            'program_id' => $this->program_id,

            'program' => new SimpleProgramResource($this->whenLoaded('program')),
            'lecturer' => new SimpleLecturerResource($this->whenLoaded('lecturer')),
            'hall' => new HallResource($this->whenLoaded('hall')),
            'subject' => new SimpleSubjectResource($this->whenLoaded('subject')),
            'enrolled' => $this->when(!is_null($this->enrolled), (int)$this->enrolled),
        ];
    }
}
