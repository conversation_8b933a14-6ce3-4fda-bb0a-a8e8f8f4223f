<?php

namespace Modules\Sms\Transformers\Timetables;

use App\Http\Resources\Program\SimpleProgramResource;
use App\Http\Resources\SimpleSubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\TimetableContent;

/** @mixin TimetableContent */
class TimetableContentCountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'subject_id' => $this->subject_id,
            'group' => $this->group,
            'capacity' => $this->capacity,
            'program_id' => $this->program_id,

            'program' => new SimpleProgramResource($this->whenLoaded('program')),
            'subject' => new SimpleSubjectResource($this->whenLoaded('subject')),
            'students_count' => $this->when(!is_null($this->students_count), fn() => $this->students_count),
            'is_confirmed' => $this->when(!is_null($this->is_confirmed), fn() => (bool)$this->is_confirmed),
            'school_confirmed' => $this->when(!is_null($this->school_confirmed), fn() => (bool)$this->school_confirmed),
            'department_confirmed' => $this->when(!is_null($this->department_confirmed), fn() => (bool)$this->department_confirmed),
            'teacher_confirmed' => $this->when(!is_null($this->teacher_confirmed), fn() => (bool)$this->teacher_confirmed),
            'has_appeals' => $this->when(!is_null($this->has_appeals), fn() => (bool)$this->has_appeals),
        ];
    }
}
