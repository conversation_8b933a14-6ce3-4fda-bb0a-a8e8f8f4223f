<?php

namespace Modules\Sms\Transformers\SecondAttempt;

use App\Http\Resources\Program\SimpleProgramResource;
use App\Http\Resources\SimpleSubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Sms\Entities\TimetableContent;

/** @mixin TimetableContent */
class SecondAttemptSubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'subject_id' => $this->subject_id,
            'group' => $this->group,
            'program_id' => $this->program_id,
            'second_attempts_count' => $this->second_attempts_count,
            
            'program' => new SimpleProgramResource($this->whenLoaded('program')),
            'subject' => new SimpleSubjectResource($this->whenLoaded('subject')),
        ];
    }
}
