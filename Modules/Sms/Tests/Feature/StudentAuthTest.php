<?php

namespace Modules\Sms\Tests\Feature;

use App\Mail\SendVerificationEmail;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class StudentAuthTest extends TestCase
{
//    public function test_register_student_successfully()
//    {
//        Mail::fake();
//
//        $user = factory(User::class)->make(['password' => 'password', 'password_confirmation' => 'password', 'gender' => 'male']);
//        $userData = array_merge($user->getAttributes(), [
//            'student_id' => 12345678,
//            'program_id' => 1,
//            'mother_name' => 'test',
//            'birth_place' => 'Tripoli',
//            'work_place' => 'None',
//            'martial_status' => 1
//        ]);
//        $userData['gender'] = 'male';
//        $response = $this->json('post', '/api/register/student', $userData, ['origin' => 'lau.ly']);
//        $response->assertJson(['message' => 'success']);
//        $this->assertGuest();
//
//        Mail::assertSent(SendVerificationEmail::class, function ($mail) use ($user) {
//            return $mail->hasTo($user->email);
//        });
//        User::where('email', $user->email)->delete();
//    }
//
//    public function test_register_student_fail()
//    {
//        $user = factory(User::class)->make(['password' => 'password', 'password_confirmation' => 'password', 'gender' => 'male']);
//        $userData = array_merge($user->getAttributes(), [
//            'student_id' => 12345678,
//            'program_id' => 4
//        ]);
//        $userData['gender'] = 'male';
//        $response = $this->json('post', '/api/register/student', $userData, ['origin' => 'lau.ly']);
//        $response->assertJsonValidationErrors(['program_id']);
//        $this->assertGuest();
//    }
}
