<?php


namespace Modules\Website\DTOs;


use Illuminate\Http\Request;

class AnnouncementData extends \Spatie\DataTransferObject\DataTransferObject
{
    public $onlyApproved;
    public $sortKey;
    public $perPage;
    public $sortType;
    public $search;
    public $queryType;

    public static function fromRequest(Request $request, $onlyApproved): self
    {
        return new self([
            'onlyApproved' => $onlyApproved,
            'perPage' => $request->per_page,
            'sortKey' => $request->sort_key,
            'sortType' => $request->sort_type,
            'search' => $request->search,
            'queryType' => $request->query_type,
        ]);
    }

    public static function fromRequestForPaginationOnly(int $elementsPerPage, Request $request): self
    {
        return new self([
            'onlyApproved' => true,
            'perPage' => $elementsPerPage,
            'search' => $request->search,
        ]);
    }
}
