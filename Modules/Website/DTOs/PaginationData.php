<?php


namespace Modules\Website\DTOs;


use Illuminate\Http\Request;
use <PERSON>tie\DataTransferObject\DataTransferObject;

class PaginationData extends DataTransferObject
{
    public $sortKey;
    public $perPage;
    public $sortType;
    public $search;

    public static function fromRequest(Request $request): self
    {
        return new self([
            'perPage' => $request->per_page,
            'sortKey' => $request->sort_key,
            'sortType' => $request->sort_type,
            'search' => $request->search,
        ]);
    }

    public static function fromRequestForPaginationOnly(int $elementsPerPage, Request $request): self
    {
        return new self([
            'perPage' => $elementsPerPage,
            'search' => $request->search,
        ]);
    }
}
