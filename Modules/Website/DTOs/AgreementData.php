<?php


namespace Modules\Website\DTOs;


use Illuminate\Http\Request;

class AgreementData extends \Spatie\DataTransferObject\DataTransferObject
{
    public $perPage;
    public $sortType;
    public $search;

    public static function fromRequestForPaginationOnly(int $elementsPerPage, Request $request): self
    {
        return new self([
            'perPage' => $elementsPerPage,
            'search' => $request->search,
        ]);
    }
}
