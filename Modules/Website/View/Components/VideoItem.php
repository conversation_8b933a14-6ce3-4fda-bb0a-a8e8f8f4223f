<?php

namespace Modules\Website\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;

class VideoItem extends Component
{

    public $video;
    public $image;
    public $embed;

    /**
     * Create a new component instance.
     *
     * @param $video
     */
    public function __construct($video)
    {
        $this->video = $video;
        $video_id = substr($this->video->link, stripos($this->video->link, 'v=') + 2);
        $this->image = 'https://img.youtube.com/vi/'. $video_id .'/mqdefault.jpg';
        $this->embed = 'https://www.youtube.com/embed/'. $video_id;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View|string
     */
    public function render()
    {
        return view('website::components.video-item');
    }
}
