<?php

namespace Modules\Website\View\Components;

use Illuminate\View\Component;

class LecturerItem extends Component
{
    public $lecturer;

    /**
     * Create a new component instance.
     *
     * @param $lecturer
     */
    public function __construct($lecturer)
    {
        $this->lecturer = $lecturer;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('website::components.lecturer-item');
    }
}
