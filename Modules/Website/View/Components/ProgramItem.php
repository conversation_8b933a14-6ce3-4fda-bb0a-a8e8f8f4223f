<?php

namespace Modules\Website\View\Components;

use Illuminate\View\Component;

class ProgramItem extends Component
{
    public $program;

    /**
     * Create a new component instance.
     *
     * @param $program
     */
    public function __construct($program)
    {

        $this->program = $program;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('website::components.program-item');
    }
}
