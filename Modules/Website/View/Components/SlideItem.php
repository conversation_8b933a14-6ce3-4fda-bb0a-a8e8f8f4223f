<?php

namespace Modules\Website\View\Components;

use Illuminate\View\Component;

class SlideItem extends Component
{
    public $slide;

    /**
     * Create a new component instance.
     *
     * @param $slide
     */
    public function __construct($slide)
    {
        //
        $this->slide = $slide;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('website::components.slide-item');
    }
}
