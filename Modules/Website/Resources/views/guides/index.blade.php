@extends('website::layouts.master', ['title' => __('website::guides.guides')])

@section('content')
    <x-breadcrumb :title="__('website::guides.guides')">
        <li><a href="{{ route('index') }}">{{ __('website::global.home') }}</a></li>
        <li>{{ __('website::guides.guides') }}</li>
    </x-breadcrumb>

    <main class="page-content">
        <section class="section-70 bg-catskill">
            <div class="shell">
                <div class="range range-xs-center">
                    <div class="cell-md-12">
                        <form class="form-search form-search-widget" action="{{ route('guides.index') }}" method="get">
                            <div class="form-group">
                                <div class="input-group">
                                    <input class="form-control" type="text" name="search"
                                        placeholder="{{ __('website::global.search') }}"
                                        value="{{ Request::has('search') ? Request::get('search') : '' }}">
                                    <span class="input-group-btn input-group-append">
                                        <button class="btn btn-primary" type="submit">
                                            <span class="icon fa-search"></span>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div class="range">
                            @foreach ($guides as $guide)
                                <x-guide-item class="cell-sm-3 offset-top-30" :guide="$guide"></x-guide-item>
                            @endforeach
                        </div>
                        <div class="offset-top-60 text-md-left">
                            <!-- Bootstrap Pagination-->
                            <nav>
                                {{ $guides->withQueryString()->links() }}
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
