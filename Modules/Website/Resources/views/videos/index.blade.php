@extends('website::layouts.master', ['title' => __('website::videos.videos')])

@section('content')

    <x-breadcrumb :title="__('website::videos.videos')">
        <li><a href="{{ route('index') }}">{{ __('website::global.home') }}</a></li>
        <li>{{ __('website::videos.videos') }}</li>
    </x-breadcrumb>

    <main class="page-content">
        <section class="section-70 bg-catskill">
            <div class="shell">
                <div class="range range-xs-center">
                    <div class="cell-md-12">
                        <form class="form-search form-search-widget" action="{{ route('videos.index') }}" method="get">
                            <div class="form-group">
                                <div class="input-group">
                                    <input
                                            class="form-control"
                                            type="text"
                                            name="search"
                                            placeholder="{{ __('website::global.search') }}"
                                            value="{{ Request::has('search') ?  Request::get('search') : ''}}">
                                    <span class="input-group-btn input-group-append">
                                          <button class="btn btn-primary" type="submit">
                                              <span class="icon fa-search"></span>
                                          </button>
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div class="range text-sm-left">
                            @foreach($videos as $video)
                                <x-video-item class="cell-sm-4 offset-top-30 playVideo" :video="$video" data-target="#videoModal" data-toggle="modal"></x-video-item>
                            @endforeach
                        </div>
                        <div class="offset-top-60 text-md-left">
                            <!-- Bootstrap Pagination-->
                            <nav>
                                {{ $videos->withQueryString()->links() }}
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        @push('modal')
            <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModal" aria-hidden="true">
                <div class="modal-dialog modal-lg w-100" role="document">
                    <div class="modal-content">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="modal-title text-white"></h4>
                            <button type="button" class="close m-0" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" src="" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        @endpush
    </main>
@endsection

