@if ($albums->count() > 0)
    <section class="bg-random">
        <div class="shell-wide section-70 section-md-114">
            <h3 class="text-madison home-headings-custom text-bold view-animate fadeInUpSmall delay-06">
                {{ __('website::home.albums') }}</h3>
            <div class="albums-slider range offset-top-60 range-xs-center">
                @foreach ($albums as $album)
                    <x-album-item :album="$album"></x-album-item>
                @endforeach
            </div>
            <div class="offset-top-50 offset-lg-top-56 view-animate fadeInUpSmall">
                <a class="btn btn-ellipse btn-icon btn-icon-right btn-primary" href="{{ route('albums.index') }}">
                    <span class="icon fa-arrow-{{ __('website::utils.opposite_direction') }}"></span>
                    <span>{{ __('website::home.view_all_albums') }}</span>
                </a>
            </div>
        </div>
    </section>
@endif
