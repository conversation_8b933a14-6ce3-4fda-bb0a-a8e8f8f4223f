@if ($events->count() > 0)
    <section class="bg-random">
        <div class="shell-wide section-70 section-md-114">
            <h3 class="text-madison text-bold view-animate fadeInUpSmall delay-04">{{ __('website::home.events') }}
            </h3>
            <div class="range range-xs-center">
                @foreach ($events as $event)
                    <x-event-item :event="$event" class="cell-sm-6 cell-md-5 cell-xl-3 offset-top-40 offset-xl-top-0">
                    </x-event-item>
                @endforeach
            </div>
            <div class="offset-top-50 offset-lg-top-56 view-animate fadeInUpSmall">
                <a class="btn btn-ellipse btn-icon btn-icon-right btn-primary" href="{{ route('events.index') }}">
                    <span class="icon fa-arrow-right"></span><span>{{ __('website::home.view_all_events') }}</span>
                </a>
            </div>
        </div>
    </section>
@endif
