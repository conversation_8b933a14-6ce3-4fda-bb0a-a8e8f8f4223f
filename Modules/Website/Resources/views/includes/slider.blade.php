@if($slides->count() > 0)
<section>
    <div class="header-slider slick-slider"
         data-dots="true"
         data-arrows="{{ $slides->count() > 1 ? 'true' : 'false' }}"
         data-autoplay="{{ $slides->count() > 1 ? 'true' : 'false' }}"
         data-loop="{{ $slides->count() > 1 ? 'true' : 'false' }}"
         data-swipe="true"
         data-rtl="{{ App::isLocale('ar') ? 'true' : 'false' }}"
    >
        @foreach($slides as $slide)
            <x-slide-item :slide="$slide"></x-slide-item>
        @endforeach
    </div>
</section>
@endif
