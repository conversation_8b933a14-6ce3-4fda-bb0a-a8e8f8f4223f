<header class="page-head">
    <!-- RD Navbar Transparent-->
    <div class="rd-navbar-wrap">
        {{-- remove rd-navbar-static when fix for phone --}}
        <nav class="rd-navbar rd-navbar-default rd-navbar-static" data-md-device-layout="rd-navbar-static"
            data-lg-device-layout="rd-navbar-static" data-xl-device-layout="rd-navbar-static" data-stick-up-offset="210"
            data-xl-stick-up-offset="85" data-lg-auto-height="true" data-md-layout="rd-navbar-static"
            data-lg-layout="rd-navbar-static" data-lg-stick-up="true">
            <div class="rd-navbar-inner">
                <!-- RD Navbar Panel-->
                <div class="rd-navbar-panel">
                    <!-- RD Navbar Toggle-->
                    <button class="rd-navbar-toggle"
                        data-rd-navbar-toggle=".rd-navbar, .rd-navbar-nav-wrap"><span></span></button>
                    <h4 class="panel-title veil-md">{{ $entity->title }}</h4>
                    <!-- RD Navbar Right Side Toggle-->
                    <button class="rd-navbar-top-panel-toggle veil-md"
                        data-rd-navbar-toggle=".rd-navbar-top-panel"><span></span></button>
                    <div class="rd-navbar-top-panel">
                        <div class="rd-navbar-top-panel-left-part">
                            <ul class="list-unstyled">
                                <li>
                                    <div class="unit unit-md-horizontal unit-md-middle">
                                        {{-- <div class="unit-left"></div> --}}
                                        <div class="unit-body d-flex align-items-center">
                                            <span class="icon fa fa-globe text-middle mr-2"></span>
                                            <a class="nav-link dropdown-toggle px-0" href="#"
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span>{{ app()->getLocale() === 'ar' ? 'العربية' : 'English' }}</span>
                                            </a>
                                            <div
                                                class="dropdown-menu shadow dropdown-menu-{{ __('website::utils.direction') }}">
                                                <a class="dropdown-item"
                                                    href="{{ route(Route::currentRouteName(), array_merge(request()->route()->parameters, ['lang' => 'ar'])) }}">العربية</a>
                                                <a class="dropdown-item"
                                                    href="{{ route(Route::currentRouteName(), array_merge(request()->route()->parameters, ['lang' => 'en'])) }}">English</a>
                                            </div>
                                        </div>
                                        {{-- <div class="unit-body ml-lg-3">
                                            <a class="nav-link dropdown-toggle px-0" href="#"
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span>{{ __('website::menu.online_services') }}</span>
                                            </a>
                                            <div
                                                class="dropdown-menu shadow dropdown-menu-{{ __('website::utils.direction') }}">
                                                <a class="dropdown-item" href="https://mail.google.com"
                                                    target="_blank">{{ __('website::menu.academic_email') }}</a>
                                                <a class="dropdown-item"
                                                    href="{{ config('app.url') }}/request-application-receipt"
                                                    target="_blank">طلب إيصال نموذج التسجيل</a>
                                                @if (\App\Helpers\Utils::isStageEnabled(\Modules\Sms\Entities\TermStage::NEW_STUDENTS_REGISTRATION))
                                                    <a class="dropdown-item" href="{{ config('app.url') }}/apply">تسجيل
                                                        الطلبة الجدد</a>
                                                @endif

                                                @if (\App\Helpers\Utils::isStageEnabled(\Modules\Sms\Entities\TermStage::NEW_DOCTORATE_STUDENTS_REGISTRATION))
                                                    <a class="dropdown-item"
                                                        href="{{ config('app.url') }}/apply/doctorate">تسجيل طلبة
                                                        الدكتوراة
                                                        الجدد</a>
                                                @endif
                                            </div>
                                        </div> --}}
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="rd-navbar-top-panel-right-part">
                            <div class="rd-navbar-top-panel-left-part">

                                <div class="unit unit-horizontal unit-middle unit-spacing-xs">
                                    @guest
                                        <div class="unit-left"><span class="icon fa fa-sign-in text-middle"></span>
                                        </div>
                                        <div class="unit-body">
                                            <a class="nav-link dropdown-toggle px-0" href="#" data-toggle="dropdown"
                                                aria-haspopup="true" aria-expanded="false">
                                                <span>{{ __('website::global.login') }}</span>
                                            </a>
                                            <div
                                                class="dropdown-menu shadow dropdown-menu-{{ __('website::utils.direction') }}">
                                                <a class="dropdown-item" href="{{ route('login.students') }}">
                                                    الطلبة
                                                </a>
                                                <a class="dropdown-item" href="{{ route('login') }}">الأساتذة
                                                    والموظفين</a>
                                            </div>
                                        </div>
                                    @endguest
                                    @auth
                                        <div class="unit-left"><span class="icon fa fa-user text-middle"></span></div>
                                        <div class="unit-body">
                                            <a class="nav-link dropdown-toggle px-0" href="#" data-toggle="dropdown"
                                                aria-haspopup="true" aria-expanded="false">
                                                <span>{{ Auth::user()->shortName }}</span>
                                            </a>
                                            <div
                                                class="dropdown-menu shadow dropdown-menu-{{ __('website::utils.direction') }}">
                                                <a class="dropdown-item"
                                                    href="{{ route('profile') }}">{{ __('website::global.profile') }}</a>
                                                <div class="dropdown-divider"></div>
                                                @foreach (Auth::user()->panels($entity->id)->get() as $panel)
                                                    <a class="dropdown-item"
                                                        href="{{ route($panel->panel_name) }}">{{ __("website::menu.$panel->panel_name") }}</a>
                                                @endforeach

                                                @foreach (Auth::user()->smsPanels($entity->id)->get() as $panel)
                                                    <a class="dropdown-item"
                                                        href="{{ route($panel->panel_name) }}">{{ __("website::menu.$panel->panel_name") }}</a>
                                                @endforeach

                                                @if (Auth::user()->isType('student'))
                                                    <a class="dropdown-item"
                                                        href="{{ Auth::user()->entity->url }}/sms">{{ __('website::menu.sms') }}</a>
                                                @endif

                                                @if (Auth::user()->isType('lecturer'))
                                                    <a class="dropdown-item"
                                                        href="{{ Auth::user()->entity->url }}/lecturer-panel">{{ __('website::menu.lecturer-panel') }}</a>
                                                @endif

                                                <div class="dropdown-divider"></div>
                                                <form action="{{ route('logout') }}" method="post" id="logout-form">
                                                    @csrf
                                                    <a class="dropdown-item" href="javascript:{}"
                                                        onclick="document.getElementById('logout-form').submit();">{{ __('website::global.logout') }}</a>
                                                </form>
                                            </div>
                                        </div>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @if ($entity->isUniversity())
                    @include('website::includes.menus.university')
                @endif
                @if ($entity->isSchool())
                    @include('website::includes.menus.school')
                @endif
                @if ($entity->isDepartment())
                    @include('website::includes.menus.department')
                @endif
                @if ($entity->isManagement())
                    @include('website::includes.menus.management')
                @endif
                @if ($entity->isStudentsPage())
                    @include('website::includes.menus.students_page')
                @endif
            </div>
        </nav>
    </div>
</header>
