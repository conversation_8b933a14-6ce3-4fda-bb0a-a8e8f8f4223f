<div class="row">

    <div class="col-md-6">
        <h6 class="text-bold">{{ __('website::footer.quick_links') }}</h6>
        <div class="text-subline"></div>
        <ul class="list list-unstyled offset-top-30">
            <li class="offset-top-10">
                <a href="{{ $entity->parent->url }}">{{ $entity->parent->title }}</a>
            </li>
            <li class="offset-top-10">
                <a href="{{ route('lecturers.index') }}">{{ __('website::footer.lecturers') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="{{ route('programs.index') }}">{{ __('website::footer.programs') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="{{ route('news.index') }}">{{ __('website::footer.news') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="{{ route('about.index') }}">{{ __('website::footer.about_department') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="{{ route('contact.index') }}">{{ __('website::footer.contact') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="https://mail.google.com" target="_blank">{{ __('website::menu.academic_email') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="https://newstudents.{{ config('app.domain') }}"
                    target="_blank">{{ __('website::menu.new_students') }}</a>
            </li>
            <li class="offset-top-10">
                <a href="https://postgraduate.{{ config('app.domain') }}/"
                    target="_blank">{{ __('website::menu.control_panel') }}</a>
            </li>
        </ul>
    </div>

    <div class="col-md-6">
        @if ($entity->directChildren->where('type', 4)->count() > 0)
            <h6 class="text-bold">{{ __('website::footer.managements') }}</h6>
            <div class="text-subline"></div>
            <ul class="list list-unstyled offset-top-30">
                @foreach ($entity->directChildren->where('type', 4) as $managements)
                    <li><a
                            href="{{ $managements->url }}/{{ app()->getLocale() }}">{{ $managements->title }}</a>
                    </li>
                @endforeach
            </ul>
        @endif
    </div>
</div>
