@extends('website::layouts.master', ['title' => __('website::announcements.announcements')])

@section('content')

    <x-breadcrumb :title="__('website::announcements.announcements')">
        <li><a href="{{ route('index') }}">{{ __('website::global.home') }}</a></li>
        <li>{{ __('website::announcements.announcements') }}</li>
    </x-breadcrumb>

    <main class="page-content">
        <section class="section-70 bg-catskill">
            <div class="shell">
                <form class="form-search form-search-widget" action="{{ URL::current() }}" method="get">
                    <div class="form-group">
                        <div class="input-group">
                            <input
                                class="form-control"
                                type="text"
                                name="search"
                                placeholder="{{ __('website::global.search') }}"
                                value="{{ Request::has('search') ?  Request::get('search') : ''}}">
                            <span class="input-group-btn input-group-append">
                                  <button class="btn btn-primary" type="submit">
                                      <span class="icon fa-search"></span>
                                  </button>
                            </span>
                        </div>
                    </div>
                </form>
                <div class="range text-left">
                    @foreach($announcements as $announcement)
                        <x-announcement-item class="cell-sm-4 offset-top-30" :announcement="$announcement"/>
                    @endforeach
                </div>
                <div class="offset-top-60 text-center text-md-left">
                    <!-- Bootstrap Pagination-->
                    <nav>
                        {{ $announcements->withQueryString()->links() }}
                    </nav>
                </div>
            </div>
        </section>
    </main>

@endsection
