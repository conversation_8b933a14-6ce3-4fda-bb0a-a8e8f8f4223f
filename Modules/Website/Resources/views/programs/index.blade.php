@extends('website::layouts.master', ['title' => __('website::programs.title')])

@section('content')

    <x-breadcrumb :title="__('website::programs.title')">
        <li><a href="{{ route('index') }}">{{ __('website::global.home') }}</a></li>
        <li>{{ __('website::programs.title') }}</li>
    </x-breadcrumb>

    <main class="page-content">
        <section class="section-70 bg-catskill">
            <div class="shell">
                <div class="range range-xs-center">
                    <div class="cell-md-4 text-left cell-xs-8 offset-top-85 offset-md-top-0">
                        @include('website::programs.aside_search', ['currentEntity' => $currentEntity])
                    </div>
                    <div class="cell-md-8">
                        <div class="range text-sm-left">
                            @foreach($programs as $program)
                                <x-program-item class="cell-sm-4 offset-top-30" :program="$program"></x-program-item>
                            @endforeach
                        </div>
                    </div>
                    <div class="cell-12">
                        <div class="offset-top-60 text-md-left">
                            <!-- Bootstrap Pagination-->
                            <nav>
                                {{ $programs->withQueryString()->links() }}
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection

