<!DOCTYPE html>
<html class="wide wow-animation scrollTo" dir="{{ __("website::utils.dir") }}" lang="{{app()->getLocale()}}">
@include('website::includes.head', ['title' => $title ?? config('app.name')])
<body>
    <div class="page text-center">

        @include('website::journals.layouts.header')

        @yield('content')

        @include('website::journals.layouts.footer')

    </div>

    @stack('modal')

    <script src="{{asset('website/js/core.min.js')}}"></script>
    <script src="{{mix('website/js/app.js')}}"></script>
    @stack('js')

</body>
</html>
