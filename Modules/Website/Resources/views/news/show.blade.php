@extends('website::layouts.master', ['title' => __('website::news.news') . ' - ' . $news->title])

@section('content')
    <x-breadcrumb :title="$news->title" :image="$news->images->first()->getFullUrl()">
        <li><a href="{{ route('index') }}">{{ __('website::global.home') }}</a></li>
        <li><a href="{{ route('news.index') }}">{{ __('website::news.news') }}</a></li>
        <li>{{ $news->title }}</li>
    </x-breadcrumb>

    <main class="page-content">
        <div id="fb-root"></div>
        <!-- Latest news-->
        <section class="section-70 section-md-114">
            <div class="shell">
                <div class="range range-xs-center">
                    <div class="cell-sm-8 cell-md-8 text-md-left">
                        <h3 class="text-bold">
                            {{ $news->title }}
                        </h3>
                        <hr class="divider bg-madison hr-md-left-0">
                        <div class="offset-top-20 text-black">
                            <i class="fa fa-calendar-o"></i>
                            <span
                                class="pl-1 inset-left-10 text-sm">{{ Date::parse($news->date)->format('d M, Y') }}</span>
                        </div>
                        <div class="offset-top-30">
                            @if ($news->images->count() > 0)
                                <div class="news-slider" data-photo-swipe-gallery="gallery">
                                    @foreach ($news->images as $image)
                                        <a data-photo-swipe-item="" href="{{ $image->getFullUrl() }}"
                                           data-image_c="href"
                                           data-size="1200x800">
                                            <img class="slider-img" src="{{ $image->getFullUrl() }}"
                                                 alt="{{ $news->title }}" data-image_n="src" data-title="alt">
                                            <span class="slider-size-icon fa-arrows-alt"></span>
                                        </a>
                                    @endforeach
                                </div>
                            @endif
                            <div class="offset-top-20">
                                <div>{!! $news->description !!}</div>
                            </div>
                        </div>
                        <div class="offset-top-30 post-news-meta range range-xs-middle range-xs-center">
                            <div class="cell-md-6">
                                <ul class="post-news-meta list list-inline list-inline-xs">
                                    <li class="mt-0 ml-2">
                                        <span class="icon icon-xs fa fa-comment-o text-middle text-madison"></span>
                                        <span class="text-middle pl-1 text-black">{{ count($news->comments) }}
                                            {{ trans_choice('website::news.comments', count($news->comments)) }}</span>
                                    </li>
                                    <li class="mt-0 ml-2">
                                        <span class="icon icon-xs fa fa-eye text-middle text-madison"></span>
                                        <span class="text-middle pl-1 text-black">{{ $news->viewed }}
                                            {{ trans_choice('website::news.views', $news->viewed) }}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="cell-md-6">
                                <ul class="list-inline list-inline-xs list-inline-madison pull-md-right text-middle">
                                    <li>
                                        <a class="icon icon-xxs fa-linkedin icon-circle icon-gray-light-filled"
                                           target="popup"
                                           href="https://www.linkedin.com/sharing/share-offsite/?url={{ URL::current() }}">
                                        </a>
                                    </li>
                                    <li>
                                        <a class="icon icon-xxs fa-twitter icon-circle icon-gray-light-filled"
                                           target="popup"
                                           href="https://twitter.com/intent/tweet?text={{ urlencode(URL::current()) }}">
                                        </a>
                                    </li>
                                    <li>
                                        <a class="icon icon-xxs fa-facebook icon-circle icon-gray-light-filled"
                                           target="popup"
                                           href="https://www.facebook.com/sharer/sharer.php?u={{ URL::current() }}">
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="offset-top-50">
                            <div class="offset-top-60">
                                <h6 class="text-bold">{{ __('website::news.the_comments') }}</h6>
                                <div class="text-subline"></div>
                                <div class="offset-top-30">

                                    @foreach ($news->comments as $comment)
                                        <x-comment-item :comment="$comment"/>
                                    @endforeach


                                    @if ($news->commentable)
                                        <div class="offset-top-60">
                                            <h6 class="text-bold">{{ __('website::news.send_comment') }}</h6>
                                            <div class="text-subline"></div>
                                            @if (Session::has('comment-created'))
                                                <div class="alert alert-success">
                                                    <i class="icon icon-xxs fa fa-check"></i>
                                                    {{ __('website::news.comment_created') }}
                                                </div>
                                            @endif
                                            @auth
                                                <div class="offset-top-20">
                                                    @if ($errors->has('recaptcha_token'))
                                                        <div class="cell-lg-12">
                                                            <div class="alert alert-danger">
                                                                <strong>{{ $errors->first('recaptcha_token') }}</strong>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    <form class="text-left" method="post"
                                                          action="{{ route('news.comments.store', $news->slug) }}">
                                                        <div class="range">
                                                            <div class="cell-lg-12 offset-top-12">
                                                                <div class="form-group">
                                                                    <label class="form-label form-label-outside"
                                                                           for="comment-form-message">{{ __('website::news.message') }}</label>
                                                                    <textarea
                                                                        class="form-control form-validation-inside {{ $errors->has('comment') ? ' is-invalid' : '' }}"
                                                                        id="comment-form-message"
                                                                        name="comment"></textarea>
                                                                    @if ($errors->has('comment'))
                                                                        <span class="invalid-feedback" role="alert">
                                                                            <strong>{{ $errors->first('comment') }}</strong>
                                                                        </span>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                            <x-recaptcha-field></x-recaptcha-field>
                                                        </div>
                                                        <div class="offset-top-20 text-center text-sm-left">
                                                            <button class="btn btn-primary"
                                                                    type="submit">{{ __('website::news.submit') }}</button>
                                                        </div>
                                                        @csrf
                                                    </form>
                                                </div>
                                            @endauth
                                            @guest
                                                <p>{{ __('website::news.no_comment_message') }}</p>
                                            @endguest

                                        </div>
                                    @endif

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- aside-->
                    <div class="cell-md-4 text-left cell-xs-8 offset-top-85 offset-md-top-0">
                        <aside class="inset-md-left-30">
                            <div class="offset-top-60">
                                <!--Recent posts-->
                                <h6 class="text-bold">{{ __('website::news.recent_news') }}</h6>
                                <div class="text-subline"></div>
                                <div class="offset-top-20 text-left">

                                    @foreach ($recentNews as $recentNew)
                                        <div class="offset-top-20">
                                            <div class="row justify-content-between align-items-start">
                                                <div class="col-8">
                                                    <h6 class="text-bold"><a
                                                            href="{{ route('news.show', ['news' => $recentNew->slug]) }}">{{ Str::limit($recentNew->title, 40) }}</a>
                                                    </h6>
                                                    <p>
                                                        {{ Str::limit(preg_replace('#<[^>]+>#', ' ', $recentNew->description), 100) }}
                                                    </p>
                                                    <p class="text-dark">
                                                        {{ Date::parse($recentNew->date)->format('d M, Y') }}</p>
                                                </div>
                                                <div class="col-4">
                                                    {!! $recentNew->coverImage?->img('', ['class' => 'img-responsive', 'alt' => $recentNew->title]) !!}
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach

                                </div>
                            </div>
                        </aside>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- PhotoSwipe Gallery-->
    <div dir="ltr" class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="pswp__bg"></div>
        <div class="pswp__scroll-wrap">
            <div class="pswp__container">
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
            </div>
            <div class="pswp__ui pswp__ui--hidden">
                <div class="pswp__top-bar">
                    <div class="pswp__counter"></div>
                    <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
                    <button class="pswp__button pswp__button--share" title="Share"></button>
                    <button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
                    <button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>
                    <div class="pswp__preloader">
                        <div class="pswp__preloader__icn">
                            <div class="pswp__preloader__cut">
                                <div class="pswp__preloader__donut"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                    <div class="pswp__share-tooltip"></div>
                </div>
                <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
                <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>
                <div class="pswp__caption">
                    <div class="pswp__caption__center"></div>
                </div>
            </div>
        </div>
    </div>
@endsection
