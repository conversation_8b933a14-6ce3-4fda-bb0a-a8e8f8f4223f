<div {{ $attributes }}>
    <article
        class="news-item post-news rounded overflow-hidden shadow-sm d-flex flex-column justify-content-between h-100">
        <a class="scale-thumbnail" href="{{ route('agreements.show', ['agreement' => $agreement->slug]) }}">
            {!! $agreement->coverImage?->img('', ['class'=>'news-item__img', 'alt'=>$agreement->title]) !!}
        </a>
        <div class="post-news-body flex-grow-1">
            <h6><a class="text-black"
                   href="{{ route('news.show', ['news' => $agreement->slug]) }}">{{ $agreement->title }}</a></h6>
            <div class="post-news-meta d-flex text-black">
                <div class="small">
                    <i class="fa fa-eye"></i>
                    <span
                        class="pl-1 inset-left-10 text-sm">{{ $agreement->viewed }} {{ trans_choice("website::news.views", $agreement->viewed) }}</span>
                </div>
                <div class="small ml-4">
                    <i class="fa fa-calendar-o"></i>
                    <span
                        class="pl-1 inset-left-10 text-sm">{{ Date::parse($agreement->date)->format('d M, Y') }}</span>
                </div>
            </div>
            <div class="offset-top-20 post-news-content">
                <p class="text-muted">{{ Str::limit(preg_replace('#<[^>]+>#', ' ', $agreement->description), 150) }}</p>
            </div>
        </div>
        <div class="px-3 pb-3 d-flex justify-content-end">
            <a class="text-black" href="{{ route('agreements.show', ['agreement' => $agreement->slug]) }}">
                <span class="insert-right-10">{{ __('website::news.read_more') }}</span>
                <i class="fa fa-arrow-circle-o-left text-middle"></i>
            </a>
        </div>
    </article>
</div>
