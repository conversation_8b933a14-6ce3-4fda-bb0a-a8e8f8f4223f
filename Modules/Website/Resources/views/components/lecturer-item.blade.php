<div {{ $attributes }}>
    <div class="bg-white shadow-sm rounded d-flex flex-column justify-content-between h-100">

        <a href="{{ route('lecturers.show', $lecturer) }}">
            {!! $lecturer->user->avatar?->img('', ['class' => 'img-cover img-h-200 w-100 reveal-inline-block', 'alt' => $lecturer->user->short_name]) ?? "<img src=\"{$lecturer->user->getFallbackMediaUrl('avatar')}\" alt=\"{$lecturer->user->short_name}\" class=\"img-cover img-h-200 w-100 reveal-inline-block\"/>" !!}
        </a>

        <div class="p-3 flex-grow-1">
            <h6 class="text-bold text-black"><a
                    href="{{ route('lecturers.show', $lecturer) }}">{{ $lecturer->user->short_name }}</a></h6>
            <div class="offset-top-5">
                <p>{{ $lecturer->academicRank->title }} - {{ $lecturer->user->entity->title }}</p>
            </div>
        </div>
        <div class="p-3 d-flex justify-content-end">
            <a href="{{ route('lecturers.show', $lecturer) }}"
                class="btn btn-sm btn-primary">{{ __('website::lecturers.profile') }}</a>
        </div>
    </div>
</div>
