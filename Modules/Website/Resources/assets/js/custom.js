const isRtl = document.dir === 'rtl'

$('.news-slider').slick({
  slidesToShow: 1,
  slidesToScroll: 1,
  fade: true,
  rtl: isRtl,
  dots: true,
  autoplay: true,
  autoplaySpeed: 2000,
})

$('.announcements-slider').slick({
  slidesToShow: 3,
  slidesToScroll: 3,
  autoplay: true,
  autoplaySpeed: 5000,
  rtl: isRtl,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      },
    },
    {
      breakpoint: 1800,
      settings: {
        slidesToShow: 3,
      },
    },
  ],
})

$('.albums-slider').slick({
  slidesToShow: 4,
  rtl: isRtl,
  arrows: false,
  useTransform: false,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
      },
    },
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
      },
    },
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 4,
      },
    },
  ],
})

$('.partners-slider').slick({
  slidesToShow: 9,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 2000,
  rtl: isRtl,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
      },
    },
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 6,
      },
    },
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 8,
      },
    },
  ],
})

$('.videos-slider').slick({
  slidesToShow: 4,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 2000,
  rtl: isRtl,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
      },
    },
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
      },
    },
    {
      breakpoint: 1440,
      settings: {
        slidesToShow: 4,
      },
    },
  ],
})

$('.playVideo a').click(function(e) {
  e.preventDefault()
  const videoModal = '#videoModal'
  const videoSRC = $(this).data('embed')
  const videoTitle = $(this).attr('title')
  const videoSRCauto = videoSRC + '?autoplay=1'
  $(videoModal + ' iframe').attr('src', videoSRCauto)
  $(videoModal + ' .modal-title').text(videoTitle)
  $(videoModal).on('hidden.bs.modal', function() {
    $(videoModal + ' iframe').attr('src', '')
  })
})
