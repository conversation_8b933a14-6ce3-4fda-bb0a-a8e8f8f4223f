// ======== Home Page Random background =========
#home-page {
  .bg-random:nth-child(even) {
    background: #fff;
  }
  .bg-random:nth-child(odd) {
    background: #f5f7fa;
  }
}
// ======== Header Slider =========
.header-slider {
  height: calc(100vh - 182px);

  & > .slider-img:not(:first-child) {
    display: none;
  }
  & > .slider-img:first-child {
    padding: 0 20px;
  }
  .swiper-slide-caption {
    position: initial;
  }
  .slider-img {
    height: calc(100vh - 182px);
    width: 100%;
    background-position: 80% center;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .slider-content {
    background: rgba(0, 0, 0, 0.5);
    padding: 1rem;
    color: #fff;
    border-radius: 10px;
  }
  .slick-arrow {
    background: transparent;
    &:before {
      font-weight: 400;
      font-family: 'FontAwesome';
      width: 46px;
      height: 46px;
      line-height: 64px;
      font-size: 64px;
      z-index: 10;
      opacity: 1;
      color: #fff;
    }
    &:hover::before {
      opacity: 0.8;
    }
  }
  .slick-prev {
    left: 15px;
    &:before {
      content: '\f104';
    }
  }
  .slick-next {
    right: 15px;
    &:before {
      content: '\f105';
    }
  }
  .slick-dots {
    bottom: 20px;
    li {
      width: 15px;
      height: 15px;
      margin: 0 15px;
      border: 2px solid #fff;
      border-radius: 50%;
      transition: 0.35s all ease;
      &.slick-active {
        background: #fff;
      }
      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
[dir='rtl'] .header-slider .slick-prev:before {
  content: '\f105';
}
[dir='rtl'] .header-slider .slick-next:before {
  content: '\f104';
}

// ======== About Section =========
.about-section__img {
  max-width: 100%;
  height: auto;
  float: right;
  margin-left: 20px;
  margin-bottom: 20px;
}

// ======== User Navbar =========
.rd-navbar-top-panel {
  .dropdown-menu {
    top: 5px !important;
    left: auto;
    //float: inherit !important;
    //text-align: inherit !important;
  }
}
.news-item {
  &__img {
    width: 100%;
    height: 250px;
    object-fit: cover;
  }
}

// ======== Event =========
.post-event {
  &-img-overlay,
  img {
    width: 100%;
    height: 250px;
  }
}
// ======== Articles =========
.articles {
  &__post {
    width: 100%;
    overflow: hidden;
    .img-wrapper {
      width: 100%;
      min-height: 200px;
      height: 100%;
      background-position: center;
      background-size: cover;
    }
  }
  &__post-body {
    height: 88px;
    overflow: hidden;
  }
}

.article-content {
  .image {
    text-align: center;
    img {
      width: 100%;
      height: auto;
    }
  }
}

// ======== Collapse Table =========
.collapse-table {
  &__col {
    width: 60px;
  }
  &__toggle {
    cursor: pointer;
  }
  &__icon {
    transition: all 0.3s ease-in-out;
  }
  tr {
    &[aria-expanded='true'] {
      .collapse-table__icon {
        transform: rotate(90deg);
      }
    }
  }
  .hidden-row td {
    padding: 0 4px !important;
    background: #f9f9f9;
  }
}

// ======== News Slider =========
.news-slider {
  height: 500px;
  .slider-img {
    height: 500px;
    width: 100%;
    object-fit: cover;
  }
  .slick-prev,
  .slick-next {
    background: rgba(0, 0, 0, 0.5);
    &:hover {
      background: #000;
    }
  }
  .slick-next:before {
    content: '\f104' /*rtl:append'\f105'*/;
    font-family: 'FontAwesome';
  }
  .slick-prev:before {
    content: '\f105' /*rtl:append'\f104'*/;
    font-family: 'FontAwesome';
  }
  .slider-size-icon {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 18px;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding: 18px;
    line-height: 1;
    opacity: 0.75;

    &:hover {
      background: #000;
      opacity: 1;
    }
  }
  .slick-dots {
    bottom: 20px;

    li {
      width: 15px;
      height: 15px;
      margin: 0 15px;
      border: 2px solid #fff;
      border-radius: 50%;
      transition: 0.35s all ease;

      &.slick-active {
        background: #fff;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// ======== Announcements Slider =========
.announcements-slider {
  .slider-img {
    height: 200px;
    width: 100%;
    object-fit: cover;
    border-bottom: 4px solid $brand-primary;
  }
}
// ======== Announcements Item =========
.announcement-item {
  height: 520px;
  &__img {
    width: 100%;
    height: 250px;
    object-fit: cover;
  }
  &__toolbar {
    margin-top: -40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__date {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 99;
      height: 75px;
      width: 75px;
      background: $brand-madison;
      color: #fff;
      border-radius: 50%;
      text-align: center;
      margin-left: 1rem;
      font-size: 12px;
    }
    &__type {
      z-index: 99;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: $brand-madison;
      color: #fff;
      width: 150px;
      padding: 8px 15px;
      border-radius: 50px 0 0 50px;
    }
  }
}

// ======== Albums Slider =========

.albums-slider {
  .slick-track {
    margin: auto !important;
  }
}

// ======== Image Scale Thumbnail =========

.scale-thumbnail {
  display: block;
  overflow: hidden;
  img {
    transition: all 0.3s ease-in-out;
  }
  &:hover img {
    transform: scale(1.025);
  }
}

// ======== Videos item =========
.video-item {
  position: relative;
  &__img {
    width: 100%;
    height: 300px;
    object-fit: cover;
  }
  &__content {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
    background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.95) 100%);
    background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.95) 100%);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.95) 100%);

    h4 {
      text-align: left;
      color: white;
      padding: 0 24px 10px 24px;
      position: absolute;
      bottom: 10px;
      font-size: 1rem;
    }
  }

  &__hover {
    position: absolute;
    cursor: pointer;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border: 3px solid rgba(0, 0, 0, 0);
    transition-duration: 250ms;
    transition-property: color, background-color, border;
    transition-timing-function: ease-in-out;

    &:hover {
      border-color: $brand-primary;
    }
  }
}

// ======== Partner item =========
.partner-item {
  position: relative;
  //background-color: white;
  //& a {
  //    position: relative;
  //    width: 100%;
  //    height: 100%;
  //}
  &__img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin: auto;
  }
  &__content {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
  }
}

// ======== Slick Slider Navs =========
.videos-slider,
.announcements-slider,
.partners-slider,
.albums-slider {
  .slick-prev,
  .slick-next {
    bottom: -80px;
    top: auto;
    background: $brand-primary;
    border: 2px solid $brand-primary;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    &:hover {
      background: $brand-madison;
    }
  }

  .slick-next {
    right: 50%;
    left: auto;
    margin-right: -45px;

    &:before {
      opacity: 1;
      content: '\f105';
      font-family: 'FontAwesome';
    }
  }

  .slick-prev {
    left: 50%;
    right: auto;
    margin-left: -45px;
    &:before {
      opacity: 1;
      content: '\f104';
      font-family: 'FontAwesome';
    }
  }
}

[dir='rtl'] {
  .videos-slider .slick-next:before,
  .announcements-slider .slick-next:before,
  .partners-slider .slick-next:before,
  .albums-slider .slick-next:before {
    content: '\f104' !important;
  }

  .videos-slider .slick-prev:before,
  .announcements-slider .slick-prev:before,
  .partners-slider .slick-prev:before,
  .albums-slider .slick-prev:before {
    content: '\f105' !important;
  }
}

// ======== Issues Item =========
.issue-item {
  img {
    height: 300px;
    width: 100%;
    object-fit: contain;
    transition: 0.3s all ease;
  }
  a:hover img {
    opacity: 0.8;
  }
}

// ======== Journals about section =========
.about-journal {
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  width: 100%;
  height: 400px;

  &__text {
    color: #fff;
    text-align: left;
    background: rgba(172, 134, 53, 0.9);
    padding: 25px;
    border-radius: 12px;
  }
}

.statistic-item,
.guide-item {
  i {
    font-size: 30px;
    width: 100px;
    font-weight: 400;
    height: 100px;
    line-height: 100px;
    border: 1px solid #636363;
    display: block;
    margin: 0 auto 40px auto;
    border-radius: 50%;
    position: relative;
    transition: color 0.4s ease;
    box-sizing: border-box;

    &:after {
      content: '';
      position: absolute;
      width: 100px;
      height: 100px;
      left: 0;
      top: 0;
      border-radius: 50%;
      border: 1px solid $brand-primary;
      transition: all 0.25s ease;
      opacity: 0;
    }
  }
  &:hover i {
    border: none;
    background-color: $brand-primary;
    color: #fff;
    &:after {
      transform: scale(1.2, 1.2);
      opacity: 1;
    }
  }
}

.editorial-item {
  img {
    height: 200px;
    width: 200px;
    object-fit: cover;
  }
}

.issues-article {
  > div:nth-child(even) {
    .issue-article-item {
      background-color: #f2f2f2;
    }
  }

  .issue-article-item {
    border-radius: 0 10px 10px 0;
    border-left: 4px solid $brand-primary;
    border-right: 1px solid #dee2e6;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    margin: 1rem !important;
    padding: 1rem;
  }
}

.journal-item {
  border-radius: 0 0 10px 10px;
  overflow: hidden;
  img {
    height: 250px;
    width: 100%;
    object-fit: cover;
  }

  &__content {
    padding: 1rem;
    border-bottom: 4px solid $brand-primary;
  }
}

// hide google recaptcha corner icon
.grecaptcha-badge {
  visibility: hidden;
}

.recaptcha-field {
  direction: ltr;
  text-align: center;
  width: 100%;

  a {
    color: #0000ff;
    text-decoration: underline;
  }
}
