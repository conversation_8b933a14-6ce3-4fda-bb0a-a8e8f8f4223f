<?php

namespace Modules\Website\Transformers\Article;

use Illuminate\Http\Resources\Json\JsonResource;

class ShowArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'title' => $this->getTranslations('title'),
            'description' => $this->getTranslations('description'),
            'image' => $this->image->getFullUrl()
        ];
    }
}
