<?php

namespace Modules\Website\Transformers\Publication;

use App\Http\Resources\Entity\SimpleEntityResource;
use App\Http\Resources\FileMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class PublicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'abstract' => $this->abstract,
            'type' => $this->type,
            'month' => $this->month,
            'year' => $this->year,
            'authors' => $this->authors,
            'publication_link' => $this->publication_link,
            'volume' => $this->volume,
            'number' => $this->number,
            'from_pages' => $this->from_pages,
            'to_pages' => $this->to_pages,
            'location' => $this->location,
            'publisher' => $this->publisher->name,
            'lang' => $this->lang,
            'file' => new FileMediaResource($this->file),
            'entity_id' => $this->entity_id,
            'entity' => new SimpleEntityResource($this->whenLoaded('entity')),
        ];
    }
}
