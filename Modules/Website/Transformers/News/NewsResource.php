<?php

namespace Modules\Website\Transformers\News;

use Illuminate\Http\Resources\Json\JsonResource;

class NewsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'date' => $this->date->format('Y-m-d'),
            'category' => [
                'title' => $this->category->getTranslations('title'),
            ],
            'approved' => $this->approved,
            'slug' => $this->slug,
            'targets' => $this->targets
        ];
    }
}
