<?php

namespace Modules\Website\Transformers\News;

use App\Http\Resources\ImageMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowNewsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'slug' => $this->slug,
            'description' => $this->getTranslations('description'),
            'date' => $this->date->format('Y-m-d'),
            'category' => [
                'title' => $this->category->getTranslations('title'),
            ],
            'category_id' => $this->category_id,
            'commentable' => $this->commentable,
            'images' => ImageMediaResource::collection($this->images),
            'targets' => $this->targets
        ];
    }
}
