<?php

namespace Modules\Website\Transformers\Album;

use App\Http\Resources\ImageMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowAlbumResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'title' => $this->getTranslations('title'),
            'category_id' => $this->category_id,
            'date' => $this->date->format('Y-m-d'),
            'images' => ImageMediaResource::collection($this->images)
        ];
    }
}
