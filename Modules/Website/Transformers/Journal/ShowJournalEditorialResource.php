<?php

namespace Modules\Website\Transformers\Journal;

use Illuminate\Http\Resources\Json\JsonResource;

class ShowJournalEditorialResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->getTranslations('name'),
            'affiliation' => $this->getTranslations('affiliation'),
            'email' => $this->email,
            'journal_position_id' => $this->journal_position_id,
            'image' => $this->image->getFullUrl()
        ];
    }
}
