<?php

namespace Modules\Website\Transformers\Guide;

use App\Http\Resources\FileMediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ShowGuideResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslations('title'),
            'guide_file' => FileMediaResource::collection($this->getMedia()),
        ];
    }
}
