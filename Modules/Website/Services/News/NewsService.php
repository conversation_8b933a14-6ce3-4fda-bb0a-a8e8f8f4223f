<?php


namespace Modules\Website\Services\News;


use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use Modules\Website\DTOs\NewsData;
use Modules\Website\Http\Requests\Comment\StoreNewsCommentRequest;
use Modules\Website\Http\Requests\News\ChangeNewsStatusRequest;
use Modules\Website\Http\Requests\News\StoreNewsRequest;
use Modules\Website\Http\Requests\News\UpdateNewsRequest;
use Modules\Website\Http\Requests\News\UploadNewsImageRequest;
use Modules\Website\Repositories\News\NewsRepositoryInterface;

class NewsService
{
    /**
     * @var NewsRepositoryInterface
     */
    private $newsRepository;

    /**
     * NewsService constructor.
     * @param NewsRepositoryInterface $newsRepository
     */
    public function __construct(NewsRepositoryInterface $newsRepository)
    {
        $this->newsRepository = $newsRepository;
    }

    public function getPagination(NewsData $newsData)
    {
        return $this->newsRepository->getPagination($newsData);
    }

    public function getLatest($numberOfElements)
    {
        return $this->newsRepository->getLatest($numberOfElements);
    }

    public function show($id)
    {
        return $this->newsRepository->show($id);
    }

    public function create(StoreNewsRequest $request)
    {
        $currentEntityId = EntitiesHelper::getEntityId();

        $validatedData = collect($request->validated());
        $newsData = $validatedData->except('images')->toArray();

        $newsData['entity_id'] = $currentEntityId;
        if(auth()->user()->hasPermissionTo('approve-news', $currentEntityId)) {
            $newsData['approved'] = 1;
        }
        $news = $this->newsRepository->create($newsData);
        StorageHelper::uploadMultipleFromRequest($news, 'images');

        return ['message' => 'success', 'code' => 200];
    }

    public function update(UpdateNewsRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $newsData = $validatedData->except('removed_images', 'images')->toArray();

        $removedNewsImagesIds = collect($validatedData->only('removed_images'))->flatten();

        $news = $this->newsRepository->update($newsData, $id);
        if($request->has('images')) {
            StorageHelper::uploadMultipleFromRequest($news, 'images');
        }
        StorageHelper::deleteMedia($news, $removedNewsImagesIds);
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->newsRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

    public function changeStatus($id, ChangeNewsStatusRequest $request)
    {
        $this->newsRepository->changeStatus($id, $request->get('approved'));
        return ['message' => 'success', 'code' => 200];
    }

    public function addComment($newsId, StoreNewsCommentRequest $request)
    {
        $newsCommentData = [
            'new_id' => $newsId,
            'comment' => $request->comment,
            'user_id' => auth()->user()->id
        ];
        return $this->newsRepository->addComment($newsCommentData);
    }

    public function uploadImage(UploadNewsImageRequest $request)
    {
        $validatedData = collect($request->validated());
        $fileName = StorageHelper::uploadFile('website/news', $validatedData->get('image'));
        return StorageHelper::getUrlOf('' . $fileName);
    }

}
