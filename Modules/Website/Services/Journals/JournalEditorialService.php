<?php


namespace Modules\Website\Services\Journals;


use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use Modules\Website\Http\Requests\Journal\StoreJournalEditorialRequest;
use Modules\Website\Http\Requests\Journal\UpdateJournalEditorialRequest;
use Modules\Website\Repositories\Journals\JournalEditorialRepositoryInterface;

class JournalEditorialService
{
    /**
     * @var JournalEditorialRepositoryInterface
     */
    private $journalEditorialRepository;

    public function __construct(JournalEditorialRepositoryInterface  $journalEditorialRepository)
    {
        $this->journalEditorialRepository = $journalEditorialRepository;
    }
    public function getAll()
    {
        return $this->journalEditorialRepository->getAll();
    }

    public function create(StoreJournalEditorialRequest $request)
    {
        $validatedData = collect($request->validated());
        $currentEntityId = EntitiesHelper::getEntityId();

        $journalEditorialData = $validatedData->except(['image']);
        $journalEditorialData->put('journal_id', $currentEntityId);
        $journalEditorialData = $journalEditorialData->toArray();

        $journalEditorial = $this->journalEditorialRepository->create($journalEditorialData);

        StorageHelper::upload($journalEditorial, $request->file('image'), 'image');

        return ['message' => 'success', 'code' => 200, 'id' => $journalEditorial->id];
    }

    public function show($id)
    {
        return $this->journalEditorialRepository->show($id);
    }


    public function update(UpdateJournalEditorialRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $journalEditorialData = $validatedData->except(['image']);
        $journalEditorialData = $journalEditorialData->toArray();
        $journalEditorial = $this->journalEditorialRepository->update($journalEditorialData, $id);
        if ($validatedData->has('image')) {
            StorageHelper::upload($journalEditorial, $request->file('image'), 'image');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->journalEditorialRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }
}
