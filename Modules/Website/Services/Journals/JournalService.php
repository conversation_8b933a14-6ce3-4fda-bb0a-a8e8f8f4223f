<?php


namespace Modules\Website\Services\Journals;


use App\Models\EntityType;
use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use App\Helpers\Utils;
use Modules\Website\Http\Requests\Journal\StoreJournalRequest;
use Modules\Website\Http\Requests\Journal\UpdateJournalRequest;
use Modules\Website\Repositories\Journals\JournalEditorialRepositoryInterface;
use Modules\Website\Repositories\Journals\JournalIssueArticleRepositoryInterface;
use Modules\Website\Repositories\Journals\JournalIssueRepository;
use Modules\Website\Repositories\Journals\JournalIssueRepositoryInterface;
use Modules\Website\Repositories\Journals\JournalRepositoryInterface;

class JournalService
{

    /**
     * @var JournalRepositoryInterface
     */
    private $journalRepository;
    /**
     * @var JournalIssueRepositoryInterface
     */
    private JournalIssueRepositoryInterface $journalIssueRepository;
    /**
     * @var JournalIssueArticleRepositoryInterface
     */
    private JournalIssueArticleRepositoryInterface $journalIssueArticleRepository;
    /**
     * @var JournalEditorialRepositoryInterface
     */
    private JournalEditorialRepositoryInterface $journalEditorialRepository;

    public function __construct(
        JournalRepositoryInterface  $journalRepository,
        JournalIssueRepositoryInterface $journalIssueRepository,
        JournalIssueArticleRepositoryInterface $journalIssueArticleRepository,
        JournalEditorialRepositoryInterface $journalEditorialRepository
    )
    {
        $this->journalRepository = $journalRepository;
        $this->journalIssueRepository = $journalIssueRepository;
        $this->journalIssueArticleRepository = $journalIssueArticleRepository;
        $this->journalEditorialRepository = $journalEditorialRepository;
    }

    public function getAll()
    {
        return $this->journalRepository->getAll();
    }

    public function create(StoreJournalRequest $request)
    {
        $validatedData = collect($request->validated());
        $currentEntityId = EntitiesHelper::getEntityId();

        $journalEntityData = $validatedData->except(['description', 'about', 'author_guide_lines', 'publication_rules', 'entity_cover', 'printing_issn', 'online_issn']);
        $journalEntityData->put('parent_entity_id', $currentEntityId);
        $journalEntityData->put('type', EntityType::JOURNAL);
        $journalEntityData = $journalEntityData->toArray();

        $journalDetailsData = $validatedData->only(['description', 'about', 'author_guide_lines', 'publication_rules', 'printing_issn', 'online_issn'])->toArray();
        $journal = $this->journalRepository->create($journalEntityData, $journalDetailsData);

        StorageHelper::upload($journal, $request->file('entity_cover'), 'cover');

        if(!app()->environment('testing')) {
            Utils::addDomain($journalEntityData['id']);
        }
        return ['message' => 'success', 'code' => 200, 'id' => $journal->id];
    }

    public function show($id)
    {
        return $this->journalRepository->show($id);
    }


    public function update(UpdateJournalRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $journalEntityData = $validatedData->except(['description', 'about', 'author_guide_lines', 'publication_rules', 'entity_cover', 'printing_issn', 'online_issn'])->toArray();
        $journalDetailsData = $validatedData->only(['description', 'about', 'author_guide_lines', 'publication_rules', 'printing_issn', 'online_issn'])->toArray();
        $journal = $this->journalRepository->update($journalEntityData, $journalDetailsData, $id);
        if ($validatedData->has('entity_cover')) {
            StorageHelper::upload($journal, $request->file('entity_cover'), 'cover');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->journalRepository->delete($id);
        if(!app()->environment('testing')) {
            Utils::removeDomain($id);
//            dispatch(function() {
//                Utils::fixDomains();
//            });
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function getStatistics()
    {
        $issuesIds = $this->journalIssueRepository->getIssuesIds();
        $articlesCount = $this->journalIssueArticleRepository->getCount($issuesIds->pluck('id')->toArray());
        $editorialsCount = $this->journalIssueArticleRepository->getAuthorsCount($issuesIds->pluck('id')->toArray());
        return ['issues_count' => $issuesIds->count(), 'articles_count' => $articlesCount, 'editorials_count' => $editorialsCount];
    }

    public function getPagination($elementsPerPage, $search)
    {
        return $this->journalRepository->getPagination($elementsPerPage, $search);
    }
}
