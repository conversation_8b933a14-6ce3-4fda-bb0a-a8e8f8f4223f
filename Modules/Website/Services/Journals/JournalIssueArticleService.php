<?php


namespace Modules\Website\Services\Journals;


use App\Helpers\StorageHelper;
use Modules\Website\Http\Requests\Journal\StoreJournalIssueArticleRequest;
use Modules\Website\Http\Requests\Journal\UpdateJournalIssueArticleRequest;
use Modules\Website\Repositories\Journals\JournalIssueArticleRepositoryInterface;

class JournalIssueArticleService
{
    /**
     * @var JournalIssueArticleRepositoryInterface
     */
    private $journalIssueArticleRepository;

    public function __construct(JournalIssueArticleRepositoryInterface  $journalIssueArticleRepository)
    {
        $this->journalIssueArticleRepository = $journalIssueArticleRepository;
    }
    public function getAll($journalIssueId)
    {
        return $this->journalIssueArticleRepository->getAll($journalIssueId);
    }

    public function create(StoreJournalIssueArticleRequest $request)
    {
        $validatedData = collect($request->validated());

        $journalIssueArticleData = $validatedData->except(['article_file', 'authors']);
        $journalIssueArticleData = $journalIssueArticleData->toArray();
        $authorsData = $validatedData->only('authors')->flatten(1)->toArray();

        $journalIssueArticle = $this->journalIssueArticleRepository->create($journalIssueArticleData);
        $this->journalIssueArticleRepository->addAuthors($authorsData, $journalIssueArticle->id);

        if ($validatedData->has('article_file')) {
            StorageHelper::upload($journalIssueArticle, $request->article_file[0], 'file');
        }

        return ['message' => 'success', 'code' => 200, 'id' => $journalIssueArticle->id];
    }

    public function show($journalIssueId, $id)
    {
        return $this->journalIssueArticleRepository->show($journalIssueId, $id);
    }


    public function update($journalIssueId, UpdateJournalIssueArticleRequest $request, $id)
    {
        $validatedData = collect($request->validated());
        $journalIssueArticleData = $validatedData->except(['article_file', 'authors', 'removed_article_file']);
        $journalIssueArticleData = $journalIssueArticleData->toArray();
        $authorsData = $validatedData->only('authors')->flatten(1)->toArray();

        $journalIssueArticle = $this->journalIssueArticleRepository->update($journalIssueId, $journalIssueArticleData, $id);
        $this->journalIssueArticleRepository->deleteAllAuthors($id);
        $this->journalIssueArticleRepository->addAuthors($authorsData, $id);

        if($request->has('removed_article_file')) {
            StorageHelper::deleteMedia($journalIssueArticle, $request->removed_article_file);
        }

        if ($validatedData->has('article_file')) {
            StorageHelper::upload($journalIssueArticle, $request->article_file[0], 'file');
        }
        return ['message' => 'success', 'code' => 200];
    }

    public function delete($journalIssueId, $id)
    {
        $this->journalIssueArticleRepository->delete($journalIssueId, $id);
        return ['message' => 'success', 'code' => 200];
    }
}
