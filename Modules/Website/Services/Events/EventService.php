<?php


namespace Modules\Website\Services\Events;


use App\Helpers\EntitiesHelper;
use App\Helpers\StorageHelper;
use Modules\Website\DTOs\PaginationData;
use Modules\Website\Http\Requests\Event\StoreEventRequest;
use Modules\Website\Http\Requests\Event\UpdateEventRequest;
use Modules\Website\Repositories\Events\EventRepositoryInterface;

class EventService
{

    /**
     * @var EventRepositoryInterface
     */
    private $eventRepository;

    /**
     * NewsService constructor.
     * @param EventRepositoryInterface $eventRepository
     */
    public function __construct(EventRepositoryInterface $eventRepository)
    {
        $this->eventRepository = $eventRepository;
    }

    public function getPagination(PaginationData $eventData)
    {
        return $this->eventRepository->getPagination($eventData);
    }

    public function show($id)
    {
        return $this->eventRepository->show($id);
    }

    public function create(StoreEventRequest $request)
    {
        $currentEntityId = EntitiesHelper::getEntityId();

        $validatedData = collect($request->validated());
        $eventData = $validatedData->except('image')->toArray();

        $eventData['entity_id'] = $currentEntityId;
        $eventData['created_by'] = auth()->user()->id;
        $eventData['updated_by'] = auth()->user()->id;
        $event = $this->eventRepository->create($eventData);
        StorageHelper::upload($event, $request->file('image'));
        return ['message' => 'success', 'code' => 200];
    }

    public function update(UpdateEventRequest $request, $id)
    {

        $validatedData = collect($request->validated());
        $eventData = $validatedData->except('image')->toArray();

        $eventData['updated_by'] = auth()->user()->id;
        $event = $this->eventRepository->update($eventData, $id);
        if ($validatedData->has('image')) {
            StorageHelper::upload($event, $request->file('image'));
        }

        return ['message' => 'success', 'code' => 200];
    }

    public function delete($id)
    {
        $this->eventRepository->delete($id);
        return ['message' => 'success', 'code' => 200];
    }

    public function getNonExpired()
    {
        return $this->eventRepository->getNonExpired();
    }

}
