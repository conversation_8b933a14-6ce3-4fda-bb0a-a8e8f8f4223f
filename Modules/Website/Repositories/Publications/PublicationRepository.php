<?php


namespace Modules\Website\Repositories\Publications;


use App\Models\User;
use Modules\Website\DTOs\PaginationData;
use Modules\Website\Entities\Publication;

class PublicationRepository implements PublicationRepositoryInterface
{
    private bool $withoutGlobalScopes = false;
    private bool $inDescendingEntities = false;

    /**
     * @param bool $withoutGlobalScopes
     */
    public function setWithoutGlobalScopes(bool $withoutGlobalScopes): void
    {
        $this->withoutGlobalScopes = $withoutGlobalScopes;
    }

    public function setInDescendingEntities(bool $inDescendingEntities)
    {
        $this->inDescendingEntities = $inDescendingEntities;
    }

    public function getPagination(PaginationData $publicationData)
    {
        $publication = Publication::query();

        $publication
            ->when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities());

        $publication->with(['entity', 'authors' => fn ($query) => $query->orderBy('author_sequence'), 'file', 'publicationType', 'publisher']);

        if ($publicationData->search) $publication->search($publicationData->search);

        if ($publicationData->sortKey && $publicationData->sortType) {
            $publication->orderBy($publicationData->sortKey ?: 'created_at', $publicationData->sortType ?: 'desc');
        } else {
            $publication->orderByDesc('year');
            $publication->orderByDesc('month');
        }
        return $publication->paginate($publicationData->perPage);
    }

    public function getUserPublications($userId)
    {
        return User::find($userId)->publications()
            ->when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->orderByDesc('year')
            ->orderByDesc('month')
            ->with('authors', 'file', 'publicationType')->get();
    }

    public function show($id)
    {
        return Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->with('authors', 'file')->findOrFail($id);
    }

    public function create($publicationData)
    {
        return Publication::create($publicationData);
    }

    public function update($publicationData, $id)
    {
        $publication = Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->where('id', $id)->first();

        $publication->update($publicationData);
        return $publication;
    }

    public function delete($id)
    {
        Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->where('id', $id)->delete();
    }

    public function addAuthors($authorsData, $id)
    {
        Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->find($id)->authors()->createMany($authorsData);
    }

    public function deleteAuthors($authorsIds, $id)
    {
        Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->find($id)->authors()->whereIn('id', $authorsIds)->delete();
    }

    public function deleteAllAuthors($id)
    {
        Publication::when($this->withoutGlobalScopes, fn ($query) => $query->withoutGlobalScopes())
            ->when($this->inDescendingEntities, fn ($query) => $query->inDescendingEntities())
            ->find($id)->authors()->delete();
    }
}
