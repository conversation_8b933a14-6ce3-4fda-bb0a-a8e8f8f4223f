<?php


namespace Modules\Website\Repositories\Guides;

use Modules\Website\DTOs\PaginationData;

interface GuideRepositoryInterface
{
    public function getAll();

    public function getPagination(PaginationData $guideData);

    public function getLatest($numberOfElements);

    public function create($guideData);

    public function update($guideData, $id);

    public function show($id);

    public function delete($id);
}
