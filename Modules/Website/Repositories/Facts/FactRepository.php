<?php


namespace Modules\Website\Repositories\Facts;


use Modules\Website\Entities\Fact;

class FactRepository implements FactRepositoryInterface
{
    public function getAll()
    {
        return Fact::get();
    }

    public function getAllByLang($lang)
    {
        return Fact::lang($lang)->get();
    }

    public function show($id)
    {
        return Fact::find($id);
    }

    public function create($factData)
    {
        return Fact::create($factData);
    }

    public function update($factData, $id)
    {
        Fact::where('id', $id)->update($factData);
    }

    public function delete($id)
    {
        Fact::destroy($id);
    }
}
