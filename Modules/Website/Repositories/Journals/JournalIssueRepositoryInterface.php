<?php


namespace Modules\Website\Repositories\Journals;


interface JournalIssueRepositoryInterface
{

    public function getAll();

    public function getPagination($elementsPerPage, $year);

    public function getAllYears();

    public function getLatest($numberOfElements);

    public function create($journalIssueData);

    public function show($id);

    public function update($journalIssueData, $id);

    public function delete($id);

    public function getIssuesIds();


}
