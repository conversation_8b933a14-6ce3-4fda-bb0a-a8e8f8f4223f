<?php


namespace Modules\Website\Repositories\Journals;


use Modules\Website\Entities\JournalIssueArticle;

class JournalIssueArticleRepository implements JournalIssueArticleRepositoryInterface
{
    public function getAll($journalIssueId)
    {
        return JournalIssueArticle::inIssue($journalIssueId)->with('authors', 'file')->get();
    }

    public function create($journalIssueArticleData)
    {
        return JournalIssueArticle::create($journalIssueArticleData);
    }

    public function show($journalIssueId, $id)
    {
        return JournalIssueArticle::inIssue($journalIssueId)->with('authors', 'file')->findOrFail($id);
    }

    public function update($journalIssueId, $journalIssueArticleData, $id)
    {
        $journalIssueArticle = JournalIssueArticle::inIssue($journalIssueId)->findOrFail($id);
        $journalIssueArticle->update($journalIssueArticleData);
        return $journalIssueArticle;
    }

    public function delete($journalIssueId, $id)
    {
        JournalIssueArticle::inIssue($journalIssueId)->where('id', $id)->delete();
    }

    public function addAuthors($authorsData, $id)
    {
        JournalIssueArticle::find($id)->authors()->createMany($authorsData);
    }

    public function deleteAllAuthors($id)
    {
        JournalIssueArticle::find($id)->authors()->delete();
    }

    public function getCount(array $issuesIds)
    {
        return JournalIssueArticle::inIssues($issuesIds)->count();
    }

    public function getAuthorsCount(array $issuesIds)
    {
        $articles = JournalIssueArticle::inIssues($issuesIds)->withCount('authors')->get();
        return $articles->sum('authors_count');
    }
}
