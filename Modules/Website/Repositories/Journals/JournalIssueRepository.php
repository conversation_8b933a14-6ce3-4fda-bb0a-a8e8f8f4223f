<?php


namespace Modules\Website\Repositories\Journals;


use Modules\Website\Entities\JournalIssue;

class JournalIssueRepository implements JournalIssueRepositoryInterface
{

    public function getAll()
    {
        return JournalIssue::with('cover')
            ->orderBy('year', 'desc')
            ->orderBy('issue', 'asc')
            ->get();
    }

    public function getPagination($elementsPerPage, $year)
    {
        $journalIssues = JournalIssue::query();
        $journalIssues->with(['cover']);

        if($year) $journalIssues->year($year);

        $journalIssues
            ->orderByDesc('year')
            ->orderByDesc('month');

        return $journalIssues->paginate($elementsPerPage);
    }

    public function getAllYears()
    {
        return JournalIssue::select('year')->distinct()->orderByDesc('year')->get()->pluck('year');
    }

    public function getLatest($numberOfElements) {
        return JournalIssue::with('cover')->take($numberOfElements)
            ->orderByDesc('year')
            ->orderByDesc('month')
            ->get();
    }

    public function create($journalIssueData)
    {
        return JournalIssue::create($journalIssueData);
    }

    public function show($id)
    {
        return JournalIssue::with('cover')->find($id);
    }

    public function update($journalIssueData, $id)
    {
        $issue = JournalIssue::find($id);
        $issue->update($journalIssueData);
        return $issue;
    }

    public function delete($id)
    {
        JournalIssue::destroy($id);
    }

    public function getIssuesIds()
    {
        return JournalIssue::get(['id']);
    }
}
