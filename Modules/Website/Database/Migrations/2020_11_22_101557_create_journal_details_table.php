<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateJournalDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('journal_details', function (Blueprint $table) {
            $table->increments('id');
            $table->char('journal_id', 30);
            $table->string('printing_issn');
            $table->string('online_issn');
            $table->json('description');
            $table->json('about');
            $table->json('author_guide_lines');
            $table->json('publication_rules');

            $table->timestamps();

            $table->foreign('journal_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('')->dropIfExists('journal_details');
    }
}
