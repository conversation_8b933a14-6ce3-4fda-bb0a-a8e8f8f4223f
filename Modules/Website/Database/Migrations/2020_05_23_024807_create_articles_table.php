<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('articles', function (Blueprint $table) {
            $table->increments('id');
            $table->char('entity_id', 30);
            $table->unsignedInteger('author_id');
            $table->json('title');
            $table->string('slug')->unique();
            $table->json('description');

            $table->timestamps();

            $table->foreign('entity_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('author_id')->references('id')->on(config('database.connections.main_db.database') . '.users')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.website_connection'))->dropIfExists('articles');
    }
}
