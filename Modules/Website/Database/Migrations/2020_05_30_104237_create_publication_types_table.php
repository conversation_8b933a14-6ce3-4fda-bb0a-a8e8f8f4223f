<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePublicationTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('publication_types', function (Blueprint $table) {
            $table->tinyIncrements('id');
            $table->json('title');
            $table->boolean('has_volume')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.website_connection'))->dropIfExists('publication_types');
    }
}
