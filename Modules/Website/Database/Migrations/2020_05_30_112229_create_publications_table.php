<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePublicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('publications', function (Blueprint $table) {
            $table->increments('id');
            $table->char('entity_id', 30);
            $table->unsignedInteger('created_by');
            $table->unsignedTinyInteger('type');
            $table->char('lang', 6);
            $table->tinyInteger('month');
            $table->smallInteger('year');
            $table->string('title');
            $table->text('abstract');
            $table->string('location');
            $table->unsignedInteger('publisher_id');
            $table->string('publication_link');
            $table->smallInteger('volume')->nullable()->default(null);
            $table->smallInteger('number')->nullable()->default(null);
            $table->smallInteger('pages')->nullable()->default(null);

            $table->timestamps();

            $table->foreign('entity_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onDelete('restrict')->onUpdate('cascade');
            $table->foreign('lang')->references('id')->on(config('database.connections.main_db.database') . '.languages')->onDelete('restrict')->onUpdate('cascade');
            $table->foreign('publisher_id')->references('id')->on('publishers')->onDelete('restrict')->onUpdate('cascade');
            $table->foreign('type')->references('id')->on('publication_types')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('created_by')->references('id')->on(config('database.connections.main_db.database') . '.users')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.website_connection'))->dropIfExists('publications');
    }
}
