<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('events', function (Blueprint $table) {
            $table->increments('id');
            $table->char('entity_id', 30);
            $table->unsignedInteger('category_id');
            $table->json('title');
            $table->string('slug')->unique();
            $table->json('description');
            $table->json('place');
            $table->double('longitude')->nullable();
            $table->double('latitude')->nullable();
            $table->date('start_date');
            $table->date('end_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->unsignedInteger('created_by');
            $table->unsignedInteger('updated_by');

            $table->timestamps();

            $table->foreign('entity_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('created_by')->references('id')->on(config('database.connections.main_db.database') . '.users')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('updated_by')->references('id')->on(config('database.connections.main_db.database') . '.users')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('category_id')->references('id')->on('event_categories')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.website_connection'))->dropIfExists('events');
    }
}
