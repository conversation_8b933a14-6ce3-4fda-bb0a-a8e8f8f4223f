<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateJournalIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.website_connection'))->create('journal_issues', function (Blueprint $table) {
            $table->increments('id');
            $table->char('journal_id', 30);
            $table->smallInteger('issue');
            $table->smallInteger('volume');
            $table->tinyInteger('month')->unsigned();
            $table->smallInteger('year')->unsigned();
            $table->timestamps();

            $table->foreign('journal_id')->references('id')->on(config('database.connections.main_db.database') . '.entities')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('')->dropIfExists('journal_issues');
    }
}
