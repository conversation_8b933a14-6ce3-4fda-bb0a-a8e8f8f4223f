<?php

namespace Modules\Website\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\Website\Entities\NewsCategory;

class NewsCategoryTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        $newsCategories = collect([
            [
                'id' => 1,
                'title' => ['ar' => 'أخبار عامة', 'en' => 'General News']
            ],
            [
                'id' => 2,
                'title' => ['ar' => 'البحث العلمي', 'en' => 'Research']
            ],
            [
                'id' => 3,
                'title' => ['ar' => 'ورشة عمل', 'en' => 'Workshop']
            ],
            [
                'id' => 4,
                'title' => ['ar' => 'نشاط', 'en' => 'Activity']
            ],
            [
                'id' => 5,
                'title' => ['ar' => 'مؤتمر علمي', 'en' => 'Conference']
            ],
            [
                'id' => 6,
                'title' => ['ar' => 'معرض', 'en' => 'Exhibition']
            ],
            [
                'id' => 7,
                'title' => ['ar' => 'إتفاقيات', 'en' => 'Agreements']
            ]
        ]);
        $newsCategories->each(function ($newsCategory) {
            NewsCategory::updateOrCreate(['id' => $newsCategory['id']], $newsCategory);
        });
    }
}
