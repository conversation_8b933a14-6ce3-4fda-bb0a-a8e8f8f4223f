<?php

namespace Modules\Website\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Website\Entities\Publication;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Route::bind('user_publication', fn($value) => Publication::withoutGlobalScopes()->whereId($value)->firstOrFail());
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware(['web'])
            ->group(function () {
                require module_path('Website', '/Routes/global.web.php');
            });

        Route::middleware(['web', 'setLang'])
            ->prefix('{lang}')
            ->where(['lang' => '[a-zA-Z]{2}'])
            ->group(function () {
                require module_path('Website', '/Routes/web.php');
            });
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
            ->middleware(['api'])
            ->group(function () {
                require module_path('Website', '/Routes/api.php');
            });
    }
}
