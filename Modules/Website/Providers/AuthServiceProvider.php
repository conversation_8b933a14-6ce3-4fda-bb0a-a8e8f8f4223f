<?php

namespace Modules\Website\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Modules\Website\Entities\Journal;
use Modules\Website\Entities\Publication;
use Modules\Website\Policies\JournalPolicy;
use Modules\Website\Policies\UserPublicationPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
        Publication::class => UserPublicationPolicy::class,
        Journal::class => JournalPolicy::class
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
