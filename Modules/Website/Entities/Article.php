<?php

namespace Modules\Website\Entities;

use App\Models\Media;
use App\Scopes\InCurrentEntityScope;
use App\Traits\HasLang;
use App\Traits\Searchable;
use App\Models\User;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use App\Traits\HasTranslations;


class Article extends Model implements HasMedia, Auditable
{
    use HasTranslations, HasSlug, HasLang, Searchable, InteractsWithMedia, \OwenIt\Auditing\Auditable;

    public $translatable = ['title', 'description'];
    public $searchableFields = ['title', 'description'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.website_connection'));
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('default')
            ->singleFile();
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'model');
    }


    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        if ($this->title == null || $this->title == '') {
            app()->setLocale('en');
        }
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new InCurrentEntityScope());
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }
}
