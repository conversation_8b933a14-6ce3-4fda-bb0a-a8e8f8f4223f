<?php

namespace Modules\Website\Entities;

use App\Models\Media;
use App\Scopes\InCurrentEntityScope;
use App\Traits\HasLang;
use <PERSON>yvoy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use <PERSON>tie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use App\Traits\HasTranslations;
use App\Traits\Searchable;

class Guide extends Model implements HasMedia, Auditable
{
    use HasTranslations, HasLang, Searchable, InteractsWithMedia, \OwenIt\Auditing\Auditable;

    public $translatable = ['title'];
    public $searchableFields = ['title'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.website_connection'));
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new InCurrentEntityScope());
    }

    public function registerMediaCollections(): void
    {
        $this
            ->addMediaCollection('default')
            ->singleFile();
    }

    public function file()
    {
        return $this->morphOne(Media::class, 'model');
    }
}
