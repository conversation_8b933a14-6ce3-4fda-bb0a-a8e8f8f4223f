<?php


namespace Modules\Website\Entities;


use App\Models\Entity;
use App\Models\EntityType;
use App\Helpers\Utils;
use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Builder;
use OwenIt\Auditing\Contracts\Auditable;


class Journal extends Entity implements Auditable
{

    use Searchable, \OwenIt\Auditing\Auditable;
    public $searchableFields = ['title'];

    protected $table = 'entities';

    protected static function booted()
    {
        static::addGlobalScope('typeJournal', function (Builder $builder) {
            $builder->where('type', '=', EntityType::JOURNAL);
        });
    }

    public function getUrlAttribute()
    {
        $url = 'http://';
        $appUrl = Utils::removeHttp(config('app.url'));
        $url .= $this->id . '.' . $appUrl;
        return $url;
    }

    public function journalDetails()
    {
        return $this->hasOne(JournalDetails::class, 'journal_id', 'id');
    }

    public function journalEditorials()
    {
        return $this->hasOne(JournalEditorial::class, 'journal_id', 'id');
    }

    public function journalIssues()
    {
        return $this->hasOne(JournalEditorial::class, 'journal_id', 'id');
    }
}
