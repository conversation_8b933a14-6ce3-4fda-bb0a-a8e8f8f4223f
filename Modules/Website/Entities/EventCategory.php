<?php

namespace Modules\Website\Entities;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use App\Traits\HasTranslations;


class EventCategory extends Model
{
    use HasTranslations, Cachable;

    public $translatable = ['title'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.website_connection'));
    }
}
