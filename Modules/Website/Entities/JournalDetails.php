<?php

namespace Modules\Website\Entities;

use Hoyvoy\CrossDatabase\Eloquent\Model;
use App\Traits\HasTranslations;


class JournalDetails extends Model
{

    use HasTranslations;
    public $translatable = ['description', 'about', 'author_guide_lines', 'publication_rules'];
    public $searchableFields = ['description', 'about', 'author_guide_lines', 'publication_rules'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.website_connection'));
    }
}
