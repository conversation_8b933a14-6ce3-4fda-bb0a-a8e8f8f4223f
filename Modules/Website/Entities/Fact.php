<?php

namespace Modules\Website\Entities;

use App\Scopes\InCurrentEntityScope;
use App\Traits\HasLang;
use Hoyvoy\CrossDatabase\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use App\Traits\HasTranslations;


class Fact extends Model implements Auditable
{
    use HasTranslations, HasLang, \OwenIt\Auditing\Auditable;

    public $translatable = ['title'];

    protected $hidden = ['created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->setConnection(config('database.website_connection'));
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new InCurrentEntityScope());
    }

}
