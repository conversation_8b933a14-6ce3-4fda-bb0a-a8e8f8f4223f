<?php

namespace Modules\Website\Http\Requests\Publication;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePublicationRequest extends FormRequest
{
    use PublicationCustomRule;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->commonRules(), [
            'removed_publication_file' => ['sometimes', 'nullable', 'array'],
            'removed_publication_file.*' => ['sometimes', 'required', 'integer']
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
