<?php

namespace Modules\Website\Http\Requests\Album;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexAlbumsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'per_page' => ['sometimes', 'integer'],
            'sort_key' => ['sometimes', Rule::in(['title', 'date', 'category_id'])],
            'sort_type' => ['sometimes', Rule::in(['asc', 'desc'])],
            'search' => ['sometimes', 'nullable'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
