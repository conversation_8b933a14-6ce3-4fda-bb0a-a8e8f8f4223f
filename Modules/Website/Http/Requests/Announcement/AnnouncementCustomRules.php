<?php


namespace Modules\Website\Http\Requests\Announcement;


use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait AnnouncementCustomRules
{

    public function commonRules()
    {
        return [
            'title' => ['required', new JsonTranslationsArray],
            'description' => ['required', new JsonTranslationsArray],
            'date' => ['required', 'date_format:Y-m-d'],
            'expiration_date' => ['required', 'date_format:Y-m-d'],
            'targets' => ['required', 'nullable', Rule::in(['public', 'students', 'lecturers', 'employees'])]
        ];
    }
}
