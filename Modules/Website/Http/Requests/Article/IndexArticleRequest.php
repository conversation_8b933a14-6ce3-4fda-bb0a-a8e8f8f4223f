<?php

namespace Modules\Website\Http\Requests\Article;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexArticleRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'per_page' => ['sometimes', 'integer'],
            'sort_key' => ['sometimes', Rule::in(['title'])],
            'sort_type' => ['sometimes', Rule::in(['asc', 'desc'])],
            'search' => ['sometimes', 'nullable'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
