<?php


namespace Modules\Website\Http\Requests\Journal;


use App\Rules\JsonTranslationsArray;
use Illuminate\Validation\Rule;

trait JournalEditorialCustomRule
{
    public function commonRules()
    {
        return [
            'name' => ['required', new JsonTranslationsArray],
            'affiliation' => ['required', new JsonTranslationsArray],
            'email' => ['sometimes', 'nullable', 'email'],
            'journal_position_id' => ['required', Rule::exists(config('database.website_connection').'.journal_positions', 'id')],
        ];
    }
}
