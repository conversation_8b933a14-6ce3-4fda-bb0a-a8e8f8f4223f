<?php

namespace Modules\Website\Http\Requests\Journal;

use Illuminate\Foundation\Http\FormRequest;

class StoreJournalIssueRequest extends FormRequest
{
    use JournalIssueCustomRule;
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge($this->commonRules(), [
            'cover' => ['required', 'image'],
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
