<?php


namespace Modules\Website\Http\Requests\Slider;


trait SliderCustomRules
{
    public function commonRules()
    {
        return [
            'title' => ['present', 'nullable', 'string', 'max:255'],
            'subtitle' => ['present', 'nullable', 'max:255'],
            'button_link' => ['present', 'nullable', 'url', 'max:255'],
            'button_title' => ['present', 'nullable', 'max:255'],
        ];
    }
}
