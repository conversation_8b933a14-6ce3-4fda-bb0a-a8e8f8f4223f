<?php

namespace Modules\Website\Http\Controllers;

use App\Models\Lecturer;
use App\Services\Lecturers\LecturerService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;

class LecturerController extends Controller
{

    /**
     * @var LecturerService
     */
    private $lecturerService;

    /**
     * AlbumController constructor.
     * @param LecturerService $lecturerService
     */
    public function __construct(LecturerService $lecturerService)
    {
        $this->lecturerService = $lecturerService;
    }

    /**
     * Display a listing of the resource.
     * @return Application|Factory|View
     */
    public function index(Request $request)
    {
        $lecturers = $this->lecturerService->getPagination(16, $request->search, false, true);
        return view('website::lecturers.index', compact('lecturers'));
    }


    /**
     * Show the specified resource.
     * @param Lecturer $lecturer
     * @return Application|Factory|View
     */
    public function show(Lecturer $lecturer)
    {
        if($lecturer->user->status != 'active' || $lecturer->type == Lecturer::COOPERATIVE_TYPE) {
            abort(404);
        }
        $lecturer->load([
            'user.publications' => fn($query) => $query->withoutGlobalScopes()
                ->with(['authors', 'file', 'publicationType', 'publisher'])
                ->orderByDesc('year')
                ->orderByDesc('month')
            ,
            'user.socialLinks',
            'qualification',
            'academicRank',
            'user.entity',
            'user.entity.parent',
            'user.avatar',
            'resume'
        ]);
        abort_if(! $lecturer->user->entity, 404);

        return view('website::lecturers.show', compact('lecturer'));
    }

    public function downloadCV(Lecturer $lecturer)
    {
        return $lecturer->resume ?? redirect()->back();
    }
}
