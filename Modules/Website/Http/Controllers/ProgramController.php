<?php

namespace Modules\Website\Http\Controllers;

use App\Models\EntityType;
use App\Models\Program;
use App\Services\Programs\ProgramService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\Website\DTOs\ProgramData;

class ProgramController extends Controller
{

    /**
     * @var ProgramService
     */
    private $programService;

    /**
     * AlbumController constructor.
     * @param ProgramService $programService
     */
    public function __construct(ProgramService $programService)
    {
        $this->programService = $programService;
    }

    /**
     * Display a listing of the resource.
     * @param Request $request
     * @return Application|Factory|View
     */
    public function index(Request $request)
    {
        $programData = ProgramData::fromRequest($request);
        $programs = $this->programService->getPagination($programData);
        $currentEntity = clone app('current_entity');
        if ($currentEntity->isUniversity()) {
            $currentEntity->load(['directChildren' => fn($query) => $query->where('type', EntityType::SCHOOL)->with('directChildren')]);
        }
        return view('website::programs.index', compact('programs', 'currentEntity'));
    }

    /**
     * Show the specified resource.
     * @param Program $program
     * @return Application|Factory|View
     */
    public function show(Program $program)
    {
        abort_if(!$program->is_active, 404);
        $program->load([
            'subjects' => fn($query) => $query->where('is_active', '1')->with(['subjectType', 'prerequisites']),
        ]);

        return view('website::programs.show', compact('program'));
    }
}
