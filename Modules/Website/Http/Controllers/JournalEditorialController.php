<?php

namespace Modules\Website\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Website\Repositories\Journals\JournalPositionRepositoryInterface;
use Modules\Website\Services\Journals\JournalEditorialService;
use Modules\Website\Transformers\Journal\JournalEditorialResource;

class JournalEditorialController extends Controller
{
    /**
     * @var JournalEditorialService
     */
    private $journalEditorialService;
    /**
     * @var JournalPositionRepositoryInterface
     */
    private JournalPositionRepositoryInterface $journalPositionRepository;

    public function __construct(JournalEditorialService $journalEditorialService, JournalPositionRepositoryInterface $journalPositionRepository)
    {
        $this->journalEditorialService = $journalEditorialService;
        $this->journalPositionRepository = $journalPositionRepository;
    }

    /**
     * Display a listing of the resource.
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View
     */
    public function __invoke()
    {
        $journalEditorials = $this->journalEditorialService->getAll();
        $positions = $this->journalPositionRepository->getAll();
        for($i = 0; $i < $positions->count(); $i++) {
            $editorials = $journalEditorials->where('journal_position_id', '=', $positions->get($i)->id);
            $positions->get($i)->editorials = $editorials;
        }
        return view('website::journals.editorials', compact('positions'));
    }
}
