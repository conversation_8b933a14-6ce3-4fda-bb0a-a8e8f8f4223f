<?php

namespace Modules\Website\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Website\Entities\Album;
use Modules\Website\Entities\Announcement;
use Modules\Website\Entities\Article;
use Modules\Website\Entities\Event;
use Modules\Website\Entities\News;
use Modules\Website\Entities\Publication;
use Modules\Website\Entities\Video;

class DashboardController extends Controller
{
    public function index()
    {
        $currentEntity = app('current_entity');
        $entityTree = $currentEntity->childrenAndSelf();
        $entitiesIds = $currentEntity->getIds($entityTree);

        $news = News::where('entity_id', $currentEntity->id)->count();
        $events = Event::where('entity_id', $currentEntity->id)->count();
        $albums = Album::where('entity_id', $currentEntity->id)->count();
        $announcements = Announcement::where('entity_id', $currentEntity->id)->count();
        $articles = Article::where('entity_id', $currentEntity->id)->count();
        $publications = Publication::whereIn('entity_id', $entitiesIds)->withoutGlobalScopes()->count();
        $videos = Video::where('entity_id', $currentEntity->id)->count();

        $result = [
            'news' => $news,
            'events' => $events,
            'albums' => $albums,
            'announcements' => $announcements,
            'articles' => $articles,
            'publications' => $publications,
            'videos' => $videos,
        ];

        return response()->json($result);
    }
}
