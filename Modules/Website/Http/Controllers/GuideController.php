<?php

namespace Modules\Website\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Website\DTOs\PaginationData;
use Modules\Website\Entities\Guide;
use Modules\Website\Http\Requests\Guide\IndexGuideRequest;
use Modules\Website\Services\Guides\GuideService;

class GuideController extends Controller
{
    /**
     * @var GuideService
     */
    private $guideService;

    /**
     * NewsController constructor.
     * @param GuideService $guideService
     */
    public function __construct(GuideService $guideService)
    {
        $this->guideService = $guideService;
    }

    public function index(IndexGuideRequest $request)
    {
        $guideData = PaginationData::fromRequestForPaginationOnly(9, $request);
        $guides = $this->guideService->getPagination($guideData);

        return view('website::guides.index', compact('guides'));
    }

    public function download(Guide $guide)
    {
        return $guide->file ?? redirect()->back();
    }
}
