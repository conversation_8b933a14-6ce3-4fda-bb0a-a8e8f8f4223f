<?php

namespace Modules\Website\Http\Controllers;

use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\URL;
use Illuminate\View\View;
use Modules\Website\DTOs\AnnouncementData;
use Modules\Website\Entities\Announcement;
use Modules\Website\Http\Requests\Announcement\IndexAnnouncementRequest;
use Modules\Website\Services\Announcements\AnnouncementService;
use Str;

class AnnouncementController extends Controller
{

    /**
     * @var AnnouncementService
     */
    private $announcementService;

    /**
     * NewsController constructor.
     * @param AnnouncementService $announcementService
     */
    public function __construct(AnnouncementService $announcementService)
    {
        $this->announcementService = $announcementService;
    }

    /**
     * Display a listing of the resource.
     * @param IndexAnnouncementRequest $request
     * @return Application|Factory|View
     */
    public function index(IndexAnnouncementRequest $request)
    {
        $announcementData = AnnouncementData::fromRequestForPaginationOnly(9, $request);
        $announcements = $this->announcementService->getPagination($announcementData);
        return view('website::announcements.index', compact('announcements'));
    }

    public function show(Announcement $announcement)
    {
        OpenGraph::setTitle($announcement->title);
        OpenGraph::setDescription(Str::limit(preg_replace('#<[^>]+>#', ' ', $announcement->description), 200));
        OpenGraph::setUrl(Url::current());
        OpenGraph::addProperty('type', 'article');
        OpenGraph::addProperty('locale', 'ar');
        OpenGraph::addProperty('locale:alternate', ['en-us']);
        OpenGraph::addImage($announcement->image?->getFullUrl(), ['height' => 300, 'width' => 300]);

        TwitterCard::setTitle($announcement->title);
        TwitterCard::setDescription(Str::limit(preg_replace('#<[^>]+>#', ' ', $announcement->description), 200));
        TwitterCard::setImage($announcement->image?->getFullUrl());
        TwitterCard::setSite(Url::current());

        if (!$announcement->hasLang(app()->getLocale()) || !$announcement->approved) {
            abort(404);
        }

        return view('website::announcements.show', compact('announcement'));
    }


    /**
     * @param IndexAnnouncementRequest $request
     * @return Application|Factory|View
     */
    public function indexDescending(IndexAnnouncementRequest $request)
    {
        $announcements = $this->announcementService->getPaginationForSubEntities(10, null, null, $request->search);
        return view('website::announcements.index', compact('announcements'));
    }

}
