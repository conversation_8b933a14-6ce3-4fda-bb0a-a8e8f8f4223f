<?php

namespace Modules\Website\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Website\Services\Journals\JournalService;

class JournalController extends Controller
{

    /**
     * @var JournalService
     */
    private $journalService;

    /**
     * PublicationController constructor.
     * @param JournalService $journalService
     */
    public function __construct(JournalService $journalService)
    {
        $this->journalService = $journalService;
    }

    /**
     * Display a listing of the resource.
     * @param Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        $journals = $this->journalService->getPagination(12, $request->search);
        return view('website::all-journals', compact('journals'));
    }
}
