<?php

namespace Modules\Website\Http\Controllers\Api;

use App\Helpers\EntitiesHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Website\DTOs\PaginationData;
use Modules\Website\Entities\Guide;
use Modules\Website\Http\Requests\Guide\IndexGuideRequest;
use Modules\Website\Http\Requests\Guide\StoreGuideRequest;
use Modules\Website\Http\Requests\Guide\UpdateGuideRequest;
use Modules\Website\Services\Guides\GuideService;
use Modules\Website\Transformers\Guide\GuideResource;
use Modules\Website\Transformers\Guide\ShowGuideResource;

class GuideController extends Controller
{
    /**
     * @var GuideService
     */
    private $guideService;

    /**
     * @param GuideService $guideService
     */
    public function __construct(GuideService $guideService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:view-guides,' . $currentEntityId, ['only' => ['index', 'show']]);
        $this->middleware('permission:create-guides,' . $currentEntityId, ['only' => ['store']]);
        $this->middleware('permission:update-guides,' . $currentEntityId, ['only' => ['update']]);
        $this->middleware('permission:delete-guides,' . $currentEntityId, ['only' => ['destroy']]);

        $this->guideService = $guideService;
    }

    /**
     * Display a listing of the resource.
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(IndexGuideRequest $request)
    {
        $guideData = PaginationData::fromRequest($request, false);
        $guides = $this->guideService->getPagination($guideData);
        return GuideResource::collection($guides)->response();
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreGuideRequest $request)
    {
        $response = $this->guideService->create($request);
        return response()->json($response, $response['code']);
    }

    /**
     * Show the specified resource.
     * @param Guide $guide
     * @return ShowGuideResource
     */
    public function show(Guide $guide)
    {
        $guide = $this->guideService->show($guide->id);
        return new ShowGuideResource($guide);
    }

    /**
     * Update the specified resource in storage.
     * @param UpdateGuideRequest $request
     * @param Guide $guide
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateGuideRequest $request, Guide $guide)
    {
        $response = $this->guideService->update($request, $guide->id);
        return response()->json($response, $response['code']);
    }

    /**
     * Remove the specified resource from storage.
     * @param Guide $guide
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Guide $guide)
    {
        return response()->json($this->guideService->delete($guide->id), 200);
    }
}
