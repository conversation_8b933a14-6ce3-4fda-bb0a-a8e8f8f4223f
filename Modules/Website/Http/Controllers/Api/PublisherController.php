<?php

namespace Modules\Website\Http\Controllers\Api;

use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Website\Repositories\Publishers\PublisherRepositoryInterface;

class PublisherController extends Controller
{
    /**
     * @var PublisherRepositoryInterface
     */
    private $publisherRepository;

    /**
     * NewsCategoryController constructor.
     * @param PublisherRepositoryInterface $publisherRepository
     */
    public function __construct(PublisherRepositoryInterface $publisherRepository)
    {
        $this->publisherRepository = $publisherRepository;
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke()
    {
        return $this->publisherRepository->getAll();
    }
}
