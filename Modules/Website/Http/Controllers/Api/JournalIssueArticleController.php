<?php

namespace Modules\Website\Http\Controllers\Api;

use App\Helpers\EntitiesHelper;
use Illuminate\Routing\Controller;
use Modules\Website\Entities\JournalIssue;
use Modules\Website\Entities\JournalIssueArticle;
use Modules\Website\Http\Requests\Journal\StoreJournalIssueArticleRequest;
use Modules\Website\Http\Requests\Journal\UpdateJournalIssueArticleRequest;
use Modules\Website\Services\Journals\JournalIssueArticleService;
use Modules\Website\Transformers\Journal\JournalIssueArticleResource;
use Modules\Website\Transformers\Journal\ShowJournalIssueArticleResource;

class JournalIssueArticleController extends Controller
{
    /**
     * @var JournalIssueArticleService
     */
    private $journalIssueArticleService;

    public function __construct(JournalIssueArticleService $journalIssueArticleService)
    {
        $currentEntityId = EntitiesHelper::getEntityId();
        $this->middleware('permission:control-journal,' . $currentEntityId);

        $this->journalIssueArticleService = $journalIssueArticleService;
    }

    /**
     * Display a listing of the resource.
     * @param JournalIssue $journalIssue
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(JournalIssue $journalIssue)
    {
        $journalIssues = $this->journalIssueArticleService->getAll($journalIssue->id);
        return JournalIssueArticleResource::collection($journalIssues)->response();
    }

    /**
     * Store a newly created resource in storage.
     * @param JournalIssue $journalIssue
     * @param StoreJournalIssueArticleRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(JournalIssue $journalIssue, StoreJournalIssueArticleRequest $request)
    {
        return response()->json($this->journalIssueArticleService->create($request), 200);
    }

    /**
     * Show the specified resource.
     * @param JournalIssue $journalIssue
     * @param JournalIssueArticle $article
     * @return ShowJournalIssueArticleResource
     */
    public function show(JournalIssue $journalIssue, JournalIssueArticle $article)
    {
        abort_if($article->journal_issue_id != $journalIssue->id, 404);

        return new ShowJournalIssueArticleResource($this->journalIssueArticleService->show($journalIssue->id, $article->id));
    }


    /**
     * Update the specified resource in storage.
     * @param JournalIssue $journalIssue
     * @param UpdateJournalIssueArticleRequest $request
     * @param JournalIssueArticle $article
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(JournalIssue $journalIssue, UpdateJournalIssueArticleRequest $request, JournalIssueArticle $article)
    {
        abort_if($article->journal_issue_id != $journalIssue->id, 404);

        return response()->json($this->journalIssueArticleService->update($journalIssue->id, $request, $article->id));
    }

    /**
     * Remove the specified resource from storage.
     * @param JournalIssue $journalIssue
     * @param JournalIssueArticle $article
     * @return array
     */
    public function destroy(JournalIssue $journalIssue, JournalIssueArticle $article)
    {
        abort_if($article->journal_issue_id != $journalIssue->id, 404);

        return $this->journalIssueArticleService->delete($journalIssue->id, $article->id);
    }
}
