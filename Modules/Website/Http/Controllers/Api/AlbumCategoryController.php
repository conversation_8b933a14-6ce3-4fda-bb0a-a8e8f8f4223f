<?php

namespace Modules\Website\Http\Controllers\Api;

use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Website\Repositories\AlbumCategories\AlbumCategoryRepositoryInterface;

class AlbumCategoryController extends Controller
{
    /**
     * @var AlbumCategoryRepositoryInterface
     */
    private $albumCategoryRepository;

    /**
     * NewsCategoryController constructor.
     * @param AlbumCategoryRepositoryInterface $albumCategoryRepository
     */
    public function __construct(AlbumCategoryRepositoryInterface $albumCategoryRepository)
    {
        $this->albumCategoryRepository = $albumCategoryRepository;
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke()
    {
        return  $this->albumCategoryRepository->getAll();
    }
}
