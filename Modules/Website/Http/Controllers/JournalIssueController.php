<?php

namespace Modules\Website\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Website\Entities\JournalIssue;
use Modules\Website\Entities\JournalIssueArticle;
use Modules\Website\Http\Requests\Journal\IndexJournalIssuesRequest;
use Modules\Website\Services\Journals\JournalIssueArticleService;
use Modules\Website\Services\Journals\JournalIssueService;

class JournalIssueController extends Controller
{
    /**
     * @var JournalIssueService
     */
    private $journalIssueService;
    /**
     * @var JournalIssueArticleService
     */
    private JournalIssueArticleService $journalIssueArticleService;

    public function __construct(JournalIssueService $journalIssueService, JournalIssueArticleService $journalIssueArticleService)
    {
        $this->journalIssueService = $journalIssueService;
        $this->journalIssueArticleService = $journalIssueArticleService;
    }

    /**
     * Display a listing of the resource.
     * @param Request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        $issues = $this->journalIssueService->getPagination(12, $request->year);
        $years = $this->journalIssueService->getAllYears();
        return view('website::journals.issues', compact('issues', 'years'));
    }

    public function show(JournalIssue $journalIssue)
    {
        $issue = $this->journalIssueService->show($journalIssue->id);
        $articles = $this->journalIssueArticleService->getAll($issue->id);
        return view('website::journals.issue-articles', compact('issue', 'articles'));
    }

    public function downloadArticle(JournalIssueArticle $journalIssueArticle)
    {
        return $journalIssueArticle->file ?? redirect()->back();
    }

}
