<?php

namespace Modules\Website\Http\Controllers;

use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\URL;
use Illuminate\View\View;
use Modules\Website\DTOs\NewsData;
use Modules\Website\Entities\News;
use Modules\Website\Http\Requests\News\IndexNewsRequest;
use Modules\Website\Services\News\NewsService;
use Str;

class NewsController extends Controller
{
    /**
     * @var NewsService
     */
    private $newsService;

    /**
     * NewsController constructor.
     * @param NewsService $newsService
     */
    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    /**
     * Display a listing of the resource.
     * @param IndexNewsRequest $request
     * @return Application|Factory|View
     */
    public function index(IndexNewsRequest $request)
    {
        $newsData = NewsData::fromRequestForPaginationOnly(9, $request);
        $news = $this->newsService->getPagination($newsData);
        return view('website::news.index', compact('news'));
    }

    /**
     * Show the specified resource.
     * @param News $news
     * @return Application|Factory|View
     */
    public function show(News $news)
    {
        $news->load('comments', 'images');

        OpenGraph::setDescription(Str::limit(preg_replace('#<[^>]+>#', ' ', $news->description), 200));
        OpenGraph::setTitle($news->title);
        OpenGraph::setUrl(Url::current());
        OpenGraph::addProperty('type', 'article');
        OpenGraph::addProperty('locale', 'ar');
        OpenGraph::addProperty('locale:alternate', ['en-us']);

        TwitterCard::setTitle($news->title);
        TwitterCard::setImage($news->images->first()->getFullUrl());
        TwitterCard::setDescription(Str::limit(preg_replace('#<[^>]+>#', ' ', $news->description), 200));
        TwitterCard::setSite(Url::current());

        OpenGraph::addImage($news->images->first()->getFullUrl(), ['height' => 300, 'width' => 300]);

        if (!$news->hasLang(app()->getLocale())) {
            abort(404);
        }

        if (!$news->approved) {
            abort(404);
        }

        $news->incrementViews();

        $recentNews = $this->newsService->getLatest(3);

        return view('website::news.show', compact('news', 'recentNews'));
    }
}
