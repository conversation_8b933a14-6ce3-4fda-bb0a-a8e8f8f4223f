const dotenvExpand = require('dotenv-expand')
dotenvExpand(require('dotenv').config({ path: '../../.env' /*, debug: true*/ }))
const config = require('./webpack.config')

const mix = require('laravel-mix')
require('laravel-mix-merge-manifest')

// require('laravel-mix-purgecss')

mix.setPublicPath('../../public').mergeManifest()

mix
  .js(__dirname + '/Resources/assets/js/app.js', 'website/js')
  .sass(__dirname + '/Resources/assets/sass/style.scss', 'website/css')
  .options({
    postCss: [
      require('autoprefixer'),
      require('postcss-rtlcss')({
        safeBothPrefix: true,
      }),
    ],
  })
  .webpackConfig(config)

if (mix.inProduction()) {
  mix.version()
}
