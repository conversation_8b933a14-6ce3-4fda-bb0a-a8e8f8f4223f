User::whereHas('avatar')->limit(10)->delete();
User::whereHas('avatar')->limit(10)->get()
  ->each(fn($user) => $user->avatar()->delete());
User::whereHas('avatar', function($q) {
  return $q->where('file_name', 'default.jpg');
})->get()
  ->each(fn($user) => $user->clearMediaCollection('avatar'));

Program::whereHas('image', function($q) {
  return $q->where('file_name', 'default.jpg');
})->get()
  ->each(fn($program) => $program->clearMediaCollection('image'));
