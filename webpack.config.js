const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

module.exports = {
  resolve: {
    extensions: ['.js', '.json', '.vue'],
    alias: {
      '@': path.resolve(__dirname, './resources/vue'),
    },
  },
  // module: {
  //   rules: [
  //     {
  //       test: /(\.(png|jpe?g|gif|webp)$|^((?!font).)*\.svg$)/,
  //       loaders: {
  //         loader: 'file-loader',
  //         options: {
  //           // name: 'images/[name].[ext]',
  //           context: './frontend/assets/images',
  //           esModule: false,
  //         },
  //       },
  //     },
  //   ],
  // },
  output: {
    chunkFilename: 'dashboard/js/[chunkhash].js',
    path: path.resolve(__dirname, './public'),
  },
  plugins: [
    new CompressionPlugin({
      // filename: '[path].gz[query]',
      algorithm: 'gzip',
      test: new RegExp('\\.(js|css)$'),
    }),
    new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: ['**/dashboard/*', '!static-files*'],
    }),
    // new BundleAnalyzerPlugin(),
  ],
}
