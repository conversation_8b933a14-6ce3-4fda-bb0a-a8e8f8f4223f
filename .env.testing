APP_NAME=University_of_Gharyan
APP_ENV=testing
APP_KEY=base64:LKRr7+qefjWB8rPCEWBFQqWBExeS9gH5iYDSZ0vshwc=
APP_DEBUG=true
APP_URL=http://uog.me
VERIFY_URL=/verify-email?queryURL=
RESET_PASSWORD_URL=/reset-password?queryURL=

MODEL_CACHE_ENABLED=false

LOG_CHANNEL=stack

SESSION_DOMAIN=.uog.me

MAIN_DB_CONNECTION=main_db
MAIN_DB_HOST=localhost
MAIN_DB_PORT=3306
MAIN_DB_DATABASE=libyan_academic_main
MAIN_DB_USERNAME=root
MAIN_DB_PASSWORD=

WEBSITE_DB_CONNECTION=website_db
WEBSITE_DB_HOST=localhost
WEBSITE_DB_PORT=3306
WEBSITE_DB_DATABASE=libyan_academic_website
WEBSITE_DB_USERNAME=root
WEBSITE_DB_PASSWORD=

SMS_DB_CONNECTION=sms_db
SMS_DB_HOST=localhost
SMS_DB_PORT=3306
SMS_DB_DATABASE=libyan_academic_sms
SMS_DB_USERNAME=root
SMS_DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SANCTUM_STATEFUL_DOMAINS=uog.me,ase.uog.me,hum.uog.me,sw.hum.uog.me,is.hum.uog.me,se.hum.uog.me,ss.hum.uog.me,law.hum.uog.me,pis.hum.uog.me,soc.hum.uog.me,geo.hum.uog.me,hist.hum.uog.me,be.ase.uog.me,cpe.ase.uog.me,epm.ase.uog.me,mee.ase.uog.me,eec.ase.uog.me,cea.ase.uog.me,psy.hum.uog.me,ps.hum.uog.me,cm.afs.uog.me
SANCTUM_DOMAIN=.uog.me

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis
MODEL_CACHE_STORE=redis

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=465
MAIL_USERNAME=65683507424d86
MAIL_PASSWORD=d3047998f28326
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MIX_API_URL=http://uog.me/api/
