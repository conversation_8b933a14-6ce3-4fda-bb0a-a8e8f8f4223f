<?php

namespace Tests;

use App\Models\Entity;
use App\Helpers\Utils;
use App\Repositories\Users\UserRepositoryInterface;
use App\Services\PrivilegesService;
use App\Models\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Laravel\Sanctum\Sanctum;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    public function actAsUser($privilege, $entity){
        $userRepository = app(UserRepositoryInterface::class);
        $user = factory(User::class)->create();
        $userRepository->attachPrivileges($user, PrivilegesService::namesToPrivileges([$privilege]), $entity);
        Sanctum::actingAs($user, ['*']);
    }

    public function actAsUserWithNoPrivileges(){
        $user = factory(User::class)->create();
        Sanctum::actingAs($user, ['*']);
    }

    public function getRandomDepartmentURL()
    {
        $url = env('APP_URL');
        $url = Utils::removeHttp($url);
        $firstEntity = Entity::departments()->inRandomOrder()->first();
        return "$firstEntity->id.$url";
    }

    public function getRandomSchoolURL()
    {
        $url = env('APP_URL');
        $url = Utils::removeHttp($url);
        $firstEntity = Entity::schools()->inRandomOrder()->first();
        return "$firstEntity->id.$url";
    }

}
