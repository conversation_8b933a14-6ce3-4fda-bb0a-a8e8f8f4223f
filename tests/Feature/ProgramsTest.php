<?php

namespace Tests\Feature;

use App\Models\Hall;
use App\Models\Program;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class ProgramsTest extends TestCase
{
    use DatabaseTransactions;

    public function test_anyone_can_fetch_all_programs()
    {
        $response = $this->get('/api/programs', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => ['id', 'entity_id', 'program_type']
        ]);
        $response->assertOk();
    }

    public function test_create_program_validation_error(){
        $this->actAsUser('create-programs', 'eng');

        $program = factory(Program::class)->make(['entity_id' => 'eng']);

        $attributes = $program->getAttributes();

        $response = $this->json('post','/api/programs', $attributes,['origin' => 'eng.lau.ly']);

        $response->assertJsonValidationErrors(['entity_id']);
    }

    public function test_can_fetch_all_programs()
    {
        $response = $this->get('/api/programs', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => ['id', 'entity_id', 'program_type']
        ]);
        $response->assertOk();
    }

    public function test_can_create_program_in_school()
    {
        $this->actAsUser('create-programs', 'eng');

        $program = factory(Program::class)->make(['entity_id' => 'a.eng']);

        $attributes = $program->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['major'] = \GuzzleHttp\json_decode($attributes['major'], true);
        $attributes['objectives'] = \GuzzleHttp\json_decode($attributes['objectives'], true);
        $attributes['outcomes'] = \GuzzleHttp\json_decode($attributes['outcomes'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);
        $attributes['entry_requirements'] = \GuzzleHttp\json_decode($attributes['entry_requirements'], true);
        $attributes['certificate_awarded'] = \GuzzleHttp\json_decode($attributes['certificate_awarded'], true);
        $attributes['job_market'] = \GuzzleHttp\json_decode($attributes['job_market'], true);

        $response = $this->json('post','/api/programs', $attributes,['origin' => 'eng.lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure(['message', 'id']);
    }

    public function test_can_not_create_program_in_school_without_privileges()
    {
        $this->actAsUser('create-programs', 'lau');

        $program = factory(Program::class)->make(['entity_id' => 'eng']);

        $attributes = $program->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['major'] = \GuzzleHttp\json_decode($attributes['major'], true);
        $attributes['objectives'] = \GuzzleHttp\json_decode($attributes['objectives'], true);
        $attributes['outcomes'] = \GuzzleHttp\json_decode($attributes['outcomes'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);
        $attributes['entry_requirements'] = \GuzzleHttp\json_decode($attributes['entry_requirements'], true);
        $attributes['certificate_awarded'] = \GuzzleHttp\json_decode($attributes['certificate_awarded'], true);
        $attributes['job_market'] = \GuzzleHttp\json_decode($attributes['job_market'], true);

        $response = $this->json('post','/api/programs', $attributes,['origin' => 'eng.lau.ly']);

        $response->assertForbidden();
    }

    public function test_can_not_create_program_in_university()
    {
        $this->actAsUser('create-programs', 'lau');

        $program = factory(Program::class)->make(['entity_id' => 'eng']);

        $attributes = $program->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['major'] = \GuzzleHttp\json_decode($attributes['major'], true);
        $attributes['objectives'] = \GuzzleHttp\json_decode($attributes['objectives'], true);
        $attributes['outcomes'] = \GuzzleHttp\json_decode($attributes['outcomes'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);
        $attributes['entry_requirements'] = \GuzzleHttp\json_decode($attributes['entry_requirements'], true);
        $attributes['certificate_awarded'] = \GuzzleHttp\json_decode($attributes['certificate_awarded'], true);
        $attributes['job_market'] = \GuzzleHttp\json_decode($attributes['job_market'], true);

        $response = $this->json('post','/api/programs', $attributes,['origin' => 'lau.ly']);

        $response->assertNotFound();
    }

    public function test_can_not_view_program_in_university(){
        $this->actAsUser('view-programs', 'eng');

        $program = factory(Program::class)->create(['entity_id' => 'eng']);

        $response = $this->get('/api/programs/'.$program->id, ['origin' => 'lau.ly']);

        $response->assertNotFound();
    }

    public function test_can_view_program_in_school(){
        $this->actAsUser('view-programs', 'eng');

        $program = factory(Program::class)->create(['entity_id' => 'a.eng']);

        $response = $this->get('/api/programs/'.$program->id, ['origin' => 'eng.lau.ly']);

        $response->assertOk();

    }

    public function test_can_update_programs(){

        $this->actAsUser('update-programs', 'eng');

        $program = factory(Program::class)->create(['entity_id' => 'a.eng']);

        $attributes = $program->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['major'] = \GuzzleHttp\json_decode($attributes['major'], true);
        $attributes['objectives'] = \GuzzleHttp\json_decode($attributes['objectives'], true);
        $attributes['outcomes'] = \GuzzleHttp\json_decode($attributes['outcomes'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);
        $attributes['entry_requirements'] = \GuzzleHttp\json_decode($attributes['entry_requirements'], true);
        $attributes['certificate_awarded'] = \GuzzleHttp\json_decode($attributes['certificate_awarded'], true);
        $attributes['job_market'] = \GuzzleHttp\json_decode($attributes['job_market'], true);

        $response = $this->json('put', '/api/programs/'.$program->id, $attributes,['origin' => 'eng.lau.ly']);

        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
    }

    public function test_can_not_update_programs(){

        $this->actAsUserWithNoPrivileges();

        $program = factory(Program::class)->create(['entity_id' => 'a.eng']);

        $attributes = $program->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['major'] = \GuzzleHttp\json_decode($attributes['major'], true);
        $attributes['objectives'] = \GuzzleHttp\json_decode($attributes['objectives'], true);
        $attributes['outcomes'] = \GuzzleHttp\json_decode($attributes['outcomes'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);
        $attributes['entry_requirements'] = \GuzzleHttp\json_decode($attributes['entry_requirements'], true);
        $attributes['certificate_awarded'] = \GuzzleHttp\json_decode($attributes['certificate_awarded'], true);
        $attributes['job_market'] = \GuzzleHttp\json_decode($attributes['job_market'], true);

        $response = $this->json('put', '/api/programs/'.$program->id, $attributes,['origin' => 'eng.lau.ly']);

        $response->assertForbidden();
    }

    public function test_can_delete_programs(){
        $this->actAsUser('delete-programs', 'eng');

        $program = factory(Program::class)->create(['entity_id' => 'a.eng']);

        $response = $this->json('delete', '/api/programs/'.$program->id, [], ['origin' => 'eng.lau.ly']);


        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
        $this->assertDeleted($program);
    }

}
