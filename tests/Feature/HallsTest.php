<?php

namespace Tests\Feature;

use App\Models\Hall;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class HallsTest extends TestCase
{
    use DatabaseTransactions;

    public function test_can_fetch_all_halls()
    {
        $this->actAsUser('view-halls', 'lau');

        $response = $this->get('/api/halls', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => ['id', 'title']
        ]);
        $response->assertOk();
    }

    public function test_can_not_fetch_halls()
    {
        $response = $this->get('/api/halls', ['origin' => 'lau.ly']);

        $response->assertForbidden();
    }


    public function test_can_create_hall()
    {
        $this->actAsUser('create-halls', 'lau');

        $hall = factory(Hall::class)->make();

        $attributes = $hall->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);

        $response = $this->json('post','/api/halls', $attributes,['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure(['message', 'id']);
    }

    public function test_can_not_view_halls_in_schools()
    {
        $this->actAsUser('view-halls', 'eng');

        $hall = factory(Hall::class)->create();

        $response = $this->get('/api/halls/'.$hall->id, ['origin' => 'eng.lau.ly']);
        $response->assertNotFound();
    }

    public function test_can_view_halls_in_university()
    {
        $this->actAsUser('view-halls', 'lau');

        $hall = factory(Hall::class)->create();

        $uniResponse = $this->get('/api/halls/'.$hall->id, ['origin' => 'lau.ly']);
        $uniResponse->assertSuccessful();
        $uniResponse->assertStatus(200);
    }

    public function test_can_update_halls(){

        $this->actAsUser('update-halls', 'lau');

        $hall = factory(Hall::class)->create();

        $attributes = $hall->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);

        $response = $this->json('put', '/api/halls/'.$hall->id, $attributes,['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
    }

    public function test_can_not_update_halls(){

        $this->actAsUserWithNoPrivileges();

        $hall = factory(Hall::class)->create();

        $attributes = $hall->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);

        $errorResponse = $this->json('put', '/api/halls/'.$hall->id, $attributes,['origin' => 'lau.ly']);
        $errorResponse->assertForbidden();
    }

    public function test_can_delete_halls(){
        $this->actAsUser('delete-halls', 'lau');

        $hall = factory(Hall::class)->create();

        $response = $this->json('delete', '/api/halls/'.$hall->id, [], ['origin' => 'lau.ly']);


        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
        $this->assertDeleted($hall);

    }
}
