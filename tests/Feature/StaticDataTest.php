<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class StaticDataTest extends TestCase
{
    use DatabaseTransactions;
    
    public function test_academic_ranks(){
        $response = $this->get('/api/academic-ranks', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title'
            ]
        ]);
        $response->assertOk();

    }

    public function test_qualifications(){
        //api/qualifications
        $response = $this->get('/api/qualifications', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title'
            ]
        ]);
        $response->assertOk();
    }

    public function test_nationalities(){
        //api/nationalities
        $response = $this->get('/api/nationalities', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title',
                'country'
            ]
        ]);
        $response->assertOk();

    }

    public function test_subject_types(){
        //api/subject-types
        $response = $this->get('/api/subject-types', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title'
            ]
        ]);
        $response->assertOk();
    }

    public function test_program_types(){
        //api/program-types
        $response = $this->get('/api/program-types', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title'
            ]
        ]);
        $response->assertOk();
    }

    public function test_program_durations(){
        //api/program-duration-units
        $response = $this->get('/api/program-duration-units', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => [
                'id',
                'title'
            ]
        ]);
        $response->assertOk();
    }

}
