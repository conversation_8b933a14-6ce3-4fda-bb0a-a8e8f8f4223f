<?php

namespace Tests\Feature;

use App\Models\Entity;
use App\Models\EntityDetails;
use App\Helpers\EntitiesHelper;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class EntityTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_request_entity_id()
    {
        request()->headers->set('Origin', 'eng.lau.ly');
        $entityId = EntitiesHelper::getEntityId();
        $this->assertEquals('eng', $entityId);
    }

    public function test_creating_entity_under_university()
    {
        $this->actAsUser('create-entities', 'lau');

        $entity = factory(Entity::class)->make([
            'type' => 2
        ]);
        $entityDetails = factory(EntityDetails::class)->make();
        $entityDetailsAttributes = collect($entityDetails->getAttributes())->map(function($attribute){
            return \GuzzleHttp\json_decode($attribute, true);
        })->toArray();

        //test without details data
        $entityAttributes = $entity->attributesToArray();
        $response = $this->json('post', '/api/entities', $entityAttributes, ['Origin' => 'lau.ly']);
        $response->assertJsonValidationErrors(['vision', 'mission', 'values', 'description', 'goals']);


        $entityAttributes = array_merge($entityAttributes, $entityDetailsAttributes);
        $response = $this->json('post', '/api/entities', $entityAttributes, ['Origin' => 'lau.ly']);
        $response->assertOk();
    }

    public function test_can_not_create_department_under_university()
    {
        $this->actAsUser('create-entities', 'lau');

        $entity = factory(Entity::class)->make([
            'type' => 3
        ]);
        $entityDetails = factory(EntityDetails::class)->make();
        $entityDetailsAttributes = collect($entityDetails->getAttributes())->map(function($attribute){
            return \GuzzleHttp\json_decode($attribute, true);
        })->toArray();

        //test without details data
        $entityAttributes = $entity->attributesToArray();
        $entityAttributes = array_merge($entityAttributes, $entityDetailsAttributes);

        $response = $this->json('post', '/api/entities', $entityAttributes, ['Origin' => 'lau.ly']);
        $response->assertJsonValidationErrors(['type']);
    }

    public function test_creating_entities_in_school()
    {
        $this->actAsUser('create-entities', 'lau');

        $entity = factory(Entity::class)->make([
            'type' => 3
        ]);
        $entityDetails = factory(EntityDetails::class)->make();
        $entityDetailsAttributes = collect($entityDetails->getAttributes())->map(function($attribute){
            return \GuzzleHttp\json_decode($attribute, true);
        })->toArray();

        $entityAttributes = $entity->attributesToArray();
        $entityAttributes = array_merge($entityAttributes, $entityDetailsAttributes);

        //no privileges in eng
        $response = $this->json('post', '/api/entities', $entityAttributes, ['origin' => 'eng.lau.ly']);
        $response->assertStatus(403);

        //attached privileges in eng
        $this->actAsUser('create-entities', 'eng');
        $response = $this->json('post', '/api/entities', $entityAttributes, ['origin' => 'eng.lau.ly']);
        $response->assertOk();
    }

    public function test_validation_error_in_entity(){
        $this->actAsUser('create-entities', 'eng');

        //cannot create school under school
        $newEntity = factory(Entity::class)->make([
            'type' => 2
        ]);
        $entityDetails = factory(EntityDetails::class)->make();
        $entityDetailsAttributes = collect($entityDetails->getAttributes())->map(function($attribute){
            return \GuzzleHttp\json_decode($attribute, true);
        })->toArray();

        $entityAttributes = $newEntity->attributesToArray();
        $entityAttributes = array_merge($entityAttributes, $entityDetailsAttributes);
        $response = $this->json('post', '/api/entities', $entityAttributes, ['origin' => 'eng.lau.ly']);
        $response->assertJsonValidationErrors(['type']);
    }

    public function test_creating_under_department()
    {
        $this->actAsUser('create-entities', 'a.lan');

        //cannot create school under school
        $newEntity = factory(Entity::class)->make([
            'type' => 3
        ]);
        $entityDetails = factory(EntityDetails::class)->make();
        $entityDetailsAttributes = collect($entityDetails->getAttributes())->map(function($attribute){
            return \GuzzleHttp\json_decode($attribute, true);
        })->toArray();

        $entityAttributes = $newEntity->attributesToArray();
        $entityAttributes = array_merge($entityAttributes, $entityDetailsAttributes);
        $response = $this->json('post', '/api/entities', $entityAttributes, ['origin' => 'a.lan.lau.ly']);
        $response->assertJsonValidationErrors(['type']);
    }

}
