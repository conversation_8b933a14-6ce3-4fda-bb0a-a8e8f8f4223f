<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Repositories\Users\UserRepositoryInterface;
use App\Services\PrivilegesService;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BuildingsTest extends TestCase
{
    use DatabaseTransactions;

    public function test_can_fetch_all_buildings()
    {
        $this->actAsUser('view-buildings', 'lau');

        $response = $this->get('/api/buildings', ['origin' => env('APP_URL')]);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => ['id', 'title', 'longitude', 'latitude']
        ]);
        $response->assertOk();
    }


    public function test_can_create_building()
    {
        $this->actAsUser('create-buildings', 'lau');

        $building = factory(Building::class)->make();

        $attributes = $building->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);

        $response = $this->json('post','/api/buildings', $attributes, ['origin' => env('APP_URL')]);

        $response->assertSuccessful();
        $response->assertJsonStructure(['message', 'id']);
    }

    public function test_can_view_buildings_in_university()
    {
        $this->actAsUser('view-buildings', 'lau');

        $building = factory(Building::class)->create();

        $uniResponse = $this->get('/api/buildings/'.$building->id, ['origin' => env('APP_URL')]);
        $uniResponse->assertSuccessful();
        $uniResponse->assertStatus(200);
    }

    public function test_can_update_buildings(){

        $this->actAsUser('update-buildings', 'lau');

        $building = factory(Building::class)->create();

        $attributes = $building->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);

        $response = $this->json('put', '/api/buildings/'.$building->id, $attributes, ['origin' => env('APP_URL')]);

        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
    }

    public function test_can_not_update_buildings(){

        $this->actAsUserWithNoPrivileges();

        $building = factory(Building::class)->create();

        $attributes = $building->getAttributes();
        $attributes['title'] = \GuzzleHttp\json_decode($attributes['title'], true);
        $attributes['description'] = \GuzzleHttp\json_decode($attributes['description'], true);

        $errorResponse = $this->json('put', '/api/buildings/'.$building->id, $attributes, ['origin' => env('APP_URL')]);
        $errorResponse->assertForbidden();
    }

    public function test_can_delete_buildings(){
        $this->actAsUser('delete-buildings', 'lau');

        $building = factory(Building::class)->create();

        $response = $this->json('delete', '/api/buildings/'.$building->id, [], ['origin' => env('APP_URL')]);


        $response->assertSuccessful();
        $response->assertJson(['message' => 'success']);
        $this->assertDeleted($building);

    }

}
