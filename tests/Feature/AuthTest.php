<?php

namespace Tests\Feature;

use App\Mail\SendResetPasswordEmail;
use App\Mail\SendVerificationEmail;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Mail;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use DatabaseTransactions;

    public function test_login_successfully()
    {
        $this->withExceptionHandling();
        $user = factory(User::class)->create(['status' => 'active']);
        $response = $this->json('post','/api/login', ['email' => $user->email, 'password' => 'password', 'recaptcha_token' => 'recaptcha-token'], ['origin' => env('APP_URL')]);
        $response->assertOk();
        $this->assertAuthenticatedAs($user);
    }

    public function test_login_failed()
    {
        $user = factory(User::class)->create(['status' => 'active']);
        $response = $this->json('post','/api/login', ['email' => $user->email, 'password' => 'incorrect password', 'recaptcha_token' => 'recaptcha-token'], ['origin' => env('APP_URL')]);
        $response->assertUnauthorized();
        $response->assertJson(['code' => 401]);
    }

    public function test_login_inactive_account()
    {
        $user = factory(User::class)->create(['status' => 'inactive']);
        $response = $this->json('post','/api/login', ['email' => $user->email, 'password' => 'password', 'recaptcha_token' => 'recaptcha-token'], ['origin' => env('APP_URL')]);
        $response->assertOk();
        $response->assertJson(['message' => 'inactive']);
        $this->assertGuest();
    }

    public function test_login_not_verified_account()
    {
        $user = factory(User::class)->create(['email_verified_at' => null]);
        $response = $this->json('post','/api/login', ['email' => $user->email, 'password' => 'password', 'recaptcha_token' => 'recaptcha-token'], ['origin' => env('APP_URL')]);
        $response->assertOk();
        $response->assertJson(['message' => 'email_not_verified']);
        $this->assertGuest();
    }

    public function test_register_lecturer_successfully()
    {
        Mail::fake();

        $user = factory(User::class)->make(['password' => 'password', 'password_confirmation' => 'password']);
        $userData = array_merge($user->getAttributes(), [
            'qualification_id' => 1,
            'academic_rank_id' => 1
        ]);
        $userData['gender'] = 'male';
        $response = $this->json('post', '/api/register/lecturer', $userData, ['origin' => env('APP_URL')]);
        $response->assertJson(['message' => 'success']);
        $this->assertGuest();

        Mail::assertSent(SendVerificationEmail::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    public function test_register_lecturer_fail()
    {
        $user = factory(User::class)->make(['password' => 'password', 'password_confirmation' => 'password', 'gender' => 'male']);
        $userData = array_merge($user->getAttributes(),[]);
        $response = $this->json('post', '/api/register/lecturer', $userData, ['origin' => env('APP_URL')]);
        $response->assertJsonValidationErrors(['gender', 'qualification_id', 'academic_rank_id']);
        $this->assertGuest();

    }

    public function test_reset_password() {

        Mail::fake();

        $user = factory(User::class)->create();

        $response = $this->json('post', '/api/request-reset-password', ['email' => $user->email], ['origin' => env('APP_URL')]);
        $response->assertOk();
        $token = null;
        Mail::assertSent(SendResetPasswordEmail::class, function ($mail) use ($user, &$token) {
            $token = $mail->user->reset_password_token;
            return $mail->hasTo($user->email);
        });

        $response = $this->json('post', '/api/reset-password', [
            'email' => $user->email, 'token' => $token, 'password' => '123456789', 'password_confirmation' => '12345678'
        ], ['origin' => env('APP_URL')]);

        $response->assertJsonValidationErrors(['password']);

        $response = $this->json('post', '/api/reset-password', [
            'email' => $user->email, 'token' => $token, 'password' => '123456789', 'password_confirmation' => '123456789'
        ], ['origin' => env('APP_URL')]);

        $response->assertOk();
        $response->assertJson(['message' => 'success']);
    }

}
