<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class CampusesTest extends TestCase
{
    use DatabaseTransactions;

    public function test_can_fetch_all_buildings()
    {
        $this->actAsUser('view-campuses', 'lau');

        $response = $this->get('/api/campuses', ['origin' => 'lau.ly']);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            '*' => ['id', 'title', 'description']
        ]);
        $response->assertOk();
    }

    public function test_can_not_fetch_all_buildings_in_schools()
    {
        $this->actAsUser('view-campuses', 'eng');

        $response = $this->get('/api/campuses', ['origin' => 'eng.lau.ly']);
        $response->assertNotFound();

    }

    public function test_can_not_fetch_all_buildings_without_privileges()
    {
        $this->actAsUserWithNoPrivileges();

        $response = $this->get('/api/campuses', ['origin' => 'lau.ly']);
        $response->assertForbidden();
    }
}
