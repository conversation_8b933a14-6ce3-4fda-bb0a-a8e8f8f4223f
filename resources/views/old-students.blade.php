<!DOCTYPE html>
<html lang="en" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="/assets/logo.png">
    <title>{{ config('app.name') }}</title>
</head>
<body>

    <div>
        <form action="{{ route('old-student.store') }}" method="post">
            @csrf
            <label>رقم القيد</label>
            <input type="text" name="id"/>
            <label>الإسم</label>
            <input type="text" name="first_name_ar"/>
            <input type="submit" value="إضافة"/>
        </form>
    </div>

    <div>
        @foreach($oldStudents as $student)
            <p>{{ $student->id }} - {{ $student->first_name_ar }}</p>
        @endforeach
    </div>

</body>
</html>
