<template>
  <Card title="إعدادات تسجيل الكليات" body-fit :loading="loading">
    <template #toolbar>
      <v-button class="mr-2" variant="brand" wide @click="submit">حفظ</v-button>
    </template>
    <template #footer>
      <div class="d-flex justify-content-end">
        <v-button class="mr-2" variant="brand" wide @click="submit">حفظ</v-button>
      </div>
    </template>
    <VTable :data="tableData" :headers="headers" indexed>
      <template slot="td-min_gpa" slot-scope="{ row }">
        <b-form-input
          v-model="row.min_gpa"
          name="min_gpa"
          class="mark-input"
          min="0"
          max="100"
          type="number"
          :disabled="!row.is_enable"
        />
      </template>
      <template slot="td-is_enable" slot-scope="{ row }">
        <SwitchInput
          v-model="row.is_enable"
          name="is_enable"
          :checked-value="1"
          :unchecked-value="0"
          class="mb-0"
          labelClass="mb-0"
        />
      </template>
    </VTable>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable.vue'
import SwitchInput from '@/components/form-controls/SwitchInput.vue'
import Card from '@/views/partials/content/Card.vue'

export default {
  components: { Card, VTable, SwitchInput },
  data() {
    return {
      loading: true,

      headers: [
        { key: 'title', name: 'الكلية', sort: true },
        { key: 'is_enable', name: 'هل يستطيع التسجيل بها', sort: true, expend: true },
        { key: 'min_gpa', name: 'النسبة المطلوبة', sort: true, expend: true },
      ],

      academicYearFaculties: [],
    }
  },
  computed: {
    academicYearId() {
      return this.$route.params.academic_year_id
    },
    schools() {
      return this.$store.getters.schools
    },
    tableData() {
      return this.schools.map(item => {
        const pivot = this.academicYearFaculties.find(entity => entity.entity_id === item.id)
        return {
          id: item.id,
          title: item.title,
          min_gpa: pivot ? pivot.min_gpa : 50,
          is_enable: !!pivot,
        }
      })
    },
  },
  methods: {
    submit() {
      // if (!this.validateMarkInputs()) return

      this.loading = true

      const entities = this.tableData
        .filter(item => item.is_enable)
        .map(entity => ({
          entity_id: entity.id,
          min_gpa: entity.min_gpa,
        }))

      ApiService.put(`academic-years/${this.academicYearId}/entities`, { entities })
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(this.$utils.errorHandlerWithValidation)
        .finally(() => (this.loading = false))
    },
  },
  created() {
    ApiService.get(`academic-years/${this.academicYearId}/entities`)
      .then(({ data }) => (this.academicYearFaculties = data))
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
