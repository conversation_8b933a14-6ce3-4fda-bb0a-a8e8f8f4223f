<template>
  <Card :loading="pageLoading" :title="`تحميل نتائج ${term.title}`" titleIcon="flaticon-folder-1">
    <template slot="toolbar">
      <v-button href="/sms/import-grades" type="button" variant="label-success">
        تنزيل القالب
      </v-button>
    </template>
    <FormValidation ref="form" :loading="loading" @submit="onSubmit">
      <div class="row">
        <!-- <div class="col-lg-6">
          <SelectInput
            v-model="form.term_id"
            :options="terms"
            label="الفصل الدراسي"
            searchable
            clearable
            rules="required"
          />
        </div> -->
        <!-- <div class="col-lg-6">
          <select-input
            v-model="department_id"
            :options="departments"
            clearable
            label="القسم"
            searchable
            rules="required"
          />
        </div> -->
        <div class="col-lg-6">
          <SelectInput
            v-model="program_id"
            :options="programs"
            clearable
            label="البرنامج الدراسي"
            searchable
            rules="required"
          />
        </div>
        <div class="col-lg-6">
          <SelectInput
            :disabled="!program_id"
            rules="required"
            label="المادة الدراسية"
            name="subject_id"
            :options="subjects"
            v-model="form.subject_id"
            searchable
            clearable
            description="يرجى اختيار البرنامج الدراسي لعرض موادها الدراسية"
          />
        </div>
        <div class="col-lg-6">
          <SelectInput
            rules="required"
            label="المجموعة"
            name="group"
            searchable
            :options="groups"
            :reduce="obj => obj"
            v-model="form.group"
          />
        </div>
        <div v-if="term && term.study_plan === 'year'" class="col-lg-6">
          <RadioInputGroup
            label="نجح من الدور"
            class="col-12"
            name="is_second_attempt"
            rules="required"
            :options="[
              { text: 'الأول', value: '0' },
              { text: 'الثاني', value: '1' },
            ]"
            v-model="form.is_second_attempt"
          />
        </div>
      </div>
      <hr />
      <div class="d-flex justify-content-between flex-wrap mb-4">
        <h4 class="text-primary">قائمة الطلبة</h4>

        <v-button type="button" variant="label-primary" @click="$refs.file.click()">
          استيراد النتائج
        </v-button>
        <input ref="file" type="file" class="d-none" @change="readXlsxFile" accept=".xlsx,.xls" />
      </div>

      <!-- <div class="row">
        <div class="col-12 d-flex justify-content-end">

        </div>
      </div> -->

      <b-row v-for="(item, index) in form.student_grades" :key="index">
        <text-input
          class="col-md-3 col-6"
          rules="required"
          :vid="`student_grades.${index}.student_id`"
          :name="`student_grades.${index}.student_id`"
          label="رقم قيد الطالب"
          v-model="item.student_id"
        />
        <text-input
          class="col-md-3 col-6"
          rules="required"
          :vid="`student_grades.${index}.mid_mark`"
          :name="`student_grades.${index}.mid_mark`"
          label="درجة الاعمال"
          min="0"
          type="number"
          v-model="item.mid_mark"
        />
        <text-input
          class="col-md-3 col-6"
          rules="required"
          :vid="`student_grades.${index}.final_mark`"
          :name="`student_grades.${index}.final_mark`"
          label="درجة النهائي"
          min="0"
          type="number"
          v-model="item.final_mark"
        />
        <b-col cols="6" md="3" style="margin-top: 2.65rem">
          <v-button
            v-if="form.student_grades.length > 1"
            @click="removeItem(index)"
            class="ml-2"
            icon="la la-remove"
            variant="label-danger"
            icon-only
          />
        </b-col>
      </b-row>
      <v-button @click="addItem" class="mb-4" icon="flaticon2-plus" variant="label-brand">
        إضافة
      </v-button>

      <div class="row">
        <div v-if="result.added.length > 0" class="col-12">
          <b-alert variant="solid-success" show dismissible>
            <div class="alert-text">
              <h4 class="alert-heading mb-4">
                أرقام قيد الطلبة الذين تمت إضافة نتائج لديهم
              </h4>

              <ol>
                <li v-for="id in result.added" :key="id">
                  {{ id }}
                </li>
              </ol>
            </div>
          </b-alert>
        </div>

        <div v-if="result.already_exists.length > 0" class="col-12">
          <b-alert variant="solid-warning" show dismissible>
            <div class="alert-text">
              <h4 class="alert-heading mb-4">
                أرقام قيد الطلبة الذين لديهم نتائج مسبقًا
              </h4>

              <ol>
                <li v-for="id in result.already_exists" :key="id">
                  {{ id }}
                </li>
              </ol>
            </div>
          </b-alert>
        </div>

        <div v-if="result.not_found.length > 0" class="col-12">
          <b-alert variant="solid-warning" show dismissible>
            <div class="alert-text">
              <h4 class="alert-heading mb-4">
                ارقام القيد التي لم يتم العثور عليها
              </h4>

              <ol>
                <li v-for="id in result.not_found" :key="id">
                  {{ id }}
                </li>
              </ol>
            </div>
          </b-alert>
        </div>

        <div class="mt-4 col-12 d-flex justify-content-end">
          <b-button class="px-4 mr-2" type="button" @click="initValues()">تفريغ</b-button>
          <v-button :loading.sync="loading" type="submit" variant="brand" wide>
            تحميل
          </v-button>
        </div>
      </div>
    </FormValidation>
  </Card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import SelectInput from '@/components/form-controls/SelectInput.vue'
import FormValidation from '@/components/form-controls/FormValidation.vue'
import FileUploader from '@/components/FileUploader.vue'
import FileInput from '@/components/form-controls/FileInput.vue'
import ApiService from '@/common/api.service'
import readXlsxFile from 'read-excel-file'
import TextInput from '@/components/form-controls/TextInput.vue'
import RadioInputGroup from '@/components/form-controls/RadioInputGroup.vue'

export default {
  name: 'Reports',
  components: { RadioInputGroup, Card, SelectInput, FormValidation, FileUploader, FileInput, TextInput },
  data() {
    return {
      pageLoading: true,
      loading: false,

      subjects: [],

      department_id: null,
      program_id: null,

      term: {},

      form: {
        group: null,
        subject_id: null,
        is_second_attempt: '0',
        student_grades: [
          {
            student_id: null,
            mid_mark: null,
            final_mark: null,
          },
        ],
      },

      result: {
        added: [],
        already_exists: [],
        not_found: [],
      },
    }
  },
  watch: {
    program_id(val) {
      if (!val) return
      this.fetchSubjectsByProgram(val)
    },
  },
  computed: {
    termId() {
      return this.$route.params.term_id
    },
    groups() {
      return Array.from(Array(26)).map((_, i) => String.fromCharCode(i + 65))
    },
    departments() {
      return this.$store.getters.departments
    },
    programs() {
      return this.$store.getters.programsOptions.filter(
        item => item.duration_unit?.id === (this.term?.study_plan === 'semester' ? 1 : 2)
      )
    },
  },
  methods: {
    addItem() {
      this.form.student_grades.push({
        student_id: null,
        mid_mark: null,
        final_mark: null,
      })
    },
    removeItem(index) {
      this.form.student_grades.splice(index, 1)
    },
    async readXlsxFile(e) {
      const file = e.target?.files?.[0]
      if (!file) return

      const ext = file.name.split('.').pop()
      if (!['xlsx', 'xls'].includes(ext)) return this.$utils.alert('الملف غير مدعوم', 'error')

      const rows = await readXlsxFile(file)
      // pop the header row
      rows.shift()

      this.form.student_grades = rows.map(row => ({
        student_id: row[0],
        mid_mark: row[1],
        final_mark: row[2],
      }))

      this.$refs.file.type = 'text'
      this.$refs.file.type = 'file'
    },
    fetchSubjectsByProgram(program_id) {
      const program = this.programs.find(item => item.id === program_id)
      if (program?.subjects) {
        this.subjects = program.subjects
      } else {
        ApiService.query('sms/subjects', { program_id }).then(({ data }) => {
          this.subjects = data.map(item => ({
            id: item.id,
            title: `${item.code} - ${this.$utils.getCurrentOrFirstLang(item.title)}`,
          }))
          program.subjects = this.subjects
        })
      }
    },
    initValues() {
      this.department_id = null
      this.program_id = null
      this.form = {
        group: null,
        subject_id: null,
        term_id: null,
        student_grades: [
          {
            student_id: null,
            mid_mark: null,
            final_mark: null,
          },
        ],
      }

      this.$refs.form.$refs.observer.reset()
    },
    onSubmit() {
      this.loading = true

      this.result = {
        added: [],
        already_exists: [],
        not_found: [],
      }

      this.form.term_id = this.termId

      if (this.term.study_plan === 'semester') {
        this.form.is_second_attempt = '0'
      }

      ApiService.post('import-grades', this.form)
        .then(({ data }) => {
          this.result = data
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.initValues()
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
  created() {
    this.pageLoading = true
    Promise.all([
      ApiService.get(`terms/${this.termId}`).then(
        ({ data }) =>
          (this.term = {
            id: data.id,
            title: `${this.$t('terms.seasons.' + data.title)} - ${data.year}`,
            study_plan: data.study_plan,
          })
      ),
      this.$store.dispatch('getPrograms'),
    ]).finally(() => {
      this.pageLoading = false
    })
  },
}
</script>
