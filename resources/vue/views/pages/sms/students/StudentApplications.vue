<template>
  <card :loading="loading" body-fit title="طلبات التحاق الطلبة" title-icon="flaticon-users">
    <template slot="toolbar">
      <b-dropdown class="mr-2" text="طباعة" variant="label-success">
        <template #button-content>
          <v-icon icon="la la-print"></v-icon>
          طباعة
        </template>
        <b-dropdown-item :href="printAcceptedStudentUrl">
          طباعة الطلبة المقبولين
        </b-dropdown-item>
        <b-dropdown-item :href="printExcelUrl">
          طباعة الطلبة حسب الفرز
        </b-dropdown-item>
      </b-dropdown>
      <v-button v-if="canAdd" :to="{ name: 'createStudentApplication' }" icon="flaticon2-plus" variant="label-brand">
        إضافة طالب
      </v-button>
    </template>
    <template slot="body">
      <v-table :data="datatable" :headers="headers" :max-items="maxItems" :total="total" pagination search sst
        @search="changeSearch" @sort="changeSort" @change-page="changePage" @max-items="changeMaxItem">
        <template slot="toolbar">
          <div class="col-lg-4"></div>
          <div class="col-lg-4"></div>

          <div class="col-lg-3">
            <select-input v-model="status" :options="statusOptions" clearable label="الحالة" />
          </div>
          <div v-if="$acl.middleware('university')" class="col-lg-3">
            <select-input v-model="school_id" :options="schools" clearable label="الكلية" searchable />
          </div>
          <div v-if="$acl.middleware('university|school')" class="col-lg-3">
            <select-input v-model="department_id" :options="departments" clearable label="القسم" searchable />
          </div>
          <div v-if="$acl.middleware('university|school|department')" class="col-lg-3">
            <select-input v-model="program_id" :options="programs" clearable label="البرنامج الدراسي" searchable />
          </div>
          <div class="col-12 d-flex justify-content-end flex-wrap">
            <v-button class="mb-4" variant="label-brand" wide @click="removeFilter">الغاء الفرز</v-button>
          </div>
        </template>
        <template slot="td-status" slot-scope="{ row }">
          <div>
            <b-badge v-show="row.status === 'student_id_issued'" pill variant="success">تم صرف رقم القيد</b-badge>
            <b-badge v-show="row.status === 'accepted'" pill variant="success">مقبول</b-badge>
            <b-badge v-show="row.status === 'confirmed'" pill variant="success">مطابقة</b-badge>
            <b-badge v-show="row.status === 'declined'" pill variant="danger">مرفوض</b-badge>
            <b-badge v-show="row.status === 'default'" pill variant="warning">قيد الانتظار</b-badge>
          </div>
        </template>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button v-if="row.status !== 'student_id_issued' || $acl.has('superadmin')"
              :to="{ name: 'editStudentApplication', params: { student_id: row.id } }" icon="la la-edit" icon-only
              size="sm" variant="label-brand" />
            <v-button class="ml-2" size="sm" variant="label-brand" :href="`/applications/${row.id}/receipt`"
              target="_blank">
              نموذج
            </v-button>
            <v-button v-if="
              $acl.middleware('university') &&
              $acl.has('general registrar') &&
              row.status === 'accepted' &&
              row.status !== 'student_id_issued'
            " class="ml-2" size="sm" variant="label-success" @click="accept(row)">
              صرف رقم قيد
            </v-button>
            <v-button v-if="
              $acl.has('general registrar') &&
              row.status === 'confirmed' &&
              row.status !== 'accepted' &&
              row.status !== 'student_id_issued'
            " class="ml-2" size="sm" variant="label-success" @click="changeStatus(row, 'accepted')">
              قبول
            </v-button>
            <v-button v-if="
              row.status !== 'confirmed' &&
              row.status !== 'accepted' &&
              row.status !== 'declined' &&
              row.status !== 'student_id_issued'
            " class="ml-2" size="sm" variant="label-success" @click="changeStatus(row, 'confirmed')">
              مطابقة
            </v-button>
            <v-button
              v-if="row.status !== 'declined' && row.status !== 'accepted' && row.status !== 'student_id_issued'"
              class="ml-2" size="sm" variant="label-danger" @click="changeStatus(row, 'declined')">
              رفض
            </v-button>
            <v-button v-if="$acl.has('general registrar') && row.status !== 'student_id_issued'" @click="remove(row)"
              class="ml-2" icon="la la-remove" variant="label-danger">
              حذف
            </v-button>
          </div>
        </template>
      </v-table>
    </template>
  </card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import VTable from '@/components/datatable/VTable'
import ApiService from '@/common/api.service'
import SelectInput from '@/components/form-controls/SelectInput'
import PaginationMixinVue from '@/mixins/PaginationMixin.vue'

const debounce = require('debounce')

export default {
  name: 'student-applications',
  components: { SelectInput, VTable, Card },
  mixins: [PaginationMixinVue],
  data() {
    return {
      loading: true,
      students: [],
      status: null,
      school_id: null,
      department_id: null,
      program_id: null,
      entity_id: null,
      headers: [
        { key: 'registration_number', name: 'رقم التسجيل', sort: true },
        { key: 'full_name', name: 'اسم الطالب', sort: true, expend: true },
        { key: 'national_id', name: 'الرقم الوطني/جواز السفر', expend: true },
        { key: 'school', name: 'الكلية', expend: true },
        { key: 'entity', name: 'القسم', expend: true },
        { key: 'program', name: 'البرنامج الدراسي', expend: true },
        { key: 'status', name: 'الحالة', expend: true },
        { key: 'actions', name: 'العمليات' },
      ],

      statusOptions: [
        { id: 'default', title: 'قيد الانتظار' },
        { id: 'confirmed', title: 'المطابقة' },
        { id: 'accepted', title: 'المقبولين' },
        { id: 'declined', title: 'المرفوضين' },
      ],
    }
  },
  watch: {
    school_id: { handler: 'loadData' },
    department_id: { handler: 'loadData' },
    program_id: { handler: 'loadData' },
    status: { handler: 'loadData' },
  },
  computed: {
    canAdd() {
      return true
      // return
      // (
      //   this.$acl.isStageActive('new students registration') ||
      //   this.$acl.isStageActive('new doctorate students registration')
      // )
    },
    datatable() {
      const lang = this.$lang === 'ar' ? 'ar' : 'en'
      return this.students.map(student => {
        const entity = this.$store.getters.entityById(student.entity_id)
        const school = this.$store.getters.entityById(entity.parent_entity_id)?.title
        const program = this.$store.getters.programsOptions.find(item => item.id === student.program_id) || {}
        const name = ` ${student['first_name_' + lang]} ${student['second_name_' + lang]} ${student['third_name_' + lang]
          } ${student['last_name_' + lang]} `

        return {
          id: student.id,
          full_name: name,
          registration_number: student.registration_number,
          email: student.email,
          entity: entity.title,
          school_id: entity.parent_entity_id,
          entity_id: student.entity_id,
          program_id: student.program_id,
          program: program.title,
          status: student.status,
          school: school,
          national_id: student.national_id || student.passport_number,
          phone_number: student.phone_number,
        }
      })
    },
    entity() {
      return this.$store.getters.entity
    },
    entities() {
      return this.$store.getters.flatEntities
    },
    entityTypes() {
      return this.$store.getters.entityTypesEnum
    },
    schools() {
      return this.$store.getters.schools
    },
    departments() {
      return this.school_id
        ? this.$store.getters.departments.filter(e => e.parent_entity_id === this.school_id)
        : this.$store.getters.departments
    },
    programs() {
      return this.department_id
        ? this.$store.getters.programsByEntityId(this.department_id)
        : this.$store.getters.programsOptions
    },
    printAcceptedStudentUrl() {
      let url = `/applications/accepted/export`
      const entityId = this.department_id || this.school_id || null
      if (entityId) url += `?entity_id=${entityId}`
      return url
    },
    printExcelUrl() {
      const searchParam = new URLSearchParams()
      const entityId = this.department_id || this.school_id || null

      if (entityId) searchParam.append('entiy_id', entityId)
      if (this.program_id) searchParam.append('program_id', this.program_id)
      if (this.status) searchParam.append('status', this.status)
      return `/applications/export?${searchParam.toString()}`
    },
  },
  methods: {
    removeFilter() {
      this.school_id = null
      this.department_id = null
      this.program_id = null
      this.status = null
    },
    remove(student) {
      const { id, full_name } = student
      const msg = `هل انت متأكد بأنك تريد حذف الطالب (${full_name})؟`
      this.$utils.confirm(msg).then(({ value }) => {
        if (!value) return
        ApiService.delete('sms/applications', id)
          .then(() => {
            const index = this.students.findIndex(student => student.id === id)
            this.students.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
      })
    },
    accept(student) {
      const msg = ` هل انت متأكد بأنك تريد تسجيل (${student.full_name})؟ كما انه سيتم صرف رقم قيد لهذا الطالب وتحويله إلى قائمة الطلبة الفعليين.`
      this.$utils.confirm(msg).then(({ value }) => {
        if (!value) return
        this.loading = true
        ApiService.put(`sms/applications/${student.id}/accept`)
          .then(({ data }) => {
            if (data.message === 'no_active_term') {
              this.$utils.alert('لا يمكنك قبول الطلبة في حالة عدم وجود فصل مفعل!', 'warning')
            } else if (data.message === 'failed') {
              this.$utils.alert(this.$t('msg.error'), 'error')
            } else {
              student.status = 'student_id_issued'
              this.$utils.alert('تم صرف رقم القيد للطالب بنجاح وتم تحويله إلى قائمة الطلبة الفعليين', 'success')
            }
          })
          .catch(error => {
            this.$utils.alert(
              error.response.status === 400 ? error?.response?.data?.message : this.$t('msg.error'),
              'error'
            )
          })
          .finally(() => (this.loading = false))
      })
    },
    changeStatus(student, status) {
      let msg = ''
      switch (status) {
        case 'declined':
          msg = `هل انت متأكد بأنك تريد رفض (${student.full_name})؟`
          break
        case 'accepted':
          msg = `هل انت متأكد بأنك تريد قبول (${student.full_name})؟`
          break
        case 'confirmed':
          msg = `هل انت متأكد بأنك تريد مطابقة (${student.full_name})؟`
          break
      }
      this.$utils.confirm(msg).then(({ value }) => {
        if (!value) return
        this.loading = true

        ApiService.put(`sms/applications/${student.id}/change-status`, { status })
          .then(() => (student.status = status))
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
          .finally(() => (this.loading = false))
      })
    },
    loadData: debounce(function () {
      this.loading = true
      this.filterData.entity_id = this.department_id || this.school_id || null
      this.filterData.program_id = this.program_id
      this.filterData.status = this.status

      ApiService.query('sms/applications', this.filterData)
        .then(({ data }) => {
          this.total = data.meta.total
          this.currentPage = data.meta.current_page

          this.students = data.data
        })
        .finally(() => (this.loading = false))
    }, 200),
    printStudentRecepit(student) {
      const formData = {
        username: student.national_id,
        phone_number: student.phone_number,
      }

      ApiService.post('application/get-receipt', formData)
        .then(({ data }) => {
          if (data.message === 'success') {
            this.$swal({
              title: `لقد تم إرسال طلب الحصول على نموذج التسجيل بنجاح`,
              text: null,
              icon: 'success',
              confirmButtonText: `<a href="${data.url}" target="_blank" class="text-white">طباعة النموذج</a>`,
            })
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .catch(({ response }) => {
          if (response?.status >= 400 && response?.status <= 500) {
            this.error = 'البيانات التي قمت بإدخالها ليست موجوده أو خاطئ.'
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .finally(() => (this.loading = false))
    },
  },
  created() {
    if (this.$acl.middleware('school')) {
      this.headers.splice(3, 1)
    } else if (this.$acl.middleware('department')) {
      this.headers.splice(3, 2)
    }

    this.$store.dispatch('getPrograms')
  },
}
</script>
