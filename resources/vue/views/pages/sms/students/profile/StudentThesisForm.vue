<template>
  <Card :loading="pageLoading" :title="id ? 'تحرير رسالة' : 'إضافة مقترح رسالة'" title-icon="la la-paperclip">
    <template #toolbar>
      <v-button
        v-if="id"
        :href="`/sms/students/${studentId}/theses/${id}/proposal`"
        icon="la la-print"
        target="_blank"
        variant="label-success"
      >
        طباعة المقترح
      </v-button>
    </template>
    <template slot="body">
      <FormValidation :loading="loading" @submit="onSubmit">
        <TextInput
          v-model="form.title_ar"
          :label-cols-lg="labelColsLg"
          label="عنوان الرسالة بالعربية"
          name="title_ar"
          rules="required"
        />
        <TextInput
          v-model="form.title_en"
          :label-cols-lg="labelColsLg"
          label="عنوان الرسالة بالإنجليزية"
          name="title_en"
          rules="required"
        />
        <SelectInput
          v-model="form.teacher_1"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          label="المشرف الأول"
          name="teacher_1"
          rules="required"
          searchable
        />
        <TextInput
          v-model="form.thesis_field"
          :label-cols-lg="labelColsLg"
          label="مجالات الإستفادة من البحث"
          name="thesis_field"
        />
        <DatepickerInput
          v-model="form.discussion_expected_date"
          :label-cols-lg="labelColsLg"
          label="تاريخ المتوقع للمناقشة"
          name="discussion_expected_date"
        />
        <SelectInput
          v-model="form.teacher_2"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المشرف التاني"
          name="teacher_2"
          searchable
        />
        <SelectInput
          v-model="form.teacher_3"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المشرف الثالث"
          name="teacher_3"
          searchable
        />
        <SelectInput
          v-model="form.teacher_4"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المشرف الرابع"
          name="teacher_4"
          searchable
        />
        <SelectInput
          v-model="form.examiner_1"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المناقش الأول"
          name="teacher_1"
          searchable
        />
        <SelectInput
          v-model="form.examiner_2"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المناقش التاني"
          name="examiner_2"
          searchable
        />
        <SelectInput
          v-model="form.examiner_3"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المناقش الثالث"
          name="examiner_3"
          searchable
        />
        <SelectInput
          v-model="form.examiner_4"
          :label-cols-lg="labelColsLg"
          :options="lecturers"
          clearable
          label="المناقش الرابع"
          name="examiner_4"
          searchable
        />
        <TextInput
          v-model="form.reg_decision_no"
          :label-cols-lg="labelColsLg"
          label="رقم قرار الإشراف"
          name="reg_decision_no"
        />
        <SelectInput
          v-model="form.register_semester"
          :label-cols-lg="labelColsLg"
          :options="terms"
          clearable
          label="الفصل الدراسي لقرار الإشراف"
          name="register_semester"
          searchable
        />
        <DatepickerInput
          v-model="form.register_date"
          :label-cols-lg="labelColsLg"
          label="تاريخ قرار الإشراف"
          name="register_date"
        />
        <TextInput
          v-model="form.discussion_decision_no"
          :label-cols-lg="labelColsLg"
          label="رقم قرار المناقشة"
          name="discussion_decision_no"
        />
        <DatepickerInput
          v-model="form.discussion_date"
          :label-cols-lg="labelColsLg"
          label="تاريخ المناقشة"
          name="discussion_date"
        />
        <SelectInput
          v-model="form.discussion_semester"
          :label-cols-lg="labelColsLg"
          :options="terms"
          clearable
          label="الفصل الدراسي للمناقشة"
          name="discussion_semester"
          searchable
        />
        <SelectInput
          v-model="form.hall_id"
          :label-cols-lg="labelColsLg"
          :options="halls"
          clearable
          label="قاعة المناقشة"
          name="hall_id"
          searchable
        />
        <SelectInput
          v-model="form.thesis_grade"
          :label-cols-lg="labelColsLg"
          :options="grades"
          label="التقدير"
          name="thesis_grade"
        />
        <TextareaInput v-model="form.notes" :label-cols-lg="labelColsLg" label="ملاحظات" name="notes" rows="6"/>
        <SelectInput
          v-model="form.proposal_status"
          :label-cols-lg="labelColsLg"
          :options="proposalStatuses"
          label="حالة الرسالة"
          name="proposal_status"
        />
        <div class="row mb-3">
          <CheckBoxInput
            v-model="form.is_passed"
            :class="`offset-lg-${labelColsLg}`"
            :label-cols-lg="labelColsLg"
            size="lg"
          >
            تم إجتياز الرسالة؟
          </CheckBoxInput>
        </div>
        <validation-error :errors="errors"/>
        <div class="d-flex justify-content-end">
          <b-button class="px-4 mr-2" to="./">{{ $t('form.back') }}</b-button>
          <v-button :loading="loading" style="width: 20rem" type="submit" variant="brand" wide>
            {{ id ? $t('form.save') : $t('form.add') }}
          </v-button>
        </div>
      </FormValidation>
      <!--end::Form-->
    </template>
  </Card>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput'
import TextareaInput from '@/components/form-controls/TextareaInput'
import SelectInput from '@/components/form-controls/SelectInput'
import DatepickerInput from '@/components/form-controls/DatepickerInput'
import RadioInputGroup from '@/components/form-controls/RadioInputGroup'
import FormValidation from '@/components/form-controls/FormValidation'
import Card from '@/views/partials/content/Card.vue'
import ApiService from '@/common/api.service'
import ValidationError from '@/components/ValidationError.vue'
import CheckBoxInput from '@/components/form-controls/CheckBoxInput.vue'

export default {
  name: 'StudentForm',
  components: {
    Card,
    FormValidation,
    RadioInputGroup,
    DatepickerInput,
    SelectInput,
    TextInput,
    TextareaInput,
    ValidationError,
    CheckBoxInput,
  },
  data() {
    return {
      pageLoading: true,
      loading: false,
      errors: null,
      labelColsLg: 3,
      form: {
        title_ar: null,
        title_en: null,
        thesis_field: null,
        thesis_type: null,
        teacher_1: null,
        teacher_2: null,
        teacher_3: null,
        teacher_4: null,
        examiner_1: null,
        examiner_2: null,
        examiner_3: null,
        examiner_4: null,
        reg_decision_no: null,
        register_semester: null,
        register_date: null,
        discussion_decision_no: null,
        discussion_expected_date: null,
        discussion_date: null,
        discussion_semester: null,
        thesis_grade: null,
        notes: null,
        hall_id: null,
        proposal_status: 'pending',
        is_passed: false,
      },
    }
  },
  computed: {
    id() {
      return this.$route.params.thesis_id
    },
    studentId() {
      return this.$route.params.student_id
    },
    terms() {
      return this.$store.getters.terms.map(term => ({
        id: term.id,
        title: `${this.$t('terms.seasons.' + term.title)} - ${term.year}`,
      }))
    },
    lecturers() {
      return this.$store.getters['sms/lecturers']
    },
    proposalStatuses() {
      return this.$store.getters['sms/thesisProposalStatuses'].map(status => ({
        id: status,
        title: this.$t('theses.statuses.' + status),
      }))
    },
    grades() {
      return this.$store.getters['sms/thesisGrades'].map(grade => ({
        id: grade,
        title: this.$t('theses.grades.' + grade),
      }))
    },
    halls() {
      return this.$store.getters['sms/buildings'].reduce((acc, building) => {
        building.halls.forEach(hall => {
          acc.push({
            id: hall.id,
            title: `${this.$utils.getCurrentOrFirstLang(building.title)} - ${this.$utils.getCurrentOrFirstLang(
              hall.title
            )}`,
          })
        })
        return acc
      }, [])
    },
  },
  methods: {
    onSubmit() {
      this.loading = true

      this.request()
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.$router.push({name: 'student-theses'})
        })
        .catch(({response}) => {
          if (response?.status === 422) {
            this.errors = response.data.errors
          } else {
            this.$utils.alert(this.$t('msg.error'), 'error')
          }
        })
        .finally(() => (this.loading = false))
    },
    request() {
      const request = `sms/students/${this.studentId}/theses`
      if (!this.id) {
        return ApiService.post(request, this.form)
      } else {
        return ApiService.put(`${request}/${this.id}`, this.form)
      }
    },
  },
  async created() {
    await Promise.all([
      this.$store.dispatch('getTerms'),
      this.$store.dispatch('sms/getLecturers'),
      this.$store.dispatch('sms/getBuildings'),
    ])
    if (this.id) {
      try {
        const {data} = await ApiService.get(`sms/students/${this.studentId}/theses`, this.id)
        this.form = data
        this.form.is_passed = !!data.is_passed
      } catch (error) {
        this.$utils.errorHandlerWith404(error)
      }
    }

    this.pageLoading = false
  },
}
</script>
