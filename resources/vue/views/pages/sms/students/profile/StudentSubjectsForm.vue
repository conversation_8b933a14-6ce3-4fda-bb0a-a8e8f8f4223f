<template>
  <div>
    <b-modal ref="modal" :title="title" centered hide-footer @hide="close">
      <form-validation :loading="loading" @submit="onSubmit">
        <b-row>
          <select-input
            class="col-12"
            label="المادة الدراسية"
            v-model="form.subject_id"
            :options="subjects"
            :disabled="groupOnly"
            name="subject_id"
            rules="required"
            searchable
          />
          <select-input
            class="col-12"
            label="المجموعة"
            v-model="form.group"
            :options="groups"
            :reduce="obj => obj"
            searchable
            name="code"
            rules="required"
          />
        </b-row>
        <hr />
        <div class="mt-4 d-flex justify-content-end">
          <v-button class="px-4 mr-2" variant="label-brand" @click="close">{{ $t('form.back') }}</v-button>
          <v-button type="submit" variant="brand" wide :loading="loading">{{ $t('form.save') }}</v-button>
        </div>
      </form-validation>
    </b-modal>
    <b-modal
      id="collisionModal"
      centered
      title="تنبيه هناك تعارض !"
      ok-variant="danger"
      ok-title="موافق"
      cancel-title="رجوع"
      size="lg"
      @ok="forceSubmit"
    >
      <p>يوجد تعارض للطالب مع المادة الدراسية المراد تنزيلها</p>
      <div class="mt-4 text-danger font-weight-bold">
        <p class="">
          * إذا كنت ترغب في تنزيل المادة الدراسية مع وجود التعارض إضغط على موافق.
        </p>
      </div>
    </b-modal>
  </div>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput.vue'
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation.vue'
import SelectInput from '@/components/form-controls/SelectInput.vue'

export default {
  name: 'StudentSubjectsForm',
  components: { TextInput, FormValidation, SelectInput },
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    subjects: {
      type: Array,
      required: true,
    },
    termId: {
      type: [Number, String],
      default: null,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    isActiveTerm: {
      type: Boolean,
      required: true,
    },
    groupOnly: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    active(val) {
      if (!val) return

      this.$refs.modal.show()
      if (this.data.id) {
        this.form = {
          old_subject_id: this.data.id,
          old_group: this.data.group,
          subject_id: this.data.id,
          group: this.data.group,
        }
      } else {
        this.form = this.initValues()
      }
    },
  },
  data() {
    return {
      form: {
        subject_id: null,
        group: null,
      },
      loading: false,
    }
  },
  computed: {
    title() {
      return this.data.title ? `استبدال المادة الدراسية ${this.data.title} ${this.data.group}` : 'تنزيل مادة دراسية'
    },
    groups() {
      this.form.group = null
      return this.isActiveTerm
        ? this.subjects.find(i => i.id === this.form.subject_id)?.groups || []
        : Array.from(Array(26)).map((_, i) => String.fromCharCode(i + 65))
    },
    studentId() {
      return this.$route.params.student_id
    },
  },
  methods: {
    initValues() {
      return {
        subject_id: null,
        group: null,
      }
    },
    forceSubmit() {
      this.form.force = true
      this.onSubmit()
    },
    onSubmit() {
      this.loading = true

      this.request()
        .then(({ data }) => {
          if (data.message === 'success') {
            this.$emit('save', { ...data.subject })
            this.close()
            this.$utils.alert('تمت العملية بنجاح', 'success')
          } else {
            this.$utils.alert(data.message, 'error')
          }
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
    request() {
      if (this.groupOnly) {
        return ApiService.put(
          this.$acl.middleware('university|school')
            ? `sms/students/${this.studentId}/terms/${this.termId}/replace-subject-group`
            : `sms/students/${this.studentId}/replace-subject-group`,
          this.form
        )
      }
      if (this.data.id) {
        return ApiService.put(
          this.$acl.middleware('university|school')
            ? `sms/students/${this.studentId}/terms/${this.termId}/replace-subject`
            : `sms/students/${this.studentId}/replace-subject`,
          this.form
        )
      } else {
        return ApiService.post(
          this.$acl.middleware('university|school')
            ? `sms/students/${this.studentId}/terms/${this.termId}/subjects`
            : `sms/students/${this.studentId}/subjects`,
          this.form
        )
      }
    },
    close() {
      this.$refs.modal.hide()
      this.$emit('update:active', false)
    },
  },
}
</script>
