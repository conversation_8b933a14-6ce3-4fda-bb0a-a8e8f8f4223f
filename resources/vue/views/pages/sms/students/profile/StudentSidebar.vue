<template>
  <b-overlay :show="!student.id" :opacity="1">
    <div ref="kt-app__aside" class="kt-grid__item kt-app__toggle kt-app__aside" id="kt_user_profile_aside">
      <button class="kt-app__aside-close" id="kt_user_profile_aside_close">
        <i class="la la-close"></i>
      </button>
      <div class="kt-portlet kt-portlet--height-fluid-">
        <div class="kt-portlet__head  kt-portlet__head--noborder">
          <div class="kt-portlet__head-label">
            <h3 class="kt-portlet__head-title"></h3>
          </div>
        </div>
        <div class="kt-portlet__body kt-portlet__body--fit-y">
          <div class="kt-widget kt-widget--user-profile-1">
            <div class="kt-widget__head mb-4">
              <div class="kt-widget__media">
                <img :src="user.avatar" :alt="userShortName" />
              </div>
              <div class="kt-widget__content">
                <div class="kt-widget__section">
                  <a href="#" class="kt-widget__username">
                    {{ userShortName }}
                  </a>
                  <span class="kt-widget__subtitle">
                    {{ student.id }}
                  </span>
                </div>
              </div>
            </div>
            <div class="kt-widget__body">
              <div class="kt-widtget__item">
                <b-alert :show="!!totalNotices" class="rounded-0 mb-2" variant="solid-danger">
                  الإنذارات المتحصل عليها: {{ totalNotices }}
                </b-alert>
              </div>
              <div class="kt-widget__items">
                <template v-for="item in sidebar">
                  <router-link
                    v-if="!item.type || item.type === user.type"
                    :key="item.title"
                    :to="item.route"
                    :class="[{ 'kt-widget__item--active': isActive(item.route) }, 'kt-widget__item']"
                  >
                    <span class="kt-widget__section">
                      <span class="kt-widget__icon">
                        <v-icon :icon="item.icon" />
                      </span>
                      <span class="kt-widget__desc">{{ item.title }}</span>
                    </span>
                  </router-link>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </b-overlay>
</template>

<script>
import KTOffcanvas from '@/assets/js/offcanvas'
import { mapGetters } from 'vuex'

export default {
  name: 'StudentSidebar',
  props: {
    sidebar: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  computed: {
    ...mapGetters({
      totalNotices: 'students/totalNotices',
    }),
    student() {
      return this.$store.state.students.student || {}
    },
    user() {
      return this.student?.user || {}
    },
    userShortName() {
      return `${this.user['first_name_' + this.$lang]} ${this.user['last_name_' + this.$lang]}`
    },
  },
  methods: {
    isActive(route) {
      return this.$route.name === route.name
    },
  },
  mounted() {
    this.$store.state.config.config.subheader.aside = true
    this.$store.dispatch('overrideLayoutConfig')

    this.$nextTick(function() {
      new KTOffcanvas(this.$refs['kt-app__aside'], {
        baseClass: 'kt-app__aside',
        overlay: true,
        closeBy: 'kt_user_profile_aside_close',
        toggleBy: {
          // the target button to active the slide menu, located in header mobile component
          target: 'kt_subheader_mobile_toggle',
          state: 'kt-header-mobile__toolbar-toggler--active',
        },
      })
    })
  },
}
</script>
