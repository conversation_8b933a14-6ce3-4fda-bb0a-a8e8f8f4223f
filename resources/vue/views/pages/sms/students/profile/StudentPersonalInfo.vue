<template>
  <Card :loading="loading" :title="$t('profile.personal_information')">
    <template #toolbar>
      <v-button
        v-if="$acl.middleware('university|school|department')"
        :to="{ name: 'editStudent', params: { student_id: id } }"
        class="ml-2"
        icon="la la-edit"
        variant="label-brand"
      >
        تعديل
      </v-button>
    </template>
    <div v-if="student.id">
      <div v-for="(item, index) in info" :key="index" class="form-group row">
        <label class="col-xl-3 col-lg-3 col-form-label text-left text-lg-right">{{ item.label }}:</label>
        <div class="col-lg-9 col-xl-6">
          <input :value="item.value" class="form-control" disabled type="text" />
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'

export default {
  name: 'StudentPersonalInfo',
  components: { Card },
  computed: {
    id() {
      return this.$route.params.student_id
    },
    info() {
      const department = this.$store.getters.entityById(this.user.entity_id)
      const school = this.$store.getters.entityById(department?.parent_entity_id)

      return [
        {
          label: 'الأسم [باللغة العربية]',
          value: `${this.user.first_name_ar} ${this.user.second_name_ar} ${this.user.third_name_ar} ${this.user.last_name_ar}`,
        },
        {
          label: 'الأسم [باللغة الانجليزية]',
          value: `${this.user.first_name_en} ${this.user.second_name_en} ${this.user.third_name_en} ${this.user.last_name_en}`,
        },
        { label: 'الجنس', value: this.$t('users.' + this.user.gender) },
        { label: 'تاريخ الميلاد', value: this.user.dob },
        { label: 'الجنسية', value: this.$utils.getCurrentOrFirstLang(this.user?.nationality?.title) },
        { label: 'الرقم الوطني', value: this.user.national_id },
        { label: 'رقم البطاقة الشخصية', value: this.user.personal_id },
        { label: 'رقم جواز السفر', value: this.user.passport_number || '#' },
        { label: 'رقم القيد', value: this.student.id },
        { label: 'الكلية', value: school?.title },
        { label: 'القسم', value: department?.title },
        { label: 'البرنامج الدراسي', value: this.$utils.getCurrentOrFirstLang(this.student?.program?.title) },
        { label: 'البريد الإلكتروني', value: this.user.email },
        { label: 'رقم الهاتف', value: this.user.phone_number },
        { label: 'اسم الام', value: this.student.mother_name },
        { label: 'مكان الولادة [بالعربية]', value: this.student.birth_place },
        { label: 'مكان الولادة [بالإنجليزية]', value: this.student.birth_place_en },
        {
          label: 'الحالة الاجتماعية',
          value: this.$utils.getCurrentOrFirstLang(this.student?.martial_status_content?.title),
        },
        { label: 'مكان العمل', value: this.student.work_place },
        { label: 'عنوان السكن', value: this.user.address },
      ]
    },
    student() {
      return this.$store.state.students.student || {}
    },
    user() {
      return this.student.user || {}
    },
    loading() {
      return !this.student.id
    },
  },
}
</script>

<style lang="scss" scoped>
h4 {
  color: #000;
  margin-bottom: 1rem;
}

table {
  td {
    width: 1%;
    white-space: nowrap;
  }

  th,
  td {
    border: 1px solid var(--gray);
  }
}
</style>
