<template>
  <b-modal ref="modal" title="تعيين خريج" centered hide-footer @hide="close">
    <form-validation :loading="loading" @submit="onSubmit">
      <p>هل انت متأكد انك تريد تعيين الطالب صاحب رقم القيد ({{ id }}) كخريج؟</p>
      <p class="text-danger">
        *إذا تم تعيين الطالب كخريج لن يستطيع الطالب الدخول إلى النظام مجدداً ولا يمكنه دراسة اي مقرر.
      </p>
      <p>يرجى تحديد نوع الشهادة الممنوحة</p>
      <b-row>
        <radio-input-group
          label="نوع الشهادة"
          class="col-12"
          rules="required"
          name="status"
          :options="options"
          v-model="is_diploma"
        />
      </b-row>
      <b-row>
        <TextInput
          class="col-12"
          rules="required"
          label="كلمة المرور"
          name="password"
          type="password"
          v-model="password"
        />
      </b-row>
      <hr />
      <div class="mt-4 d-flex justify-content-end">
        <v-button class="px-4 mr-2" variant="clean" bold @click="close">{{ $t('form.back') }}</v-button>
        <v-button type="submit" variant="brand" wide :loading="loading">تعيين</v-button>
      </div>
    </form-validation>
  </b-modal>
</template>

<script>
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation.vue'
import SelectInput from '@/components/form-controls/SelectInput.vue'
import DatepickerInput from '@/components/form-controls/DatepickerInput.vue'
import RadioInputGroup from '@/components/form-controls/RadioInputGroup.vue'
import TextInput from '@/components/form-controls/TextInput.vue'

export default {
  name: 'TermStageForm',
  components: { FormValidation, SelectInput, DatepickerInput, RadioInputGroup, TextInput },
  props: {
    active: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    active(val) {
      if (!val) return
      this.$refs.modal.show()
      this.is_diploma = false
      this.password = null
    },
  },
  data() {
    return {
      form: {},
      loading: false,
      is_diploma: false,
      password: null,
      options: [
        { value: true, text: 'دبلوم' },
        { value: false, text: 'ماجستير' },
      ],
    }
  },
  computed: {
    id() {
      return this.$route.params.student_id
    },
  },
  methods: {
    onSubmit() {
      this.loading = true

      ApiService.put(`sms/students/${this.id}/mark-graduated`, { is_diploma: this.is_diploma, password: this.password })
        .then(() => {
          this.$store.commit('students/setStatus', 'graduated')
          this.close()
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(({ response }) => {
          this.$utils.alert(
            response?.data?.errors ? Object.values(response.data.errors)[0][0] : this.$t('msg.error'),
            'error'
          )
        })
        .finally(() => (this.loading = false))
    },
    close() {
      this.$refs.modal.hide()
      this.$emit('update:active', false)
    },
  },
}
</script>
