<template>
  <card :loading="loading" :title="title" body-fit>
    <template #toolbar>
      <v-button
        :to="{ name: 'lecturer-panel-subject-grades', params: { subject_id: subjectId }, query: { group: group } }"
        class="mr-2"
        variant="brand"
      >
        رصد الدرجات
      </v-button>
      <v-button
        :to="{ name: 'lecturer-panel-subject-appeals', params: { subject_id: subjectId }, query: { group: group } }"
        class="mr-2"
        variant="brand"
      >
        الطعون
      </v-button>
      <b-dropdown variant="success">
        <template #button-content>
          <v-icon icon="la la-print"></v-icon>
          طباعة
        </template>
        <b-dropdown-item :href="`/lecturer-panel/subjects/${subjectId}/export?group=${group}`" target="_blank">
          قائمة الطلبة
        </b-dropdown-item>
        <b-dropdown-item :href="`/lecturer-panel/subjects/${subjectId}/export-appeals?group=${group}`" target="_blank">
          قائمة الطعون
        </b-dropdown-item>
        <b-dropdown-item
          :href="`/lecturer-panel/subjects/${subjectId}/export-students-excel?group=${group}`"
          target="_blank"
        >
          قائمة الطلبة (Excel)
        </b-dropdown-item>
        <b-dropdown-item
          :href="`/lecturer-panel/subjects/${subjectId}/export-attendance?group=${group}`"
          target="_blank"
        >
          قائمة الحضور
        </b-dropdown-item>
        <b-dropdown-item
          :href="`/lecturer-panel/subjects/${subjectId}/export-exams-attendance?group=${group}`"
          target="_blank"
        >
          كشف حضور الامتحان
        </b-dropdown-item>
        <b-dropdown-item
          :href="`/lecturer-panel/subjects/${subjectId}/export-exams-attendance-second-attempt?group=${group}`"
          target="_blank"
        >
          كشف حضور الامتحان الدور الثاني
        </b-dropdown-item>
        <b-dropdown-item :href="`/lecturer-panel/subjects/${subjectId}/export-grades?group=${group}`" target="_blank">
          كشف درجات مقرر
        </b-dropdown-item>
        <b-dropdown-item
          :href="`/lecturer-panel/subjects/${subjectId}/export-second-attempt-grades?group=${group}`"
          target="_blank"
        >
          نموذج نتيجة الدور الثاني
        </b-dropdown-item>
      </b-dropdown>
    </template>
    <v-table :data="students" :headers="headers" :max-items="30" indexed pagination search></v-table>
  </card>
</template>

<script>
import ApiService from '@/common/api.service'

import Card from '@/views/partials/content/Card.vue'
import VTable from '@/components/datatable/VTable.vue'

export default {
  components: { Card, VTable },
  data() {
    return {
      loading: true,
      headers: [
        { key: 'id', name: 'رقم القيد', sort: true },
        { key: 'fullName', name: 'اسم الطالب', sort: true },
      ],
      subject: null,
      students: [],
    }
  },
  computed: {
    title() {
      if (!this.subject) return 'تسجلات الطلبة'
      const subjectTitle = this.$utils.getCurrentOrFirstLang(this.subject.title)
      return `تسجيلات الطلبة في ${this.subject.code} ${subjectTitle} (${this.group})`
    },
    subjectId() {
      return this.$route.params.subject_id
    },
    group() {
      return this.$route.query.group
    },
  },
  created() {
    ApiService.query(`lecturer-panel/subjects/${this.subjectId}`, { group: this.group })
      .then(({ data }) => {
        this.subject = data.subject
        this.students = data.students.map(item => {
          const fullAr = `${item.first_name_ar} ${item.second_name_ar} ${item.third_name_ar} ${item.last_name_ar}`
          const fullEn = `${item.first_name_en} ${item.second_name_en} ${item.third_name_en} ${item.last_name_en}`
          return {
            ...item,
            fullName: this.$lang === 'ar' ? fullAr : fullEn,
          }
        })
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
