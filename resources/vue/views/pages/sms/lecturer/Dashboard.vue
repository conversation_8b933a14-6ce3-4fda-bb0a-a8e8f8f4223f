<template>
  <b-row>
    <b-col>
      <card :loading="loading">
        <template #body>
          <b-tabs>
            <b-tab
              v-for="timetable in timetables"
              :key="timetable.id"
              :title="
                `${$utils.getCurrentOrFirstLang(timetable.entity)} ${$t('terms.seasons.' + timetable.term_title)} ${
                  timetable.year
                }`
              "
            >
              <b-tabs nav-class="nav-tabs-line nav-tabs-bold nav-tabs-line-brand">
                <b-tab active>
                  <template #title>
                    <i class="flaticon-edit-1"></i>
                    <span>المواد الدراسية</span>
                  </template>
                  <VTable
                    :headers="headers"
                    :data="subjects.filter(subject => subject.timetable_id === timetable.id)"
                    indexed
                    no-data-text="لا يوجد لديك مواد دراسية في هذا الفصل"
                  >
                    <template slot="td-actions" slot-scope="{ row }">
                      <VButton
                        class="mr-2 mb-2"
                        variant="label-brand"
                        :to="{
                          name: 'lecturer-panel-subject',
                          params: { subject_id: row.id },
                          query: { group: row.group },
                        }"
                      >
                        عرض الطلبة
                      </VButton>
                      <VButton
                        v-if="$acl.isStageActive('second attempt', timetable.term_study_plan)"
                        class="mb-2"
                        variant="label-brand"
                        :to="{
                          name: 'lecturer-panel-subject-second-attempt-grades',
                          params: { subject_id: row.id },
                          query: { group: row.group },
                        }"
                      >
                        عرض طلبة الدور الثاني
                      </VButton>
                    </template>
                  </VTable>
                </b-tab>

                <b-tab>
                  <template #title>
                    <i class="flaticon-calendar-with-a-clock-time-tools"></i>
                    <span>الجدول الدراسي</span>
                  </template>
                  <Timetable
                    :data="timetable.content"
                    :start-time="timetable.start_time"
                    :end-time="timetable.end_time"
                    :duration="timetable.duration"
                    show-program
                  />
                </b-tab>
              </b-tabs>
            </b-tab>
          </b-tabs>
        </template>
      </card>
    </b-col>
  </b-row>
</template>
<script>
import ApiService from '@/common/api.service'

import Card from '@/views/partials/content/Card.vue'
import Timetable from '@/components/timetable/Timetable.vue'
import VTable from '@/components/datatable/VTable.vue'
import VButton from '@/components/VButton.vue'

export default {
  components: { Card, Timetable, VTable, VButton },
  data() {
    return {
      loading: true,
      headers: [
        { key: 'subject_code', name: 'رمز المادة', sort: true },
        { key: 'subject_name', name: 'اسم المادة', sort: true, expend: true },
        { key: 'program', name: 'البرنامج الدراسي', sort: true, expend: true },
        { key: 'group', name: 'المجموعة', sort: true, expend: true },
        { key: 'actions', name: 'العمليات' },
      ],
      subjects: [],
      timetables: [],
    }
  },
  created() {
    this.loading = true
    Promise.all([ApiService.get('lecturer-panel/active-timetable'), ApiService.get('lecturer-panel/active-term')])
      .then(([timetables, subjects]) => {
        this.subjects = subjects.data.map(subject => ({
          ...subject,
          subject_name: this.$utils.getCurrentOrFirstLang(subject.subject_name),
          program: subject.program ? this.$utils.getCurrentOrFirstLang(subject.program.title) : '',
        }))
        this.timetables = timetables.data.filter(timetable => timetable.content.length > 0)
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
