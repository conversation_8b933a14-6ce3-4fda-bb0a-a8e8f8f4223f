<template>
  <card :loading="loading" :title="title" body-fit>
    <template #toolbar></template>
    <v-table :data="students" :headers="headers" :max-items="30" indexed pagination search>
      <template #toolbar>
        <div class="col d-flex align-items-start justify-content-end">
          <v-button class="mr-2" variant="brand" wide @click="submit">حفظ</v-button>
          <!--                <b-dropdown class="ml-2" text="المزيد" variant="label-brand">-->
          <!--                  <b-dropdown-item-button @click="confirmGrades">-->
          <!--                    رصد-->
          <!--                  </b-dropdown-item-button>-->
          <!--                </b-dropdown>-->
        </div>
      </template>
      <template slot="td-final_mark" slot-scope="{ row }">
        <span v-if="row.is_confirmed">{{ row.final_mark }}</span>
        <b-form-input
          v-else
          v-model="row.final_mark"
          :max="subjectFinalMark"
          class="mark-input"
          min="0"
          type="number"
        />
      </template>
    </v-table>
  </card>
</template>

<script>
import ApiService from '@/common/api.service'

import Card from '@/views/partials/content/Card.vue'
import VTable from '@/components/datatable/VTable.vue'

export default {
  components: { Card, VTable },
  data() {
    return {
      loading: true,
      headers: [
        { key: 'id', name: 'رقم القيد', sort: true },
        { key: 'name', name: 'اسم الطالب', sort: true, expend: true },
        { key: 'old_final_mark', name: 'درجة نهائي الدور الأول', sort: true, expend: true },
        { key: 'final_mark', name: 'درجة نهائي الدور الثاني', sort: true, expend: true },
      ],
      subject: null,
      students: [],
    }
  },
  computed: {
    subjectFinalMark() {
      return this.subject?.final_mark || 50
    },
    isConfirmed() {
      return this.students.some(student => student.is_confirmed)
    },
    title() {
      if (!this.subject) return 'درجات الدور الثاني'
      const subjectTitle = this.$utils.getCurrentOrFirstLang(this.subject.title)
      return `درجات الدور الثاني في ${this.subject.code} ${subjectTitle} (${this.group})`
    },
    subjectId() {
      return this.$route.params.subject_id
    },
    group() {
      return this.$route.query.group
    },
  },
  methods: {
    validateMarkInputs() {
      const inputs = document.querySelectorAll('.mark-input')
      if (!inputs) return true

      let validInputs = 0
      inputs.forEach(input => input.reportValidity() && validInputs++)
      return inputs.length === validInputs
    },
    submit() {
      if (!this.validateMarkInputs()) return

      this.loading = true

      ApiService.put(`lecturer-panel/subjects/${this.subjectId}/second-attempt`, {
        group: this.group,
        students: this.students.map(student => ({
          id: student.id,
          final_mark: student.final_mark || 0,
        })),
      })
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.loadData()
        })
        .catch(this.$utils.errorHandlerWithValidation)
        .finally(() => (this.loading = false))
    },
    loadData() {
      this.loading = true
      ApiService.query(`lecturer-panel/subjects/${this.subjectId}/grades`, { group: this.group, failed_only: 1 })
        .then(({ data }) => {
          this.subject = data.subject

          this.students = data.students.map(item => {
            const fullAr = `${item.first_name_ar} ${item.second_name_ar} ${item.third_name_ar} ${item.last_name_ar}`
            const fullEn = `${item.first_name_en} ${item.second_name_en} ${item.third_name_en} ${item.last_name_en}`
            return {
              ...item,
              name: this.$lang === 'ar' ? fullAr : fullEn,
              old_final_mark: item.final_mark,
              final_mark: item?.second_attempt?.final_mark || 0,
              is_confirmed: item?.second_attempt?.is_confirmed || false,
            }
          })
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
  created() {
    this.loadData()
  },
}
</script>
