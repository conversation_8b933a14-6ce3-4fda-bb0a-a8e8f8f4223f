<template>
  <card :loading="loading" :title="title" body-fit>
    <template #toolbar></template>
    <v-table :data="students" :headers="headers" :max-items="30" indexed pagination search>
      <template #toolbar>
        <div v-if="!isConfirmed" class="col d-flex align-items-start justify-content-end">
          <v-button class="mr-2" variant="brand" wide @click="submit">حفظ</v-button>
          <b-dropdown class="ml-2" text="المزيد" variant="label-brand">
            <b-dropdown-item-button @click="confirmGrades">
              رصد
            </b-dropdown-item-button>
          </b-dropdown>
        </div>
      </template>
      <!-- <template slot="td-incomplete" slot-scope="{ row }">
        <b-form-checkbox
          v-model="row.incomplete"
          :disabled="isConfirmed || ['W', 'T'].includes(row.grade)"
          :value="true"
          size="lg"
          @change="changeIncomplete($event, row)"
        />
      </template> -->
      <template slot="td-mid_mark" slot-scope="{ row }">
        <span v-if="isConfirmed">{{ row.mid_mark }}</span>
        <span v-else-if="['I', 'W', 'T'].includes(row.grade)"></span>
        <b-form-input v-else v-model="row.mid_mark" :max="subjectMidMark" class="mark-input" min="0" type="number" />
      </template>
      <template slot="td-final_mark" slot-scope="{ row }">
        <span v-if="isConfirmed">{{ row.final_mark }}</span>
        <span v-else-if="['I', 'W', 'T'].includes(row.grade)"></span>
        <b-form-input v-else v-model="row.final_mark" :max="subjectFinalMark" class="mark-input" min="0"
          type="number" />
      </template>
      <template slot="td-practical_final_mark" slot-scope="{ row }">
        <span v-if="isConfirmed">{{ row.practical_final_mark }}</span>
        <span v-else-if="['I', 'W', 'T'].includes(row.grade)"></span>
        <b-form-input v-else v-model="row.practical_final_mark" :max="subjectPracticalFinalMark" class="mark-input"
          min="0" type="number" />
      </template>
      <template slot="td-notes" slot-scope="{ row }">
        <b-badge v-if="row.has_appeal" variant="primary">
          تم تقديم طعن
        </b-badge>
      </template>
    </v-table>
  </card>
</template>

<script>
import ApiService from '@/common/api.service'

import Card from '@/views/partials/content/Card.vue'
import VTable from '@/components/datatable/VTable.vue'

export default {
  components: { Card, VTable },
  data() {
    return {
      loading: true,
      headers: [
        { key: 'id', name: 'رقم القيد', sort: true },
        { key: 'fullName', name: 'اسم الطالب', sort: true, expend: true },
        // {key: 'incomplete', name: 'غير مكمل؟', expend: true},
        { key: 'mid_mark', name: 'درجة الأعمال', sort: true, expend: true },
        // { key: 'practical_final_mark', name: 'درجة العملي النهائي', sort: true, expend: true },
        { key: 'final_mark', name: 'درجة النهائي', sort: true, expend: true },
        { key: 'full_mark', name: 'المجموع', sort: true, expend: true },
        // { key: 'grade', name: 'التقدير', sort: true, expend: true },
      ],
      subject: null,
      students: [],
    }
  },
  computed: {
    subjectFinalMark() {
      return this.subject?.final_mark || 50
    },
    subjectMidMark() {
      return this.subject?.mid_mark || 50
    },
    subjectPracticalFinalMark() {
      return this.subject?.practical_final_mark || 0
    },
    isConfirmed() {
      return this.students.some(student => student.confirmed_by !== null)
    },
    title() {
      if (!this.subject) return 'تسجلات الطلبة'
      const subjectTitle = this.$utils.getCurrentOrFirstLang(this.subject.title)
      return `تسجيلات الطلبة في ${this.subject.code} ${subjectTitle} (${this.group})`
    },
    subjectId() {
      return this.$route.params.subject_id
    },
    group() {
      return this.$route.query.group
    },
    isAnyStudentHasAppeal() {
      return this.students.some(student => student?.has_appeal)
    }
  },
  methods: {
    changeIncomplete(event, student) {
      student.grade = event ? 'I' : ''
    },
    studentGrades() {
      return this.students.map(student => ({
        id: student.id,
        mid_mark: !student.incomplete ? student.mid_mark || 0 : 0,
        final_mark: !student.incomplete ? student.final_mark || 0 : 0,
        practical_final_mark: !student.incomplete ? student.practical_final_mark || 0 : 0,
        incomplete: student.incomplete,
        has_appeal: student.has_appeal
      }))
    },
    validateMarkInputs() {
      const inputs = document.querySelectorAll('.mark-input')
      if (!inputs) return true

      let validInputs = 0
      inputs.forEach(input => input.reportValidity() && validInputs++)
      return inputs.length === validInputs
    },
    submit() {
      if (!this.validateMarkInputs()) return

      this.loading = true

      ApiService.put(`lecturer-panel/subjects/${this.subjectId}/grades`, {
        group: this.group,
        students: this.studentGrades(),
      })
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.loadData()
        })
        .catch(this.$utils.errorHandlerWithValidation)
        .finally(() => (this.loading = false))
    },
    async confirmGrades() {
      if (!this.validateMarkInputs()) {
        this.$utils.alert('يجب ألا يتجاوز مجموع الدرجات 100', 'error')
        return
      }

      const msg = `هل أنت متأكد من رصد الدرجات ؟ لن تتمكن من تعديل الدرجات مره اخرى بعد الرصد.`
      const confirm = await this.$utils.confirm(msg)
      if (!confirm.value) return

      this.loading = true
      try {
        await ApiService.put(`lecturer-panel/subjects/${this.subjectId}/grades`, {
          group: this.group,
          students: this.studentGrades(),
        })
        await ApiService.post(`lecturer-panel/subjects/${this.subjectId}/grades`, { group: this.group })

        this.$utils.alert(
          'لقد تم رصد الدرجات بنجاح، إذا كانت لديك مشكلة يمكنك التواصل مع رئيس القسم لإلغاء الرصد.',
          'success'
        )
        this.loadData()
      } catch (e) {
        this.$utils.errorHandlerWithValidation(e)
      }
      this.loading = false
    },
    loadData() {
      this.loading = true
      ApiService.query(`lecturer-panel/subjects/${this.subjectId}/grades`, { group: this.group })
        .then(({ data }) => {
          this.subject = data.subject

          if (this.headers.findIndex(header => header.key === 'practical_final_mark') === -1) {
            if (this.subject?.practical_final_mark && this.subject?.practical_final_mark > 0) {
              this.headers.splice(3, 0, {
                key: 'practical_final_mark',
                name: 'درجة العملي النهائي',
                sort: true,
                expend: true,
              })
            }
          }

          this.students = data.students.map(item => {
            const fullAr = `${item.first_name_ar} ${item.second_name_ar} ${item.third_name_ar} ${item.last_name_ar}`
            const fullEn = `${item.first_name_en} ${item.second_name_en} ${item.third_name_en} ${item.last_name_en}`
            return {
              ...item,
              incomplete: item.grade === 'I',
              fullName: this.$lang === 'ar' ? fullAr : fullEn,
            }
          })

          if (this.isAnyStudentHasAppeal) this.headers.push(
            { key: 'notes', name: 'ملاحظات', sort: true, expend: true },
          )
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
  created() {
    this.loadData()
  },
}
</script>
