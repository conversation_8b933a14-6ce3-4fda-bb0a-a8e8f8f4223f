<template>
  <card :loading="loading" :title="title" body-fit>
    <template #toolbar></template>
    <v-table :data="appeals" :headers="headers" :max-items="30" indexed pagination search>
      <template #toolbar>
        <div v-if="canSave" class="col d-flex align-items-start justify-content-end">
          <v-button class="mr-2" variant="brand" wide @click="submit">حفظ</v-button>
        </div>
      </template>
      <!-- <template slot="td-incomplete" slot-scope="{ row }">
        <b-form-checkbox
          v-model="row.incomplete"
          :disabled="isConfirmed || ['W', 'T'].includes(row.grade)"
          :value="true"
          size="lg"
          @change="changeIncomplete($event, row)"
        />
      </template> -->
      <!-- <template slot="td-mid_mark" slot-scope="{ row }">
        <span v-if="row.status !== 'pending'">{{ row.mid_mark }}</span>
        <template v-else>
          <b-form-input v-model="row.mid_mark" :max="subjectMidMark" class="mark-input" min="0" type="number" />
        </template>
      </template>

      <template slot="td-practical_final_mark" slot-scope="{ row }">
        <span v-if="row.status !== 'pending'">{{ row.practical_final_mark }}</span>
        <b-form-input v-else v-model="row.practical_final_mark" :max="subjectPracticalFinalMark" class="mark-input"
          min="0" type="number" />
      </template> -->
      <template slot="td-final_mark" slot-scope="{ row }">
        <span v-if="row.status !== 'pending'">{{ row.final_mark }}</span>
        <template v-else>
          <b-form-input v-model="row.final_mark" :max="subjectFinalMark" class="mark-input" min="0" type="number" />
        </template>
      </template>
    </v-table>
  </card>
</template>

<script>
import ApiService from '@/common/api.service'

import Card from '@/views/partials/content/Card.vue'
import VTable from '@/components/datatable/VTable.vue'

export default {
  components: { Card, VTable },
  data() {
    return {
      loading: true,
      headers: [
        { key: 'student_id', name: 'رقم القيد', sort: true },
        { key: 'student_name', name: 'اسم الطالب', sort: true, expend: true },
        // { key: 'old_mid_mark', name: 'درجة الأعمال السابقة', sort: true, expend: true },
        { key: 'old_final_mark', name: 'درجة النهائي السابقة', sort: true, expend: true },
        // { key: 'mid_mark', name: 'درجة الاعمال', sort: true, expend: true },
        { key: 'final_mark', name: 'درجة النهائي', sort: true, expend: true },
        // { key: 'full_mark', name: 'المجموع', sort: true, expend: true },
        // { key: 'grade', name: 'التقدير', sort: true, expend: true },
      ],
      subject: null,
      appeals: [],
    }
  },
  computed: {
    subjectFinalMark() {
      return this.subject?.final_mark || 50
    },
    subjectMidMark() {
      return this.subject?.mid_mark || 50
    },
    subjectPracticalFinalMark() {
      return this.subject?.practical_final_mark || 0
    },
    canSave() {
      return this.appeals.some(item => item.status === 'pending')
    },
    title() {
      if (!this.subject) return 'الطعون'
      const subjectTitle = this.$utils.getCurrentOrFirstLang(this.subject.title)
      return `الطعون في ${this.subject.code} ${subjectTitle} (${this.group})`
    },
    subjectId() {
      return this.$route.params.subject_id
    },
    group() {
      return this.$route.query.group
    },
  },
  methods: {

    validateMarkInputs() {
      const inputs = document.querySelectorAll('.mark-input')
      if (!inputs) return true

      let validInputs = 0
      inputs.forEach(input => input.reportValidity() && validInputs++)
      return inputs.length === validInputs
    },
    submit() {
      if (!this.validateMarkInputs()) {
        this.$utils.alert('يجب ألا يتجاوز مجموع الدرجات 100', 'error')
        return
      }

      this.loading = true

      ApiService.put(`lecturer-panel/subjects/${this.subjectId}/appeals`, {
        group: this.group,
        appeals: this.appeals,
      })
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.loadData()
        })
        .catch(error => {
          if (error?.response?.data?.errors) {
            this.$utils.errorHandlerWithValidation(error)
          } else {
            this.$utils.toast(error?.response?.data?.message || this.$t("msg.error"), 'error')
          }
        })
        .finally(() => (this.loading = false))
    },
    loadData() {
      this.loading = true
      ApiService.query(`lecturer-panel/subjects/${this.subjectId}/appeals`, { group: this.group })
        .then(({ data }) => {
          this.subject = data.subject

          // if (this.headers.findIndex(header => header.key === 'practical_final_mark') === -1) {
          //   if (this.subject?.practical_final_mark && this.subject?.practical_final_mark > 0) {
          //     this.headers.splice(3, 0, {
          //       key: 'old_practical_final_mark',
          //       name: 'درجة العملي النهائي السابقة',
          //       sort: true,
          //       expend: true,
          //     })
          //     this.headers.splice(6, 0, {
          //       key: 'practical_final_mark',
          //       name: 'درجة العملي النهائي',
          //       sort: true,
          //       expend: true,
          //     })
          //   }
          // }

          this.appeals = data.appeals?.map(item => ({
            ...item,
            student_id: item.student?.id,
            student_name: item.student?.name,
            mid_mark: item.mid_mark ?? item.old_mid_mark,
            practical_final_mark: item.practical_final_mark ?? item.old_practical_final_mark,
            final_mark: item.final_mark ?? item.old_final_mark,
          })) || []

          // this.students = data.appeals.map(item => {
          //   const fullAr = `${item.first_name_ar} ${item.second_name_ar} ${item.third_name_ar} ${item.last_name_ar}`
          //   const fullEn = `${item.first_name_en} ${item.second_name_en} ${item.third_name_en} ${item.last_name_en}`
          //   return {
          //     ...item,
          //     incomplete: item.grade === 'I',
          //     fullName: this.$lang === 'ar' ? fullAr : fullEn,
          //   }
          // })
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
  created() {
    this.loadData()
  },
}
</script>
