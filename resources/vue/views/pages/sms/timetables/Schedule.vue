<template>
  <card :title="title" :loading="loading">
    <template slot="toolbar">
      <v-button variant="label-success" icon="flaticon2-print" :href="printUrl" target="_blank">
        طباعة
      </v-button>
      <v-button v-if="canAddOrUpdate" @click="openForm()" icon="flaticon2-plus" variant="brand" class="ml-2">
        إضافة محاضرة
      </v-button>
    </template>
    <template slot="body">
      <b-row>
        <!-- <div class="col-lg-4">
          <select-input
            placeholder="نوع البرنامج الدراسي"
            :options="programTypes"
            v-model="filter.programType"
            clearable
          />
        </div> -->
        <div class="col-lg-4">
          <select-input
            placeholder="البرنامج الدراسي"
            :options="programs"
            v-model="filter.program"
            searchable
            clearable
          />
        </div>
        <div class="col-lg-4">
          <select-input placeholder="الأستاذ" :options="lecturers" v-model="filter.lecturer" searchable clearable />
        </div>
        <div class="col-lg-4">
          <select-input placeholder="المبنى" :options="buildings" v-model="filter.building" searchable clearable />
        </div>
        <div class="col-lg-4">
          <select-input placeholder="القاعة" :options="halls" v-model="filter.hall" searchable clearable />
        </div>
        <div class="col-lg-4">
          <v-button v-if="Object.values(filter).some(item => item)" @click="clearFilter" variant="label-brand">
            إلغاء الفرز
          </v-button>
        </div>
      </b-row>
      <timetable
        v-if="timetable.id"
        @cell-click="openForm"
        :data="content"
        :start-time="timetable.start_time"
        :end-time="timetable.end_time"
        :duration="timetable.duration"
        :can-edit="canAddOrUpdate"
        show-program
      />
      <schedule-form
        v-if="timetable.id && canAddOrUpdate"
        :data="lectureData"
        :active.sync="isModalActive"
        :start-time="timetable.start_time"
        :end-time="timetable.end_time"
        :duration="timetable.duration"
        :settings="timetable.settings"
        :study-plan="timetable.term_study_plan"
        @save="save"
        @remove="remove"
      />
    </template>
  </card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import Timetable from '@/components/timetable/Timetable.vue'
import ScheduleForm from '@/views/pages/sms/timetables/ScheduleForm.vue'
import ApiService from '@/common/api.service'
import SelectInput from '@/components/form-controls/SelectInput.vue'

export default {
  name: 'Schedule',
  components: { ScheduleForm, Timetable, Card, SelectInput },
  data() {
    return {
      loading: true,
      isModalActive: false,
      lectureData: {},
      timetable: {},
      filter: {
        programType: null,
        lecturer: null,
        program: null,
        building: null,
        hall: null,
      },
    }
  },
  computed: {
    id() {
      return this.$route.params.timetable_id
    },
    title() {
      const { term_title, year } = this.timetable
      return `جدول دراسي ${this.$t(`terms.seasons.${term_title}`)} ${year}`
    },
    lecturers() {
      return this.$store.getters['sms/lecturers']
    },
    buildings() {
      return this.$store.getters['sms/buildings'].map(building => ({
        id: building.id,
        title: this.$utils.getCurrentOrFirstLang(building.title),
        halls: building.halls,
      }))
    },
    halls() {
      return (
        // this.buildings
        //   .find(item => item.id === this.filter.building)
        //   ?.halls.map(item => ({ id: item.id, title: this.$utils.getCurrentOrFirstLang(item.title) })) || []
        this.buildings
          .filter(item => (this.filter.building ? item.id === this.filter.building : true))
          .reduce(
            (acc, building) => [
              ...acc,
              ...building.halls.map(item => ({
                id: item.id,
                title: this.$utils.getCurrentOrFirstLang(item.title),
              })),
            ],
            []
          )
      )
    },
    programs() {
      return this.$store.getters.programsOptions
    },
    canAddOrUpdate() {
      if (!this.$acl.middleware('department')) return false
      const { term_status } = this.timetable
      return (
        (this.$acl.middleware('department') && (term_status === 'pending' || term_status === 'active')) ||
        this.$acl.has('superadmin')
      )
    },
    content() {
      return this.timetable.content
        ? this.timetable.content.filter(item => {
            let flag = true
            if (this.filter.programType) flag = item?.program?.type === this.filter.programType
            if (this.filter.program) flag = item?.program_id === this.filter.program
            if (this.filter.lecturer) flag = item?.lecturer_id === this.filter.lecturer
            if (this.filter.building) flag = item?.hall?.building_id === this.filter.building
            if (this.filter.hall) flag = item?.hall_id === this.filter.hall
            return flag
          })
        : []
    },
    printUrl() {
      const search = Object.entries(this.filter)
        .reduce((acc, [key, value]) => {
          if (value) acc.push(`${key}=${value}`)
          return acc
        }, [])
        .join('&')
      return `/sms/reports/timetables/${this.id}/content${search ? '?' : ''}${search}`
    },
  },
  methods: {
    openForm(id = null) {
      let data = id ? this.timetable.content.find(f => f.id === id) : {}
      this.lectureData = JSON.parse(JSON.stringify(data))
      this.isModalActive = true
    },
    save(data) {
      const index = this.timetable.content.findIndex(item => item.id === data.id)
      index !== -1 ? this.$set(this.timetable.content, index, data) : this.timetable.content.push(data)
    },
    remove(id) {
      const index = this.timetable.content.findIndex(i => i.id === id)
      this.timetable.content.splice(index, 1)
    },
    clearFilter() {
      this.filter = {
        programType: null,
        lecturer: null,
        program: null,
        building: null,
        hall: null,
      }
    },
  },
  created() {
    Promise.all([
      this.$store.dispatch('sms/getLecturers'),
      this.$store.dispatch('sms/getBuildings'),
      this.$store.dispatch('getPrograms'),
      ApiService.query(`timetables/${this.id}/content`).then(({ data }) => {
        this.timetable = data
      }),
    ])
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
