<template>
  <div>
    <card :title="`إعدادات الفصل الدراسي ${title}`" class="mb-4" :loading="pageLoading">
      <form-validation @submit="onSubmit" :loading="loading">
        <h5 class="text-dark mb-4">إعدادات الجدول الدراسي</h5>
        <b-row>
          <timepicker-input label-cols-lg="2" class="col-lg-8 col-12" label="وقت البداية:" name="start_time"
            rules="required" v-model="form.timetable_settings.start_time" />
        </b-row>
        <b-row>
          <timepicker-input label-cols-lg="2" class="col-lg-8 col-12" label="وقت النهاية:" name="end_time"
            rules="required" v-model="form.timetable_settings.end_time" />
        </b-row>
        <b-row>
          <text-input label-cols-lg="2" class="col-lg-8 col-12" label="مدة اقصر فترة:" name="duration" append="دقيقة"
            rules="required|numeric" v-model="form.timetable_settings.duration" />
        </b-row>
        <!-- <b-row>
          <div class="row col-lg-8 col-12 my-2">
            <check-box-input
              class="offset-lg-2"
              v-model="form.settings.only_private_halls"
              name="only_private_halls"
              size="lg"
            >
              عدم السماح باختيار القاعات من كليات اخرى
            </check-box-input>
          </div>
        </b-row> -->
        <hr />
        <h5 class="text-dark my-4">إعدادات تنزيل المواد</h5>
        <b-row>
          <text-input label-cols-lg="2" class="col-lg-8 col-12" label="اقصى عدد الوحدات:"
            description="اقصى عدد الوحدات هي الوحدات الدراسية التي يستطيع الطالب تنزيلها" rules="required|numeric"
            v-model="form.settings.enrollment_subjects" />
        </b-row>
        <div class="d-flex justify-content-end mt-4">
          <v-button class="px-4 mr-2" to="../">{{ $t('form.back') }}</v-button>
          <v-button variant="success" type="submit">{{ $t('form.save_changes') }}</v-button>
        </div>
      </form-validation>
    </card>
    <!-- <card title="إعدادات اخرى">
      <b-row>
        <div class="col-lg-2">
          <label>حذف الطلبة الذين لم يكملوا عملية الدفع من المواد الدراسية</label>
        </div>
        <div class="col-lg-8">
          <v-button variant="label-danger" @click="removeUnpaidStudents">حذف الطلبة</v-button>
        </div>
      </b-row>
    </card> -->
  </div>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import FormValidation from '@/components/form-controls/FormValidation.vue'
import TimepickerInput from '@/components/form-controls/TimepickerInput.vue'
import TextInput from '@/components/form-controls/TextInput.vue'
import ApiService from '@/common/api.service'
import CheckBoxInput from '@/components/form-controls/CheckBoxInput.vue'

export default {
  components: { Card, FormValidation, TimepickerInput, TextInput, CheckBoxInput },
  data() {
    return {
      pageLoading: true,
      loading: false,
      title: '',
      creditsOptions: [
        { id: '1', title: 'وحدة' },
        { id: '2', title: 'وحدتين' },
        { id: '3', title: '3 وحدات' },
      ],
      form: {
        timetable_settings: {
          start_time: null,
          end_time: null,
          duration: null,
        },
        settings: {
          // enrollment_subjects: 1,
          only_private_halls: true,
        },
      },
    }
  },
  computed: {
    id() {
      return this.$route.params.term_id
    },
  },
  methods: {
    async removeUnpaidStudents() {
      const msg = `
        <p>
          هل انت متأكد بأنك تريد حذف الطلبة الذين لم يكملوا عملية الدفع من المواد الدراسية؟
          <br /> <br />
          <b class="text-danger">لن يمكنك الترجع عن هذه العملية بعد اجراءها.</b>
          <br /> <br />
        </p>
      `

      const { value } = await this.$swal({
        title: 'تأكيد',
        html: msg,
        icon: 'warning',
        input: 'password',
        inputPlaceholder: 'أدخل كلمة المرور',
        inputAttributes: { autocapitalize: 'off', autocorrect: 'off', required: true },
        showCancelButton: true,
        confirmButtonText: 'نعم متأكد!',
        cancelButtonText: 'رجوع',
      })
      if (!value) return
      ApiService.post(`terms/${this.id}/delete-none-paid-students`, { password: value })
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(err => {
          if (err?.response?.status === 422) {
            this.$utils.alert(Object.values(err?.response?.data?.errors)[0][0], 'error')
          } else {
            this.$utils.alert(this.$t('msg.error'), 'error')
          }
        })
    },
    onSubmit() {
      this.loading = true
      ApiService.put(`terms/${this.id}/settings`, this.form)
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
  },
  created() {
    ApiService.query(`terms/${this.id}/settings`)
      .then(({ data }) => {
        this.form.timetable_settings = data.timetable_settings
        this.form.settings = data.settings
        this.title = `${this.$t(`terms.seasons.${data.title}`)} ${data.year}`
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.pageLoading = false))
  },
}
</script>

<style lang="scss"></style>
