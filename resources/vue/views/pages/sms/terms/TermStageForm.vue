<template>
  <b-modal
    ref="modal"
    :title="form.id ? $t('terms.stages.edit') : $t('terms.stages.add')"
    centered
    hide-footer
    size="lg"
    @hide="close"
  >
    <form-validation :loading="loading" @submit="onSubmit">
      <b-row>
        <select-input
          class="col-12"
          label="المرحلة"
          rules="required"
          name="stage_id"
          v-model="form.stage_id"
          :disabled="!!form.id"
          :options="stages"
        />
        <datepicker-input
          class="col-lg-6"
          rules="required"
          name="start_at"
          type="datetime"
          :label="$t('events.start_date')"
          v-model="form.start_at"
        />
        <datepicker-input
          class="col-lg-6"
          rules="required"
          name="end_at"
          type="datetime"
          :label="$t('events.end_date')"
          v-model="form.end_at"
        />
        <radio-input-group
          label="وضع المرحلة"
          class="col-12"
          rules="required"
          name="status"
          :options="statuses"
          v-model="form.status"
        />
      </b-row>
      <hr />
      <div class="mt-4 d-flex justify-content-end">
        <v-button class="px-4 mr-2" variant="clean" bold @click="close">{{ $t('form.back') }}</v-button>
        <v-button type="submit" variant="brand" wide :loading="loading">{{ $t('form.save') }}</v-button>
      </div>
    </form-validation>
  </b-modal>
</template>

<script>
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation.vue'
import SelectInput from '@/components/form-controls/SelectInput.vue'
import DatepickerInput from '@/components/form-controls/DatepickerInput.vue'
import RadioInputGroup from '@/components/form-controls/RadioInputGroup.vue'

export default {
  name: 'TermStageForm',
  components: { FormValidation, SelectInput, DatepickerInput, RadioInputGroup },
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      required: true,
    },
  },
  watch: {
    active(val) {
      if (!val) return
      this.$refs.modal.show()
      this.form = this.initValues()
      Object.assign(this.form, this.data)
    },
  },
  data() {
    return {
      form: {},
      loading: false,
      statuses: [
        { value: 'auto', text: this.$t('terms.stages.statuses.auto') },
        { value: 'active', text: this.$t('terms.stages.statuses.active') },
        { value: 'suspended', text: this.$t('terms.stages.statuses.suspended') },
      ],
    }
  },
  computed: {
    stages() {
      return this.$store.getters.termStages
    },
    termId() {
      return this.$route.params.term_id
    },
  },
  methods: {
    initValues() {
      return {
        stage_id: null,
        start_at: null,
        end_at: null,
        status: 'auto',
      }
    },
    onSubmit() {
      this.loading = true
      this.request()
        .then(({ data }) => {
          this.$emit('save', { ...data.stage })
          this.close()
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(({ response }) => {
          this.$utils.alert(
            response?.data?.errors ? Object.values(response.data.errors)[0][0] : this.$t('msg.error'),
            'error'
          )
        })
        .finally(() => (this.loading = false))
    },
    request() {
      const request = `terms/${this.termId}/stages`

      if (!this.data.id) {
        return ApiService.post(request, this.form)
      } else {
        return ApiService.put(`${request}/${this.data.id}`, this.form)
      }
    },
    close() {
      this.$refs.modal.hide()
      this.$emit('update:active', false)
    },
  },
}
</script>
