<template>
  <card :loading="loading" body-fit title="السجل الدراسي">
    <!-- <template #toolbar>
      <b-dropdown text="طباعة" variant="label-success">
        <b-dropdown-item :href="`/sms/transcript`">
          كشف الدرجات
        </b-dropdown-item>
      </b-dropdown>
    </template> -->
    <template slot="body">
      <v-table :data="datatable" :headers="headers" indexed pagination search>
        <template #td-status="{ row }">
          {{ $t('student_status.' + row.status) }}
        </template>
        <template #td-actions="{ row }">
          <v-button v-if="row.term_status === 'active' && $acl.isStageActive('enrollment') && canEnroll"
            :to="{ name: 'enrollment', params: { term_id: row.id } }" variant="label-brand">
            تنزيل المواد
          </v-button>
          <v-button v-else :to="{ name: 'academic-record-subjects', params: { term_id: row.id } }"
            variant="label-brand">
            عرض المواد الدراسية
          </v-button>
        </template>
      </v-table>
    </template>
  </card>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import { mapState } from 'vuex'

export default {
  name: 'AcademicRecord',
  components: { Card, VTable },
  data() {
    return {
      headers: [
        { key: 'term', name: 'الفصل', sort: true },
        { key: 'status', name: 'حالة التسجيل', sort: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      loading: true,
      terms: [],
    }
  },
  computed: {
    ...mapState({
      noticesCount: state => state.student.noticesCount,
      canEnroll: state => state.student.canEnroll,
    }),
    datatable() {
      return this.terms.map(item => ({
        id: item.id,
        term: this.$t(`terms.seasons.${item.term_title}`) + ' ' + item.year,
        status: item.student_status,
        term_status: item.term_status,
      }))
    },
  },
  created() {
    ApiService.query('sms/student-terms')
      .then(({ data }) => {
        this.terms = data
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
