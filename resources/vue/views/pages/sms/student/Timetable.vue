<template>
  <card :title="title" :loading="loading">
    <p class="font-weight-bold text-dark mb-4">
      * المواد التي تظهر في الجدول امامك ليست جدول القسم بالكامل انما هي فقط المواد المتاحة لك.
    </p>
    <b-row>
      <div class="col-lg-4">
        <select-input
          placeholder="فرز بالمادة الدراسية"
          :options="subjects"
          v-model="selectedSubject"
          searchable
          clearable
        />
      </div>
    </b-row>
    <timetable
      v-if="timetable.id"
      :data="content"
      :start-time="timetable.start_time"
      :end-time="timetable.end_time"
      :duration="timetable.duration"
    />
  </card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import Timetable from '@/components/timetable/Timetable.vue'
import ApiService from '@/common/api.service'
import SelectInput from '@/components/form-controls/SelectInput.vue'

export default {
  name: 'Schedule',
  components: { Timetable, Card, SelectInput },
  data() {
    return {
      loading: true,
      timetable: {},
      subjects: [],
      selectedSubject: null,
    }
  },
  computed: {
    title() {
      const { term_title, year } = this.timetable
      return `جدول دراسي ${this.$t(`terms.seasons.${term_title}`)} ${year}`
    },
    content() {
      return (
        this.timetable.content.filter(lecture =>
          this.selectedSubject ? this.selectedSubject === lecture.subject.id : true
        ) || []
      )
    },
  },
  created() {
    ApiService.query('sms/timetable')
      .then(({ data }) => {
        this.timetable = data
        this.subjects = this.timetable.content.reduce((subjects, lecture) => {
          const subject = subjects.find(i => i.code == lecture.subject.code)
          if (!subject) {
            subjects.push({
              id: lecture.subject.id,
              title: `${lecture.subject.code} - ${this.$utils.getCurrentOrFirstLang(lecture.subject.title)}`,
            })
          }
          return subjects
        }, [])
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
