<template>
  <div>
    <card :loading="loading">
      <template #body>
        <b-tabs content-class="mt-3" nav-class="nav-tabs-line nav-tabs-bold nav-tabs-line-brand">
          <b-tab active>
            <template #title>
              <i class="flaticon-edit-1"></i>
              <span>تنزيل المواد الدراسية</span>
            </template>
            <v-table :data="datatable" :headers="headers" indexed search>
              <template slot="td-groups" slot-scope="{ row }">
                <b-form-select
                  v-model="row.selectedGroup"
                  :disabled="row.isEnrolled"
                  :options="row.groups"
                  size="sm"
                ></b-form-select>
              </template>
              <template slot="td-remaining" slot-scope="{ row }">
                {{ row.remaining[row.selectedGroup] }}
              </template>
              <template v-if="!row.isPaid" slot="td-actions" slot-scope="{ row }">
                <v-button v-if="row.isEnrolled" variant="label-danger" @click="unenroll(row)">الغاء</v-button>
                <v-button v-else variant="brand" @click="enroll(row)">تنزيل</v-button>
              </template>
            </v-table>
          </b-tab>
          <b-tab>
            <template #title>
              <i class="flaticon-calendar-with-a-clock-time-tools"></i>
              <span>الجدول الدراسي</span>
            </template>

            <timetable
              v-if="timetable.id"
              :data="timetable.content"
              :duration="timetable.duration"
              :end-time="timetable.end_time"
              :start-time="timetable.start_time"
            />
          </b-tab>
        </b-tabs>
      </template>
    </card>
    <card :loading="loading">
      <template #body>
        <b-tabs content-class="mt-3" nav-class="nav-tabs-line nav-tabs-bold nav-tabs-line-brand">
          <b-tab active>
            <template #title>
              <i class="flaticon-edit-1"></i>
              <span>المواد الدراسية المسجلة</span>
            </template>
            <!-- <v-table :headers="enrolledSubjectsHeader" :data="enrolledSubjects" indexed /> -->
            <timetable
              v-if="timetable.id"
              :data="enrolledTimetable"
              :duration="timetable.duration"
              :end-time="timetable.end_time"
              :start-time="timetable.start_time"
            />
          </b-tab>
        </b-tabs>
      </template>
    </card>
  </div>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import Timetable from '@/components/timetable/Timetable.vue'

export default {
  name: 'Enrollment',
  components: {Card, VTable, Timetable},
  data() {
    return {
      headers: [
        {key: 'code', name: 'الرمز', sort: true},
        {key: 'title', name: 'اسم المادة', sort: true},
        {key: 'groups', name: 'المجموعة'},
        {key: 'remaining', name: 'السعة المتبقية'},
        {key: 'actions', name: this.$t('table.actions')},
      ],
      // enrolledSubjectsHeader: [
      //   { key: 'subject_code', name: 'الرمز', sort: true },
      //   { key: 'subject_name', name: 'اسم المادة', sort: true },
      //   { key: 'group', name: 'المجموعة' },
      // ],
      loading: true,
      timetable: {},
      enrolledSubjects: [],
    }
  },
  computed: {
    termId() {
      return this.$route.params.term_id
    },
    enrolledTimetable() {
      return this.timetable.content?.filter(lect =>
        this.enrolledSubjects.some(sub => sub.id === lect.subject_id && sub.group === lect.group)
      )
    },
    datatable() {
      return this.timetable.content?.reduce((subjects, lecture) => {
        const subject = subjects.find(i => i.code == lecture.subject.code)
        if (!subject) {
          const enrolledSubject = this.enrolledSubjects.find(item => item.id === lecture.subject.id)
          subjects.push({
            id: lecture.subject.id,
            code: lecture.subject.code,
            title: this.$utils.getCurrentOrFirstLang(lecture.subject.title),
            groups: [lecture.group],
            remaining: {[lecture.group]: lecture.capacity - lecture.enrolled},
            selectedGroup: enrolledSubject?.group || lecture.group,
            isEnrolled: !!enrolledSubject,
            isPaid: enrolledSubject?.is_paid,
          })
        } else if (!subject.groups.includes(lecture.group)) {
          subject.groups.push(lecture.group)
          subject.remaining[lecture.group] = lecture.capacity - lecture.enrolled
        }
        return subjects
      }, [])
    },
  },
  methods: {
    enroll(subject) {
      const data = {subject_id: subject.id, group: subject.selectedGroup}
      this.loading = true
      ApiService.post('sms/enrollment', data)
        .then(({data}) => {
          if (data.message === 'success') {
            this.enrolledSubjects.push(data.subject)
            this.$utils.alert('تم تنزيل المادة الدراسية بنجاح', 'success')
          } else {
            this.$utils.alert(data.message, 'error')
          }
        })
        .catch(({response: {data}}) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
    async unenroll(subject) {
      const {id, title} = subject
      const {value} = await this.$utils.confirm(`هل انت متأكد بأنك تريد الغاء المادة ${title}؟`)
      if (!value) return
      ApiService.delete('sms/enrollment', id)
        .then(() => {
          const index = this.enrolledSubjects.findIndex(item => item.id === id)
          this.enrolledSubjects.splice(index, 1)
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(() => {
          this.$utils.alert(this.$t('msg.error'), 'error')
        })
    },
  },
  async created() {
    try {
      await ApiService.query(`sms/student-terms/${this.termId}/subjects`).then(({data}) => {
        this.enrolledSubjects = data.subjects
      })
      await ApiService.query('sms/timetable').then(({data}) => {
        this.timetable = data
      })
    } catch (e) {
      this.$utils.toast('حدث خطأ في جلب البيانات، الرجاء إعادة المحاولة!', 'error')
    }

    this.loading = false
  },
}
</script>
