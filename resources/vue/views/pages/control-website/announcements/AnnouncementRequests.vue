<template>
  <Card
    :title="$t('announcements.manage') + ' - ' + $t('announcements.publish_requests')"
    title-icon="fas fa-bullhorn"
    body-fit
    :loading="loading"
  >
    <template slot="body">
      <v-table
        :headers="headers"
        :data="datatable"
        sst
        pagination
        search
        :total="total"
        :max-items="maxItems"
        @change-page="changePage"
        @sort="changeSort"
        @search="changeSearch"
        @max-items="changeMaxItem"
      >
        <template slot="td-approved" slot-scope="{ row }">
          <b-badge :variant="row.approved ? 'success' : 'primary'" pill>
            {{ row.approved ? $t('news.published') : $t('news.not_published') }}
          </b-badge>
        </template>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button
              v-if="$acl.can('create-announcements')"
              @click="changeStatus(row)"
              :variant="row.approved ? 'label-warning' : 'label-success'"
              :icon="row.approved ? 'la la-remove' : 'la la-check'"
            >
              {{ $tc('announcements.change_status', row.approved) }}
            </v-button>
            <v-button
              v-if="$acl.can('update-announcements')"
              :to="{
                name: 'editAnnouncement',
                params: { announcement_id: row.slug },
              }"
              class="ml-2"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button
              v-if="$acl.can('delete-announcements')"
              @click="remove(row.slug)"
              class="ml-2"
              icon="la la-trash"
              variant="label-danger"
              icon-only
            />
          </div>
        </template>
      </v-table>
    </template>
  </Card>
</template>

<script>
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import ApiService from '@/common/api.service'
import PaginationMixin from '@/mixins/PaginationMixin'

export default {
  name: 'Announcements',
  components: { Card, VTable },
  mixins: [PaginationMixin],
  data() {
    return {
      loading: true,
      headers: [
        { key: 'title', name: this.$t('announcements.title'), sort: true },
        { key: 'targets', name: this.$t('announcements.targets'), expend: true },
        { key: 'entity', name: this.$t('announcements.entity'), expend: true },
        { key: 'approved', name: this.$t('news.status'), sort: true, expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      announcements: [],
    }
  },
  computed: {
    datatable() {
      return this.announcements.map(a => {
        return {
          id: a.id,
          slug: a.slug,
          title: this.$utils.getCurrentOrFirstLang(a.title),
          targets: this.$t(`targets.${a.targets}`),
          approved: a.approved,
          entity: this.$utils.getCurrentOrFirstLang(a.from_entity),
        }
      })
    },
  },
  methods: {
    changeStatus(data) {
      const status = data.approved ? 0 : 1
      const msg = this.$tc('announcements.change_status_confirm', data.approved, {
        entity: this.$store.getters.entity.title,
      })
      this.$utils.confirm(msg).then(({ value }) => {
        if (!value) return
        ApiService.put(`announcements/${data.slug}/change-status`, { approved: status })
          .then(() => (data.approved = status))
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
      })
    },
    remove(slug) {
      this.$utils.confirm(this.$t('announcements.confirm_delete')).then(({ value }) => {
        if (!value) return
        this.pageLoading = true
        ApiService.delete('announcements', slug)
          .then(() => {
            const index = this.announcements.findIndex(data => data.slug === slug)
            this.announcements.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
          .finally(() => (this.pageLoading = false))
      })
    },
    loadData() {
      this.loading = true
      ApiService.query('announcements', { query_type: 'requests_only', ...this.filterData })
        .then(({ data }) => {
          this.total = data.meta.total
          this.currentPage = data.meta.current_page
          this.announcements = data.data
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
}
</script>
