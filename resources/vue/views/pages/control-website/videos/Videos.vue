<template>
  <Card :title="$t('videos.manage')" title-icon="fas fa-video" body-fit :loading="loading">
    <template slot="toolbar">
      <v-button v-if="$acl.can('create-videos')" @click="openForm()" variant="brand" icon="flaticon2-plus">
        {{ $t('videos.add') }}
      </v-button>
    </template>
    <template slot="body">
      <v-table
        :headers="headers"
        :data="datatable"
        sst
        pagination
        search
        :total="total"
        :max-items="maxItems"
        @change-page="changePage"
        @sort="changeSort"
        @search="changeSearch"
        @max-items="changeMaxItem"
      >
        <template slot="td-link" slot-scope="{ row }">
          <a :href="row.link">{{ row.link }}</a>
        </template>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button
              v-if="$acl.can('update-videos')"
              @click="openForm(row.id)"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button
              v-if="$acl.can('delete-videos')"
              @click="remove(row.id)"
              class="ml-2"
              icon="la la-trash"
              variant="label-danger"
              icon-only
            />
          </div>
        </template>
      </v-table>
      <video-form
        v-if="$acl.can('create-videos|update-videos')"
        :data="videoData"
        :active.sync="isModalActive"
        @save="save"
      />
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import VideoForm from '@/views/pages/control-website/videos/VideoForm'
import PaginationMixin from '@/mixins/PaginationMixin'

export default {
  name: 'Videos',
  components: { VideoForm, Card, VTable },
  mixins: [PaginationMixin],
  data() {
    return {
      loading: true,
      isModalActive: false,
      headers: [
        { key: 'title', name: this.$t('videos.title'), sort: true },
        { key: 'link', name: this.$t('videos.link'), sort: true, expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      videoData: {},
      videos: [],
    }
  },
  computed: {
    datatable() {
      return this.videos.map(v => {
        return {
          id: v.id,
          title: this.$utils.getCurrentOrFirstLang(v.title),
          link: v.link,
        }
      })
    },
  },
  methods: {
    openForm(id = -1) {
      let data = id === -1 ? {} : this.videos.find(f => f.id === id)
      this.videoData = JSON.parse(JSON.stringify(data))
      this.isModalActive = true
    },
    save(data) {
      const index = this.videos.findIndex(video => video.id === data.id)
      index !== -1 ? this.$set(this.videos, index, data) : this.videos.push(data)
    },
    remove(id) {
      this.$utils.confirm(this.$t('videos.confirm_delete')).then(({ value }) => {
        if (!value) return
        ApiService.delete('videos', id)
          .then(() => {
            const index = this.videos.findIndex(video => video.id === id)
            this.videos.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
      })
    },
    loadData() {
      this.loading = true
      ApiService.query('videos', this.filterData)
        .then(({ data }) => {
          this.total = data.meta.total
          this.currentPage = data.meta.current_page
          this.videos = data.data
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
}
</script>
