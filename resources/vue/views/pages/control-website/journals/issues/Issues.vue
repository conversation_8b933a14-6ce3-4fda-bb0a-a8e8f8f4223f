<template>
  <Card :title="$t('issues.manage')" title-icon="fa fa-hashtag" body-fit :loading="loading">
    <template slot="toolbar">
      <v-button :to="{ name: 'newIssue' }" variant="brand" icon="flaticon2-plus">
        {{ $t('issues.add') }}
      </v-button>
    </template>
    <template slot="body">
      <image-viewer v-if="viewImageSrc" :viewImageSrc="viewImageSrc" @close="viewImageSrc = null" />
      <v-table :headers="headers" :data="datatable" pagination search>
        <template slot="td-cover" slot-scope="{ row }">
          <div class="td-cover">
            <b-img @click="viewImageSrc = row.cover" thumbnail :src="row.cover" :alt="`${row.issue}`" />
          </div>
        </template>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button :to="{ name: 'issueArticles', params: { issue_id: row.id } }" variant="label-brand">
              {{ $t('issues.view_articles') }}
            </v-button>
            <v-button
              :to="{ name: 'editIssue', params: { issue_id: row.id } }"
              class="ml-2"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button @click="remove(row.id)" class="ml-2" icon="la la-trash" variant="label-danger" icon-only />
          </div>
        </template>
      </v-table>
    </template>
  </Card>
</template>

<script>
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import ApiService from '@/common/api.service'
import ImageViewer from '@/components/ImageViewer'

export default {
  name: 'Issues',
  components: { ImageViewer, Card, VTable },
  data() {
    return {
      viewImageSrc: null,
      headers: [
        { key: 'cover', name: this.$t('issues.cover'), expend: true },
        { key: 'issue', name: this.$t('issues.issue'), sort: true },
        { key: 'volume', name: this.$t('issues.volume'), sort: true },
        { key: 'month', name: this.$t('issues.month'), sort: true, expend: true },
        { key: 'year', name: this.$t('issues.year'), sort: true, expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      loading: true,
      issues: [],
    }
  },
  computed: {
    datatable() {
      return this.issues
    },
  },
  methods: {
    remove(id) {
      this.$utils.confirm(this.$t('issues.confirm_delete')).then(({ value }) => {
        if (!value) return
        this.loading = true
        ApiService.delete('issues', id)
          .then(() => {
            const index = this.issues.findIndex(data => data.id === id)
            this.issues.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(({ response: { data } }) => {
            this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
          })
          .finally(() => (this.loading = false))
      })
    },
  },
  created() {
    ApiService.query('issues')
      .then(({ data }) => (this.issues = data))
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>

<style>
.td-cover {
  width: 80px;
  cursor: pointer;
}
</style>
