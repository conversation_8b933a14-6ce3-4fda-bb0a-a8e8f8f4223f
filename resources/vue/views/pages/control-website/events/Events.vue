<template>
  <Card :title="$t('events.manage')" title-icon="flaticon-event-calendar-symbol" body-fit :loading="loading">
    <template slot="toolbar">
      <v-button v-if="$acl.can('create-events')" :to="{ name: 'newEvent' }" variant="brand" icon="flaticon2-plus">
        {{ $t('events.add') }}
      </v-button>
    </template>
    <template slot="body">
      <v-table
        :headers="headers"
        :data="datatable"
        sst
        pagination
        search
        :total="total"
        :max-items="maxItems"
        @change-page="changePage"
        @sort="changeSort"
        @search="changeSearch"
        @max-items="changeMaxItem"
      >
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button
              v-if="$acl.can('update-events')"
              :to="{
                name: 'editEvent',
                params: { event_id: row.slug },
              }"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button
              v-if="$acl.can('delete-events')"
              @click="remove(row.slug)"
              class="ml-2"
              icon="la la-trash"
              variant="label-danger"
              icon-only
            />
          </div>
        </template>
      </v-table>
    </template>
  </Card>
</template>

<script>
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import ApiService from '@/common/api.service'
import PaginationMixin from '@/mixins/PaginationMixin'

export default {
  name: 'Events',
  components: { Card, VTable },
  mixins: [PaginationMixin],
  data() {
    return {
      loading: true,
      headers: [
        { key: 'title', name: this.$t('events.title'), sort: true },
        { key: 'category', name: this.$t('events.type'), sort: true, expend: true },
        { key: 'start_time', name: this.$t('events.time'), sort: true, expend: true },
        { key: 'start_date', name: this.$t('events.date'), sort: true, expend: true },
        { key: 'place', name: this.$t('events.place'), sort: true, expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      events: [],
    }
  },
  computed: {
    datatable() {
      return this.events.map(e => {
        return {
          slug: e.slug,
          title: this.$utils.getCurrentOrFirstLang(e.title),
          category: this.categories.find(cat => cat.id === e.category_id).title,
          start_time: e.start_time,
          start_date: e.start_date,
          place: this.$utils.getCurrentOrFirstLang(e.place),
        }
      })
    },
    categories() {
      return this.$store.getters.eventCategories
    },
  },
  methods: {
    remove(slug) {
      this.$utils.confirm(this.$t('events.confirm_delete')).then(({ value }) => {
        if (!value) return
        this.pageLoading = true
        ApiService.delete('events', slug)
          .then(() => {
            const index = this.events.findIndex(data => data.slug === slug)
            this.events.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
          .finally(() => (this.pageLoading = false))
      })
    },
    loadData() {
      this.loading = true
      ApiService.query('events', this.filterData)
        .then(({ data }) => {
          this.total = data.meta.total
          this.currentPage = data.meta.current_page
          this.events = data.data
        })
        .catch(this.$utils.errorHandler)
        .finally(() => (this.loading = false))
    },
  },
  created() {
    Promise.all([this.$store.dispatch('getEventCategories')])
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
