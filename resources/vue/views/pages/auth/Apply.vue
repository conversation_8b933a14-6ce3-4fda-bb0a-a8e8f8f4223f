<template>
  <div class="container">
    <template v-if="!registerComplete">
      <card>
        <a href="#" class="kt-login__logo d-flex">
          <img src="/assets/logo.png" class="mx-auto" width="140px" />
        </a>
        <h2 class="text-dark text-center mt-5">
          نموذج تسجيل طلب الألتحاق بجامعة غريان
        </h2>
        <div class="mt-4 d-flex justify-content-end align-items-center">
          <!-- <v-button class="mr-2" variant="label-brand" :to="'/update-application-data'">
            طلب تعديل بيانات التسجيل
          </v-button> -->
          <v-button variant="label-brand" to="/request-application-receipt">طلب نموذج التسجيل</v-button>
        </div>
      </card>
      <ApplyForm @complete="complete" />
    </template>
    <b-jumbotron v-else class="shadow" bg-variant="white">
      <template slot="lead">
        <h1>تم استلام طلبك بنجاح</h1>
        <p class="my-4">
          سوف يتم تقييم الطلب واعلامكم عن طريق البريد الالكتروني.
          <br />
          يمكنكم مراجعة ادارة المسجل العام لمعرفة نتيجة تقييم الطلب باستخدام البيانات التالية:
          <br />
          رقم التسجيل: <b>{{ student.registration_number }}</b>
          <br />
          البريد الالكتروني: <b>{{ student.email }}</b>
        </p>

        <p class="my-4">
          يرجى تاكيد البريد الالكتروني، ستصلك رسالة تأكيد علي بريدك الإلكتروني قريباً، الرجاء التحقق من Spam أو Junk
          Folder.
          <br />
          بعد ذلك ستصلك رسالة على بريدك الإلكتروني تحتوي على نموذج التسجيل، كما يرجى طباعة نموذج التسجيل و تقديمه مع
          المستندات المطلوبة.
        </p>
        <p class="my-4">
          إذا لم يصلك إيصال نموذج التسجيل يمكنك طلبه عبر صفحة
          <a href="/request-application-receipt" target="_blank">طلب إيصال نموذج التسجيل</a>.
        </p>
      </template>
      <b-button variant="primary" href="/" class="ml-auto">الصفحة الرئيسية</b-button>
    </b-jumbotron>
  </div>
</template>

<script>
import ApplyForm from './ApplyForm.vue'
import Card from '@/views/partials/content/Card.vue'

export default {
  name: 'apply',
  components: { ApplyForm, Card },
  data() {
    return {
      student: {},
      registerComplete: false,
    }
  },
  methods: {
    complete(student) {
      this.student = student || {}
      this.registerComplete = true
    },
  },
}
</script>
