<template>
  <div>
    <!--begin::Body-->
    <b-row v-if="!valid" class="justify-content-md-center">
      <div class="kt-login__body bg-white shadow kt-rounded p-4 m-0 col-xl-4 col-12">
        <!--begin::Signin-->
        <div class="kt-login__form">
          <a href="#" class="kt-login__logo d-flex">
            <img src="/assets/logo.png" class="mx-auto" width="140px" />
          </a>
          <h2 class="text-center text-dark mt-5">جامعة غريان</h2>
          <h5 class="text-center text-muted mt-3">طلب تعديل على نموذج التسجيل</h5>
          <!--begin::Form-->
          <FormValidation class="login" @submit="onSubmit" :loading="loading">
            <TextInput
              rules="required"
              label="الرقم الوطني أو جواز السفر"
              label-class="font-weight-bold text-dark"
              name="username"
              v-model.trim="credentials.username"
            />
            <TextInput
              rules="required|digits:9"
              label="رقم الهاتف"
              label-class="font-weight-bold text-dark"
              name="phone_number"
              description="قم بإدخال رقم الهاتف التي قمت بتسجيل به، يجب ان يكون رقم الهاتف على صيغة 9XXXXXXXX"
              v-model.trim="credentials.phone_number"
            />

            <b-alert :show="!!error" dismissible @dismissed="error = null" variant="danger" class="mb-0 mt-4">
              {{ error }}
            </b-alert>

            <div class="kt-login__actions">
              <v-button type="submit" wide size="lg" variant="brand" :loading="loading">إرسال</v-button>
            </div>

            <p>
              إذا كنت مسجل ولم تستطع تعديل بياناتك يرجى التواصل على البريد الإلكتروني <EMAIL>
            </p>
          </FormValidation>
        </div>
        <!--end::Signin-->
      </div>
    </b-row>
    <div class="container" v-else-if="valid && !registerComplete">
      <card>
        <img src="/assets/logo.png" class="mx-auto" width="140px" />
        <h2 class="text-dark text-center mt-5">
          نموذج تعديل بيانات التسجيل
        </h2>
      </card>
      <ApplyForm
        :editMode="true"
        :student="student"
        :credentials="credentials"
        @complete="() => (registerComplete = true)"
      />
    </div>
    <b-jumbotron v-else class="shadow" bg-variant="white">
      <template slot="lead">
        <h1>لقد قمت بتعديل بياناتك بنجاح</h1>
        <p class="my-4">
          إذا لم تقم بتأكيد بريدك الإلكتروني الرجاء تأكيده حتى يصلك نموذج التسجيل
        </p>
        <p class="my-4">
          إذا لم يصلك إيصال نموذج التسجيل يمكنك طلبه عبر صفحة
          <a href="/request-application-receipt" target="_blank">طلب إيصال نموذج التسجيل</a>.
        </p>
      </template>
      <b-button variant="primary" href="/" class="ml-auto">الصفحة الرئيسية</b-button>
    </b-jumbotron>
    <!--end::Body-->
  </div>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput'
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation'
import ApplyForm from './ApplyForm.vue'
import Card from '@/views/partials/content/Card.vue'

export default {
  components: { FormValidation, TextInput, ApplyForm, Card },
  name: 'login',
  data() {
    return {
      loading: false,
      error: null,
      credentials: {
        username: '',
        phone_number: '',
      },
      valid: false,
      registerComplete: false,
      student: null,
    }
  },
  methods: {
    async onSubmit() {
      this.loading = true
      this.error = null

      await ApiService.post('check-application', this.credentials)
        .then(({ data }) => {
          if (data.message === 'success') {
            this.student = data.student
            this.valid = true
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .catch(({ response }) => {
          if (response?.status === 401) {
            this.error = 'البيانات التي قمت بإدخالها ليست موجوده أو خاطئ.'
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .finally(() => (this.loading = false))
    },
  },
}
</script>
<style lang="scss">
.login {
  margin: 3rem 0;
  .form-group {
    margin-bottom: 1rem;
  }
  .form-control {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #464e5f;
    padding: 1rem;
    height: auto;
    &:focus {
      background-color: #ecf0f3;
      border-color: #ecf0f3;
    }
  }
}
</style>
