<template>
  <div>
    <!--begin::Body-->
    <b-row class="justify-content-md-center">
      <div class="kt-login__body bg-white shadow kt-rounded p-4 m-0 col-xl-4 col-12">
        <!--begin::Signin-->
        <div class="kt-login__form">
          <a href="/" class="kt-login__logo d-flex">
            <img src="/assets/logo.png" class="mx-auto" width="140px" />
          </a>
          <h2 class="text-center text-dark mt-5">جامعة غريان</h2>
          <h5 class="text-center text-muted mt-3">طلب الحصول على إيصال نموذج التسجيل</h5>
          <!--begin::Form-->
          <FormValidation class="login" @submit="onSubmit" :loading="loading">
            <TextInput
              rules="required"
              label="الرقم الوطني أو جواز السفر"
              label-class="font-weight-bold text-dark"
              name="username"
              v-model.trim="form.username"
            />
            <TextInput
              rules="required"
              label="رقم الهاتف"
              label-class="font-weight-bold text-dark"
              name="phone_number"
              description="قم بإدخال رقم الهاتف التي قمت بتسجيل به، يجب ان يكون رقم الهاتف على صيغة 002189XXXXXXXX"
              v-model.trim="form.phone_number"
            />

            <b-alert :show="!!error" dismissible @dismissed="error = null" variant="danger" class="mb-0 mt-4">
              {{ error }}
            </b-alert>

            <div class="kt-login__actions">
              <v-button type="submit" wide size="lg" variant="brand" :loading="loading">إرسال الطلب</v-button>
            </div>
          </FormValidation>
        </div>
        <!--end::Signin-->
      </div>
    </b-row>
    <!--end::Body-->
  </div>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput'
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation'

// import Vue from 'vue'
// import { VueReCaptcha } from 'vue-recaptcha-v3'

// Vue.use(VueReCaptcha, { siteKey: process.env.MIX_GOOGLE_CAPTCHA_PUBLIC_KEY })

export default {
  components: { FormValidation, TextInput },
  name: 'login',
  data() {
    return {
      loading: false,
      error: null,
      form: {
        username: '',
        phone_number: '',
      },
    }
  },
  methods: {
    async onSubmit() {
      this.loading = true
      this.error = null

      // await this.$recaptchaLoaded()
      // this.form.recaptcha_token = await this.$recaptcha('login')

      await ApiService.post('application/get-receipt', this.form)
        .then(({ data }) => {
          if (data.message === 'success') {
            this.form.username = ''
            this.form.phone_number = ''

            this.$swal({
              title: `يرجى طباعة نموذج التسجيل و تقديمه مع المستندات المطلوبة`,
              text: null,
              icon: 'success',
              confirmButtonText: `<a href="${data.url}" class="text-white">طباعة النموذج</a>`,
            })
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .catch(({ response }) => {
          if (response?.status >= 400 && response?.status <= 500) {
            this.error = 'البيانات التي قمت بإدخالها ليست موجوده أو خاطئ.'
          } else {
            this.error = this.$t('msg.error')
          }
        })
        .finally(() => (this.loading = false))
    },
  },
}
</script>
<style lang="scss">
.login {
  margin: 3rem 0;
  .form-group {
    margin-bottom: 1rem;
  }
  .form-control {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #464e5f;
    padding: 1rem;
    height: auto;
    &:focus {
      background-color: #ecf0f3;
      border-color: #ecf0f3;
    }
  }
}
</style>
