<template>
  <card :title="$t('profile.personal_information')" title-icon="fa fa-user">
    <template slot="body">
      <div class="kt-section kt-section--first">
        <div class="kt-section__body">
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('profile.image') }}</label>
            <div class="col-lg-9 col-xl-6">
              <div class="kt-avatar kt-avatar--outline" id="kt_user_avatar">
                <img class="kt-avatar__holder" :src="user.image" alt="avatar"/>
                <label class="kt-avatar__upload" data-toggle="kt-tooltip" title="" data-original-title="Change avatar">
                  <i class="fa fa-pen"></i>
                  <input
                    ref="avatarInput"
                    @change="changeAvatar"
                    type="file"
                    name="profile_avatar"
                    accept=".png, .jpg, .jpeg, .webp"
                  />
                </label>
                <b-overlay :show="loadingAvatar" spinner-variant="brand" no-wrap/>
                <!--                                <span-->
                <!--                                    class="kt-avatar__cancel"-->
                <!--                                    data-toggle="kt-tooltip"-->
                <!--                                    title=""-->
                <!--                                    data-original-title="Cancel avatar"-->
                <!--                                >-->
                <!--                                    <i class="fa fa-times"></i>-->
                <!--                                </span>-->
              </div>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.name_ar') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input
                disabled
                class="form-control"
                type="text"
                :value="`${user.first_name_ar} ${user.second_name_ar} ${user.third_name_ar} ${user.last_name_ar}`"
              />
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.name_en') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input
                disabled
                class="form-control"
                type="text"
                :value="`${user.first_name_en} ${user.second_name_en} ${user.third_name_en} ${user.last_name_en}`"
              />
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.national_id') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="user.national_id"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">رقم البطاقة الشخصية</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="user.personal_id"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.passport') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="user.passport_number"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.birthday') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="user.dob"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.address') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="user.address"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.gender') }}</label>
            <div class="col-lg-9 col-xl-6">
              <input disabled class="form-control" type="text" :value="$t('users.' + user.gender)"/>
            </div>
          </div>
          <div class="row">
            <label class="col-xl-3"></label>
            <div class="col-lg-9 col-xl-6">
              <h3 disabled class="kt-section__title kt-section__title-sm">
                {{ $t('profile.contact_info') }}
              </h3>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.email') }}</label>
            <div class="col-lg-9 col-xl-6">
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text"><i class="la la-at"></i></span>
                </div>
                <input disabled type="text" class="form-control" :value="user.email"/>
              </div>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-xl-3 col-lg-3 col-form-label">{{ $t('users.phone') }}</label>
            <div class="col-lg-9 col-xl-6">
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text"><i class="la la-phone"></i></span>
                </div>
                <input disabled type="text" class="form-control" :value="user.phone_number"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import Compressor from 'compressorjs'

export default {
  name: 'PersonalInformation',
  components: {Card},
  computed: {
    user() {
      return this.$store.getters.auth
    },
  },
  data() {
    return {
      loadingAvatar: false,
    }
  },
  methods: {
    changeAvatar(e) {
      this.loadingAvatar = true

      const file = e.target.files[0]
      new Compressor(file, {
        quality: 0.8,
        convertSize: '1000000',
        maxWidth: '350',
        maxHeight: '350',
        success: file => {
          const formData = new FormData()
          formData.append('image', file, file.name)
          this.$store
            .dispatch('updateAvatar', formData)
            .catch(({response: {data}}) => {
              this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
            })
            .finally(() => (this.loadingAvatar = false))
        },
        error: () => {
          this.$utils.alert(this.$t('msg.uploading_image_error'), 'error')
        },
      })

      const input = this.$refs.avatarInput
      input.type = 'text'
      input.type = 'file'
    },
  },
}
</script>
