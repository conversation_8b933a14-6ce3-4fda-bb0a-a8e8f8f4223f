<template>
  <card :title="$t('profile.job_info')" title-icon="fas fa-user-tie" :loading="pageLoading">
    <template slot="body">
      <form-validation ref="form" :loading="loading" @submit="submit">
        <b-tabs>
          <b-tab v-for="lang in $store.getters.languages" :key="lang.id" :title="lang.title">
            <b-row class="multiple-languages" dir="auto">
              <TextareaInput
                class="col-12"
                :vid="`bio_${lang.id}`"
                :label="$t('users.bio', lang.id)"
                name="bio[]"
                v-model="form.bio[lang.id]"
              />
              <text-input
                class="col-12"
                :vid="`job_title_${lang.id}`"
                :label="$t('users.job_title', lang.id)"
                name="job_title[]"
                v-model="form.job_title[lang.id]"
              />
            </b-row>
          </b-tab>
        </b-tabs>
        <hr />

        <b-row>
          <text-input
            class="col-12 col-md-6"
            :label="$t('users.general_major')"
            name="general_major"
            v-model="form.general_major"
          />
          <text-input
            class="col-12 col-md-6"
            :label="$t('users.specialization')"
            name="specialization"
            v-model="form.specialization"
          />
        </b-row>

        <b-row>
          <text-input
            class="col-12"
            :label="$t('users.personal_website')"
            name="personal_website"
            v-model="form.personal_website"
          />
          <DatepickerInput
            class="col-12"
            :label="$t('users.start_job_date')"
            name="start_date"
            default-value="2000-01-01"
            v-model="form.start_date"
          />
        </b-row>
        <b-row>
          <h4>{{ $t('users.cv') }}</h4>
          <file-uploader :files="form.cv" accept=".pdf" class="col-12 mt-4" />
        </b-row>
        <div class="d-flex justify-content-end">
          <v-button variant="success" type="submit" :loading="loading">
            {{ $t('form.save_changes') }}
          </v-button>
        </div>
      </form-validation>
    </template>
  </card>
</template>

<script>
import Card from '@/views/partials/content/Card.vue'
import FormValidation from '@/components/form-controls/FormValidation'
import TextareaInput from '@/components/form-controls/TextareaInput'
import FileUploader from '@/components/FileUploader'
import TextInput from '@/components/form-controls/TextInput'
import DatepickerInput from '@/components/form-controls/DatepickerInput'
import FormService from '@/common/form.service'
import ApiService from '@/common/api.service'

export default {
  name: 'JobInfo',
  components: { DatepickerInput, TextInput, FileUploader, TextareaInput, FormValidation, Card },
  data() {
    return {
      form: {
        bio: { translate: true },
        job_title: { translate: true },
        general_major: '',
        specialization: '',
        personal_website: null,
        start_date: null,
        cv: [],
      },
      loading: false,
      pageLoading: true,
    }
  },
  methods: {
    submit() {
      this.loading = true

      const formData = FormService.objectToFormData(this.form)

      formData.append('_method', 'put')
      ApiService.post('lecturers/job-info', formData)
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
        })
        .catch(({ response: { data = {} } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
  },
  created() {
    ApiService.get('lecturers/job-info')
      .then(({ data }) => {
        this.form = data
        if (Array.isArray(data.bio)) this.form.bio = { translate: true }
        // if (Array.isArray(data.general_major)) this.form.general_major = { translate: true }
        // if (Array.isArray(data.specialization)) this.form.specialization = { translate: true }
        if (Array.isArray(data.job_title)) this.form.job_title = { translate: true }
      })
      .catch(this.$utils.errorHandler)
      .finally(() => {
        FormService.setTranslations(this.form, this.$store.getters.languages)

        this.pageLoading = false
      })
  },
}
</script>
