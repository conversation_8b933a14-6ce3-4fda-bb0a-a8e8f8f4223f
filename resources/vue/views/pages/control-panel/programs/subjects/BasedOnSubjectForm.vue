<template>
    <b-modal ref="modal" title="إضافة مادة من برنامج اخر" centered hide-footer size="lg" @hide="close">
        <FormValidation :loading="loading" @submit="onSubmit">
            <b-row v-if="!form.id">
                <select-input
                    class="col-md-6 col-12"
                    rules="required"
                    label="البرنامج الدراسي"
                    :options="programs"
                    v-model="form.program_id"
                />
                <select-input
                    class="col-md-6 col-12"
                    rules="required"
                    label="المادة الدراسية"
                    :options="subjects"
                    :loading="loadSubjects"
                    v-model="form.subject_id"
                />
            </b-row>
            <b-row>
                <text-input class="col-12" rules="required" label="رمز المادة الدراسية" v-model="form.code" />
            </b-row>
            <div class="d-flex justify-content-end">
                <v-button class="px-4 mr-2" variant="label-brand" @click="close">{{ $t('form.back') }}</v-button>
                <v-button type="submit" variant="brand" wide :loading="loading">{{ $t('form.save') }}</v-button>
            </div>
        </FormValidation>
    </b-modal>
</template>

<script>
import FormValidation from '@/components/form-controls/FormValidation'
import ApiService from '@/common/api.service'
import SelectInput from '@/components/form-controls/SelectInput'
import TextInput from '@/components/form-controls/TextInput'

export default {
    name: 'BasedOnSubjectForm',
    components: { TextInput, SelectInput, FormValidation },
    props: {
        data: {
            type: Object,
            required: true,
        },
        active: {
            type: Boolean,
            default: false,
        },
    },
    watch: {
        active(val) {
            if (!val) return
            this.form = { ...this.data }
            this.$refs.modal.show()
        },
        'form.program_id'(id) {
            const program = this.programs.find(program => program.id === id)
            program && this.getSubjects(program)
        },
    },
    data() {
        return {
            form: {
                program_id: null,
                subject_id: null,
                code: '',
            },
            subjects: [],
            loading: false,
            loadSubjects: false,
        }
    },
    computed: {
        programId() {
            return this.$route.params.program_id
        },
        programs() {
            return this.$store.getters.programs
                .filter(program => program.id != this.programId)
                .map(program => ({ id: program.id, title: program.title[this.$lang] }))
        },
    },
    methods: {
        getSubjects(program) {
            if (program.subjects) {
                this.subjects = program.subjects
                return
            }

            this.loadSubjects = true
            ApiService.query(`programs/${program.id}/subjects`)
                .then(({ data }) => {
                    program.subjects = data.subjects.map(subject => {
                        return {
                            id: subject.id,
                            title: `${subject.code} - ${subject.title[this.$lang]}`,
                            subject_title: subject.title,
                        }
                    })
                    this.subjects = program.subjects
                })
                .finally(() => (this.loadSubjects = false))
        },
        onSubmit() {
            this.loading = true
            this.request()
                .then(() => {
                    this.$emit('save', {
                        id: this.form.subject_id,
                        code: this.form.code,
                        title: this.form.title || this.subjects.find(s => s.id === this.form.subject_id)?.subject_title,
                    })
                    this.close()
                })
                .catch(({ response: { data } }) => {
                    this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
                })
                .finally(() => (this.loading = false))
        },
        request() {
            if (!this.form.id) {
                return ApiService.post(`programs/${this.programId}/based-on`, this.form)
            } else {
                return ApiService.update(`programs/${this.programId}/based-on`, this.form.id, this.form)
            }
        },
        close() {
            this.$refs.modal.hide()
            this.$emit('update:active', false)
        },
    },
    created() {
        this.$store.dispatch('getPrograms')
    },
}
</script>
