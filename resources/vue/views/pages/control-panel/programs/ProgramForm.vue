<template>
  <Card :loading="pageLoading" :title="!id ? $t('programs.add') : $t('programs.edit')" title-icon="flaticon-users">
    <template slot="toolbar">
      <!--      <v-button v-if="id && $acl.can('delete-programs')" @click="remove" variant="danger" icon="flaticon2-trash">{{-->
      <!--        $t('programs.delete')-->
      <!--      }}</v-button>-->
    </template>
    <template slot="body">
      <form-validation :loading="loading" @submit="onSubmit">
        <b-row>
          <SelectInput
            v-if="isUniversity"
            rules="required"
            class="col-md-6 col-12"
            :label="$t('school')"
            name="school_id"
            :options="schools"
            :disabled="!!form.id"
            v-model="school_id"
          >
          </SelectInput>
          <SelectInput
            v-if="$acl.middleware('university|school')"
            rules="required"
            class="col-md-6 col-12"
            :label="$t('department')"
            name="entity_id"
            :disabled="(!school_id && isUniversity) || !!form.id"
            :options="department"
            v-model="form.entity_id"
          >
          </SelectInput>
          <select-input
            v-model="form.type"
            :label="$t('programs.type')"
            :options="$store.getters.programTypes"
            class="col-md-3 col-6"
            name="type"
            rules="required"
            text-field="title"
            value-field="id"
          />
          <select-input
            v-model="form.duration_unit"
            :label="$t('study_type')"
            :options="$store.getters.durationUnits"
            class="col-md-3 col-6"
            name="duration_unit"
            rules="required"
            text-field="title"
            value-field="id"
          />
          <!-- <select-input
            v-model="form.duration_unit"
            :label="$t('study_type')"
            :disabled="!!form.id"
            :options="$store.getters.durationUnits"
            class="col-md-3 col-6"
            name="duration_unit"
            rules="required"
            text-field="title"
            value-field="id"
          /> -->
          <SelectInput
            v-model="form.program_based_on_id"
            :disabled="!form.entity_id"
            :options="programs"
            class="col-md-3 col-6"
            label="يعتمد على البرنامج"
            name="program_based_on_id"
            text-field="title"
            value-field="id"
            clearable
            description="يمكنك ترك هذا الحقل فارغ إذا كان هذا البرنامج لا يعتمد على برنامج اخر"
          />
        </b-row>
        <b-row>
          <text-input
            v-model="form.duration"
            :label="$t('programs.duration')"
            class="col-md-3 col-6"
            name="duration"
            rules="required|integer"
            type="number"
          />
          <select-input
            v-model="form.lang"
            :label="$t('programs.lang')"
            :options="$store.getters.languages"
            class="col-md-3 col-6"
            name="lang"
            rules="required"
            text-field="title"
            value-field="id"
          />
          <text-input
            v-model="form.weakly_teaching_hours"
            :label="$t('programs.weakly_teaching_hours')"
            class="col-md-3 col-6"
            name="weakly_teaching_hours"
            rules="required|integer"
            type="number"
          />
          <text-input
            v-model="form.weakly_lab_hours"
            :label="$t('programs.weakly_lab_hours')"
            class="col-md-3 col-6"
            name="weakly_lab_hours"
            rules="required|integer"
            type="number"
          />
        </b-row>
        <b-row>
          <text-input
            v-model="form.general_credits"
            :label="$t('programs.general_credits')"
            class="col-md-3 col-6"
            name="general_credits"
            rules="required|integer"
            type="number"
          />
          <text-input
            v-model="form.compulsory_credits"
            :label="$t('programs.compulsory_credits')"
            class="col-md-3 col-6"
            name="compulsory_credits"
            rules="required|integer"
            type="number"
          />
          <text-input
            v-model="form.elective_credits"
            :label="$t('programs.elective_credits')"
            class="col-md-3 col-6"
            name="elective_credits"
            rules="required|integer"
            type="number"
          />
          <text-input
            v-model="form.training_credits"
            :label="$t('programs.training_credits')"
            class="col-md-3 col-6"
            name="training_credits"
            rules="required|integer"
            type="number"
          />
          <text-input
            v-model="form.supportive_credits"
            :label="$t('programs.supportive_credits')"
            class="col-md-3 col-6"
            name="supportive_credits"
            rules="integer"
            type="number"
          />
          <text-input
            v-model="form.total_credits"
            :label="$t('programs.total_credits')"
            class="col-md-3 col-6"
            name="total_credits"
            rules="required|integer"
            type="number"
          />
          <switch-input
            v-model="form.is_rewarded"
            :checked-value="1"
            :unchecked-value="0"
            class="col-md-3 col-12"
            label="يمنح شهادة؟"
            name="is_rewarded"
          />
          <switch-input
            v-model="form.is_active"
            :checked-value="1"
            :unchecked-value="0"
            class="col-md-3 col-12"
            label="مفعل؟"
            name="is_active"
          />
        </b-row>
        <b-tabs>
          <b-tab v-for="lang in $store.getters.languages" :key="lang.id" :title="lang.title">
            <b-row class="multiple-languages" dir="auto">
              <text-input
                v-model="form.title[lang.id]"
                :label="$t('programs.title', lang.id)"
                :rules="{ required: !Object.values(form.title).some(value => !!value) }"
                :vid="`title_${lang.id}`"
                class="col-md-4 col-12"
                name="title[]"
              />
              <text-input
                v-model="form.certificate_awarded[lang.id]"
                :label="$t('programs.certificate_awarded', lang.id)"
                :vid="`certificate_awarded_${lang.id}`"
                class="col-md-4 col-12"
                name="certificate_awarded[]"
              />
              <text-input
                v-model="form.major[lang.id]"
                :label="$t('programs.major', lang.id)"
                :vid="`major_${lang.id}`"
                class="col-md-4 col-12"
                name="major[]"
              />
              <rich-editor
                v-model="form.objectives[lang.id]"
                :label="$t('programs.objectives', lang.id)"
                :lang="lang.id"
                :vid="`objectives_${lang.id}`"
                class="col-12"
                max-rows="8"
                name="objectives[]"
                rows="3"
              />
              <rich-editor
                v-model="form.outcomes[lang.id]"
                :label="$t('programs.outcomes', lang.id)"
                :lang="lang.id"
                :vid="`outcomes_${lang.id}`"
                class="col-12"
                max-rows="8"
                name="outcomes[]"
                rows="3"
              />
              <rich-editor
                v-model="form.entry_requirements[lang.id]"
                :label="$t('programs.entry_requirements', lang.id)"
                :lang="lang.id"
                :vid="`entry_requirements_${lang.id}`"
                class="col-12"
                max-rows="8"
                name="entry_requirements[]"
                rows="3"
              />
              <rich-editor
                v-model="form.job_market[lang.id]"
                :label="$t('programs.job_market', lang.id)"
                :lang="lang.id"
                :vid="`job_market_${lang.id}`"
                class="col-12"
                max-rows="8"
                name="job_market[]"
                rows="3"
              />
              <rich-editor
                v-model="form.description[lang.id]"
                :label="$t('programs.description', lang.id)"
                :lang="lang.id"
                :vid="`description_${lang.id}`"
                class="col-12"
                max-rows="8"
                name="description[]"
                rows="3"
              />
            </b-row>
            <hr />
            <h4 class="text-primary mb-4">{{ $t('programs.booklet') }}</h4>
            <b-row>
              <file-uploader :files="form.booklet" class="col-lg-6" />
            </b-row>

            <h4 class="text-primary mb-4">{{ $t('programs.image') }}</h4>
            <image-uploader :images="images" />
          </b-tab>
        </b-tabs>
        <hr />
        <div class="d-flex justify-content-end">
          <b-button class="px-4 mr-2" to="../">{{ $t('form.back') }}</b-button>
          <v-button :loading="loading" type="submit" variant="brand" wide>
            {{ id ? $t('form.save') : $t('form.add') }}
          </v-button>
        </div>
      </form-validation>
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation'
import TextInput from '@/components/form-controls/TextInput'
import SelectInput from '@/components/form-controls/SelectInput'
import Card from '@/views/partials/content/Card.vue'
import FormService from '@/common/form.service'
import ImageUploader from '@/components/ImageUploader'
import FileUploader from '@/components/FileUploader'
import RichEditor from '@/components/RichEditor'
import SwitchInput from '@/components/form-controls/SwitchInput.vue'

export default {
  name: 'ProgramForm',
  components: {
    RichEditor,
    FileUploader,
    ImageUploader,
    Card,
    SelectInput,
    TextInput,
    FormValidation,
    SwitchInput,
  },
  data() {
    return {
      pageLoading: true,
      loading: false,
      images: [],
      school_id: null,
      form: {
        entity_id: null,
        program_based_on_id: null,
        type: 1,
        duration_unit: 1,
        lang: null,
        duration: null,
        weakly_teaching_hours: null,
        weakly_lab_hours: null,
        total_credits: null,
        general_credits: null,
        elective_credits: null,
        compulsory_credits: null,
        training_credits: null,
        supportive_credits: null,
        title: { translate: true },
        major: { translate: true },
        objectives: { translate: true },
        outcomes: { translate: true },
        description: { translate: true },
        job_market: { translate: true },
        entry_requirements: { translate: true },
        certificate_awarded: { translate: true },
        booklet: [],
        is_active: true,
        is_rewarded: true,
      },
    }
  },
  watch: {
    school_id(_, oldVal) {
      if (oldVal) this.form.entity_id = null
    },
    'form.entity_id'(_, oldVal) {
      if (oldVal) this.form.program_based_on_id = null
    },
  },
  computed: {
    id() {
      return this.$route.params.program_id
    },
    schools() {
      return this.$store.getters.schools
    },
    isUniversity() {
      return this.$acl.middleware('university')
    },
    isDepartment() {
      return this.$acl.middleware('department')
    },
    department() {
      if (this.isUniversity) {
        return this.$store.getters.departments.filter(dep => dep.parent_entity_id === this.school_id)
      } else {
        return this.$store.getters.departments
      }
    },
    programs() {
      const programs = this.isUniversity
        ? this.$store.getters.basedOnPrograms.filter(program =>
            this.department.some(dep => dep.id === program.entity_id)
          )
        : this.$store.getters.basedOnPrograms
      return programs.map(program => ({
        id: program.id,
        title: this.$utils.getCurrentOrFirstLang(program.title),
      }))
    },
  },
  methods: {
    requirements() {
      if (this.images.length === 0) {
        this.$utils.alert(this.$t('programs.required_image'), 'warning')
        return false
      }
      return true
    },
    onSubmit() {
      if (!this.requirements()) return
      this.loading = true
      this.request()
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.$router.push({ name: 'programs' })
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
    request() {
      if (this.images[0] && this.images[0].file) this.form.image = this.images[0]

      const formData = FormService.objectToFormData(this.form)

      if (!this.id) {
        return ApiService.post('programs', formData)
      } else {
        formData.append('_method', 'put')
        return ApiService.post('programs/' + this.id, formData)
      }
    },
    remove() {
      this.$utils.confirm(this.$t('programs.confirm_delete')).then(({ value }) => {
        if (!value) return
        this.pageLoading = true
        ApiService.delete('programs', this.id)
          .then(() => {
            this.$utils.alert(this.$t('msg.success'), 'success')
            this.$router.push({ name: 'programs' })
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
          .finally(() => (this.pageLoading = false))
      })
    },
  },
  async created() {
    await Promise.all([
      this.$store.dispatch('getBasedOnPrograms'),
      this.$store.dispatch('getProgramTypes'),
      this.$store.dispatch('getDurationUnits'),
    ]).catch(this.$utils.errorHandler)

    this.id &&
      (await ApiService.get('programs', this.id)
        .then(({ data }) => {
          this.form = data
          this.images.push({ image: data.image })
          delete this.form.image
          if (this.isUniversity) {
            this.school_id = this.$store.getters.entityById(data.entity_id)?.parent_entity_id
          }
        })
        .catch(this.$utils.errorHandlerWith404))

    if (!this.id && this.$store.getters.durationUnits.length > 0) {
      this.form.duration_unit = this.$store.getters.durationUnits[0].id
    }

    FormService.setTranslations(this.form, this.$store.getters.languages)

    if (this.isDepartment && !this.form.entity_id) {
      this.form.entity_id = this.$store.getters.entity.id
    }

    this.pageLoading = false
  },
}
</script>
