<template>
  <div class="row">
    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.entities" :subtitle="$t('menu.entities')" icon="la la-university"></stats-card>
    </div>
    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.programs" :subtitle="$t('menu.programs')" icon="flaticon-interface-3"></stats-card>
    </div>

    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.lecturers" :subtitle="$t('menu.lecturers')" icon="flaticon-users-1"></stats-card>
    </div>
    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.employees" :subtitle="$t('menu.employees')" icon="flaticon-users-1"></stats-card>
    </div>
    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.buildings" :subtitle="$t('menu.buildings')" icon="la la-building"></stats-card>
    </div>
    <div class="col-lg-4 mt-4">
      <stats-card :title="statistics.halls" :subtitle="$t('menu.halls')" icon="la la-header"></stats-card>
    </div>
  </div>
</template>

<script>
import StatsCard from '@/components/StatsCard.vue'
import ApiService from '@/common/api.service'

export default {
  name: 'dashboard',
  components: { StatsCard },

  data() {
    return {
      statistics: {},
    }
  },

  created() {
    ApiService.query(`control-panel/dashboard`)
      .then(({ data }) => {
        this.statistics = data
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
