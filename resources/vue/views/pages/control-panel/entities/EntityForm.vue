<template>
  <Card :title="!id ? $t('entities.add') : $t('entities.edit')" title-icon="la la-university" :loading="pageLoading">
    <template slot="toolbar">
      <v-button
        v-if="id && $store.getters.entity.id !== id && $acl.can('delete-entities')"
        @click="remove"
        variant="danger"
        icon="flaticon2-trash"
      >
        {{ $t('entities.delete') }}
      </v-button>
    </template>
    <template slot="body">
      <form-validation :loading="loading" @submit="onSubmit">
        <b-tabs nav-class="nav-tabs-line">
          <b-tab :title="$t('entities.main_info')">
            <b-tabs>
              <b-tab v-for="lang in $store.getters.languages" :key="lang.id" :title="lang.title">
                <b-row class="multiple-languages" dir="auto">
                  <text-input
                    class="col-12"
                    :rules="{ required: !Object.values(form.title).some(value => !!value) }"
                    :vid="`title_${lang.id}`"
                    name="title[]"
                    :label="$t('entities.name', lang.id)"
                    v-model="form.title[lang.id]"
                  />
                </b-row>
              </b-tab>
            </b-tabs>
            <hr />
            <b-row>
              <text-input
                v-if="!id"
                dir="ltr"
                :append="`.${domain}`"
                class="col-md-6 col-12"
                rules="required|alpha|min:2|max:5"
                name="id"
                :label="$t('entities.domain')"
                v-model="form.id"
              />
              <text-input v-else class="col-md-6 col-12" :label="$t('entities.domain')" v-model="form.url" readonly />
              <select-input
                v-if="!id"
                class="col-md-6 col-12"
                rules="required"
                name="type"
                :label="$t('entities.type')"
                :options="entityTypes"
                v-model="form.type"
              />
              <text-input
                v-else
                class="col-md-6 col-12"
                :label="$t('entities.type')"
                :value="getEntityType(form.type)"
                readonly
              />
            </b-row>
            <b-row>
              <text-input
                class="col-md-6 col-12"
                rules="required|email"
                name="email"
                :label="$t('entities.email')"
                v-model.trim="form.email"
              />
              <text-input
                class="col-md-6 col-12"
                rules="required|numeric|min:7|max:15"
                name="tel"
                :label="$t('entities.phone')"
                v-model="form.tel"
              />
            </b-row>
            <b-row>
              <select-input
                searchable
                clearable
                class="col-md-6 col-12"
                name="entity_head"
                text="name_ar"
                :label="$t('entities.entity_head')"
                :options="lecturers"
                v-model="form.entity_head"
              />
            </b-row>
            <b-row>
              <select-input
                v-if="form.type === entityTypesEnum.SCHOOL"
                searchable
                clearable
                class="col-md-6 col-12"
                name="entity_head"
                text="name_ar"
                label="الدراسة والامتحانات"
                :options="lecturers"
                v-model="form.study_and_exams"
              />
            </b-row>
            <b-row>
              <select-input
                class="col-md-6 col-12"
                name="campus_id"
                :label="$t('entities.campus')"
                :options="$store.getters.campuses"
                v-model="form.campus_id"
              />
              <select-input
                class="col-md-6 col-12"
                name="buildings"
                :label="$t('entities.buildings')"
                :options="$store.getters.buildings"
                v-model="form.buildings"
                multiple
                searchable
              />
            </b-row>
          </b-tab>
          <b-tab :title="$t('entities.details')">
            <b-tabs>
              <b-tab v-for="lang in $store.getters.languages" :key="lang.id" :title="lang.title">
                <b-row class="multiple-languages" dir="auto">
                  <rich-editor
                    class="col-12"
                    :vid="`head_word_${lang.id}`"
                    :lang="lang.id"
                    name="head_word[]"
                    :label="$t('entities.head_word', lang.id)"
                    rows="3"
                    max-rows="8"
                    v-model="form.head_word[lang.id]"
                  />
                  <rich-editor
                    class="col-12"
                    :vid="`description_${lang.id}`"
                    :lang="lang.id"
                    name="description[]"
                    :label="$t('entities.description', lang.id)"
                    rows="3"
                    max-rows="8"
                    v-model="form.description[lang.id]"
                  />
                  <rich-editor
                    class="col-12"
                    :vid="`vision_${lang.id}`"
                    :lang="lang.id"
                    name="vision[]"
                    :label="$t('entities.vision', lang.id)"
                    rows="3"
                    max-rows="8"
                    v-model="form.vision[lang.id]"
                  />
                  <rich-editor
                    class="col-12"
                    :vid="`message_${lang.id}`"
                    :lang="lang.id"
                    name="mission[]"
                    :label="$t('entities.mission', lang.id)"
                    rows="3"
                    max-rows="8"
                    v-model="form.message[lang.id]"
                  />
                  <rich-editor
                    class="col-12"
                    :vid="`goals_${lang.id}`"
                    :lang="lang.id"
                    name="goals[]"
                    :label="$t('entities.goals', lang.id)"
                    rows="3"
                    max-rows="8"
                    v-model="form.goals[lang.id]"
                  />
                </b-row>

                <hr />

                <h4 class="text-primary mb-4">
                  {{ $t('entities.cover') }}
                </h4>
                <image-uploader :images="images" />
              </b-tab>
            </b-tabs>
          </b-tab>
          <b-tab title="اعدادات">
            <div v-if="form.type === entityTypesEnum.SCHOOL || form.type === entityTypesEnum.DEPARTMENT">
              <p class="text-primary mb-4">
                التخصص
              </p>
              <div class="d-flex flex-wrap">
                <label
                  v-for="item in [
                    { value: 'scientific', text: 'علمي' },
                    { value: 'literary', text: 'ادبي' },
                  ]"
                  :key="item.value"
                  class="my-3 mx-4 select-none font-weight-bolder text-dark kt-checkbox kt-checkbox--tick kt-checkbox--brand"
                >
                  <input type="checkbox" :value="item.value" v-model="form.departments" />
                  {{ item.text }}
                  <span></span>
                </label>
              </div>
            </div>
          </b-tab>
          <b-tab :title="$t('entities.links')">
            <b-row class="multiple-languages" dir="ltr">
              <text-input
                v-for="link in $store.getters.links"
                :key="link.id"
                :name="link.title"
                :label="link.title"
                v-model.trim="form.links[link.id]"
                class="col-12"
                type="url"
              />
            </b-row>
          </b-tab>
        </b-tabs>
        <hr />
        <div class="d-flex justify-content-end">
          <b-button class="px-4 mr-2" to="./">{{ $t('form.back') }}</b-button>
          <v-button type="submit" variant="brand" wide :loading="loading">
            {{ id ? $t('form.save') : $t('form.add') }}
          </v-button>
        </div>
      </form-validation>
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation'
import TextInput from '@/components/form-controls/TextInput'
import SelectInput from '@/components/form-controls/SelectInput'
import Card from '@/views/partials/content/Card.vue'
import FormService from '@/common/form.service'
import RichEditor from '@/components/RichEditor'
import ImageUploader from '@/components/ImageUploader'

export default {
  name: 'EntityForm',
  components: { ImageUploader, RichEditor, Card, SelectInput, TextInput, FormValidation },
  data() {
    return {
      pageLoading: true,
      loading: false,
      images: [],
      form: {
        id: null,
        type: null,
        tel: null,
        email: null,
        entity_head: null,
        study_and_exams: null,
        buildings: [],
        campus_id: null,
        title: { translate: true },
        vision: { translate: true },
        message: { translate: true },
        description: { translate: true },
        goals: { translate: true },
        head_word: { translate: true },
        links: {},
        entity_cover: null,
        departments: [],
      },
      lecturers: [],
    }
  },
  computed: {
    id() {
      return this.$route.params.entity_id
    },
    domain() {
      return window.location.hostname
    },
    entityTypesEnum() {
      return this.$store.getters.entityTypesEnum
    },
    entityTypes() {
      const types = this.$store.getters.entityTypes
      switch (this.$store.getters.entityType) {
        case 'university':
          return types.filter(t => t.id === this.entityTypesEnum.SCHOOL || t.id === this.entityTypesEnum.MANAGEMENT)
        case 'school':
          return types.filter(t => t.id === this.entityTypesEnum.DEPARTMENT)
        default:
          return []
      }
    },
    getEntityType() {
      return type => {
        return this.$store.getters.entityTypes.find(t => t.id === type)?.title
      }
    },
  },
  methods: {
    onSubmit() {
      this.loading = true
      this.request()
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.$router.push({ name: 'entities' })
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
    request() {
      if (this.images[0] && this.images[0].file) this.form.entity_cover = this.images[0]

      const formData = FormService.objectToFormData(this.form)

      if (!this.id) {
        return ApiService.post('entities', formData)
      } else {
        formData.append('_method', 'put')
        return ApiService.post('entities/' + this.form.id, formData)
      }
    },
    remove() {
      this.$utils.confirm(this.$t('entities.confirm_delete')).then(({ value }) => {
        if (!value) return
        this.pageLoading = true
        ApiService.delete('entities', this.id)
          .then(() => {
            this.$utils.alert(this.$t('msg.success'), 'success')
            this.$router.push({ name: 'entities' })
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
          .finally(() => (this.pageLoading = false))
      })
    },
  },
  async created() {
    await Promise.all([
      this.$store.dispatch('getCampuses'),
      this.$store.dispatch('getBuildings'),
      this.$store.dispatch('getEntityTypes'),
      this.$store.dispatch('getLinks'),
      ApiService.query('all-lecturers').then(({ data }) => (this.lecturers = data || [])),
    ]).catch(this.$utils.errorHandler)

    this.id &&
      (await ApiService.get('entities', this.id)
        .then(({ data }) => {
          data.buildings = data.buildings.map(building => building.id)
          Object.assign(this.form, data, data.details)
          this.images.push({ image: data.entity_cover })

          delete this.form.details
          delete this.form.entity_cover

          this.form.links = {}
          this.form.departments = data?.details?.departments || []
          data.links.forEach(link => (this.form.links[link.id] = link.pivot.link))
        })
        .catch(this.$utils.errorHandlerWith404))

    FormService.setTranslations(this.form, this.$store.getters.languages)

    this.pageLoading = false
  },
}
</script>
