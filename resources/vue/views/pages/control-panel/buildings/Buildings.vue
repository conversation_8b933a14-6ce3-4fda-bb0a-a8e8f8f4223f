<template>
  <Card :title="$t('buildings.manage')" title-icon="la la-building" body-fit :loading="loading">
    <template slot="toolbar">
      <v-button v-if="$acl.can('create-buildings')" @click="openForm()" variant="brand" icon="flaticon2-plus">{{
        $t('buildings.add')
      }}</v-button>
    </template>
    <template slot="body">
      <v-table :headers="headers" :data="datatable" pagination search>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button
              v-if="$acl.can('view-halls')"
              :to="{ name: 'halls', params: { building_id: row.id } }"
              variant="label-brand"
            >
              {{ $t('buildings.view_halls') }}
            </v-button>
            <v-button
              v-if="$acl.can('update-buildings')"
              @click="openForm(row.id)"
              class="ml-2"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button
              v-if="$acl.can('delete-buildings')"
              @click="remove(row.id)"
              class="ml-2"
              icon="la la-trash"
              variant="label-danger"
              icon-only
            />
          </div>
        </template>
      </v-table>
      <building-form
        v-if="$acl.can('create-buildings|update-buildings')"
        :data="buildingData"
        :active.sync="isModalActive"
        @save="save"
      />
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import BuildingForm from '@/views/pages/control-panel/buildings/BuildingForm'

export default {
  name: 'Buildings',
  components: { BuildingForm, Card, VTable },
  data() {
    return {
      headers: [
        { key: 'title', name: this.$t('buildings.title'), sort: true },
        { key: 'description', name: this.$t('buildings.description'), sort: true, expend: true },
        { key: 'longitude', name: this.$t('buildings.longitude'), sort: true, expend: true },
        { key: 'latitude', name: this.$t('buildings.latitude'), sort: true, expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      loading: true,
      isModalActive: false,
      buildingData: {},
    }
  },
  computed: {
    buildings() {
      return this.$store.getters.buildingsExtra
    },
    datatable() {
      return this.buildings.map(building => {
        return {
          id: building.id,
          title: this.$utils.getCurrentOrFirstLang(building.title),
          description: this.$utils.getCurrentOrFirstLang(building.description),
          longitude: building.longitude,
          latitude: building.latitude,
        }
      })
    },
  },
  methods: {
    openForm(id = -1) {
      let data = id === -1 ? {} : this.buildings.find(b => b.id === id)
      this.buildingData = JSON.parse(JSON.stringify(data))
      this.isModalActive = true
    },
    save(data) {
      const index = this.buildings.findIndex(building => building.id === data.id)
      index !== -1 ? this.$set(this.buildings, index, data) : this.buildings.push(data)
    },
    remove(id) {
      this.$utils.confirm(this.$t('buildings.confirm_delete')).then(({ value }) => {
        if (!value) return
        ApiService.delete('buildings', id)
          .then(() => {
            const index = this.buildings.findIndex(building => building.id === id)
            this.buildings.splice(index, 1)
            this.$utils.alert(this.$t('msg.success'), 'success')
          })
          .catch(() => {
            this.$utils.alert(this.$t('msg.error'), 'error')
          })
      })
    },
  },
  created() {
    Promise.all([this.$store.dispatch('getBuildings', { force: true })])
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>
