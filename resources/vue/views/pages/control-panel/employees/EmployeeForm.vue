<template>
  <Card :title="id ? $t('employees.edit') : $t('employees.add')" title-icon="flaticon-user-add" :loading="pageLoading">
    <template slot="body">
      <!--begin::Form-->
      <FormValidation @submit="onSubmit" :loading="loading">
        <h4 class="text-primary mb-4">{{ $t('users.basic_info') }}</h4>
        <h5>{{ $t('users.name_ar') }}</h5>
        <b-row>
          <TextInput
            class="col-6 col-md-3"
            rules="required|arabic"
            :label="$t('users.first_name')"
            name="first_name"
            v-model="form.first_name_ar"
          />
          <TextInput
            class="col-6 col-md-3"
            rules="required|arabic"
            :label="$t('users.second_name')"
            name="second_name"
            v-model="form.second_name_ar"
          />
          <TextInput
            class="col-6 col-md-3"
            rules="required|arabic"
            :label="$t('users.third_name')"
            name="third_name"
            v-model="form.third_name_ar"
          />
          <TextInput
            class="col-6 col-md-3"
            rules="required|arabic"
            :label="$t('users.last_name')"
            name="last_name"
            v-model="form.last_name_ar"
          />
        </b-row>
        <h5>{{ $t('users.name_en') }}</h5>
        <b-row>
          <TextInput
            class="col-6 col-md-3"
            :label="$t('users.first_name')"
            name="first_name"
            v-model="form.first_name_en"
          />
          <TextInput
            class="col-6 col-md-3"
            :label="$t('users.second_name')"
            name="second_name"
            v-model="form.second_name_en"
          />
          <TextInput
            class="col-6 col-md-3"
            :label="$t('users.third_name')"
            name="third_name"
            v-model="form.third_name_en"
          />
          <TextInput
            class="col-6 col-md-3"
            :label="$t('users.last_name')"
            name="last_name"
            v-model="form.last_name_en"
          />
        </b-row>
        <b-row>
          <SelectInput
            class="col-12 col-md-4"
            :label="$t('users.nationality')"
            name="national"
            :options="$store.getters.nationalities"
            value-field="id"
            text-field="title"
            v-model="form.nationality_id"
          >
          </SelectInput>
          <DatepickerInput
            class="col-12 col-md-4"
            rules="required"
            :label="$t('users.birthday')"
            name="birthday"
            default-value="2000-01-01"
            v-model="form.dob"
          />
          <RadioInputGroup
            class="col-12 col-md-4"
            :label="$t('users.gender')"
            name="gender"
            v-model="form.gender"
            :options="[
              { value: 'male', text: $t('users.male') },
              { value: 'female', text: $t('users.female') },
            ]"
          />
          <TextInput
            v-if="form.nationality_id === 'ly'"
            class="col-12 col-md-4"
            rules="required|digits:12"
            :label="$t('users.national_id')"
            name="national_id"
            v-model="form.national_id"
          />
          <TextInput
            class="col-12 col-md-4"
            label="رقم البطاقة الشخصية"
            name="personal_id"
            v-model="form.personal_id"
          />
          <TextInput
            class="col-12 col-md-4"
            :rules="`${form.nationality_id !== 'ly' ? 'required' : ''}`"
            :label="$t('users.passport')"
            name="passport_number"
            v-model="form.passport_number"
          />
          <SelectInput
            class="col-md-6 col-12"
            rules="required"
            :label="$t('employees.agency')"
            name="entity_id"
            :options="entities"
            v-model="form.entity_id"
            searchable
          >
          </SelectInput>
        </b-row>
        <hr />
        <h4 class="text-primary mb-4">{{ $t('users.secondary_info') }}</h4>
        <b-row>
          <text-input
            class="col-md-6 col-12"
            rules="required"
            :label="$t('users.address')"
            name="address"
            v-model="form.address"
          />
        </b-row>
        <hr />
        <h4 class="text-primary mb-4">البيانات الوظيفية</h4>
        <b-row>
          <TextInput
            class="col-md-6 col-12"
            rules="required"
            label="الرقم الوظيفي"
            name="employee_id"
            v-model="form.employee_id"
          />
          <TextInput
            class="col-md-6 col-12"
            rules="required"
            label="تاريخ التعيين"
            name="hiring_date"
            type="date"
            v-model="form.hiring_date"
          />
          <TextInput
            class="col-md-6 col-12"
            rules="required"
            label="رقم قرار التعيين"
            name="hiring_decision_number"
            v-model="form.hiring_decision_number"
          />
          <TextInput
            class="col-md-6 col-12"
            rules="required"
            label="المسمى الوظيفي"
            name="job_title"
            v-model="form.job_title"
          />
          <SelectInput
            class="col-md-6 col-12"
            rules="required"
            label="الدرجة الوظيفية"
            name="job_level"
            :options="jobLevels"
            v-model="form.job_level"
          />
          <SelectInput
            rules="required"
            :label="$t('users.qualifications')"
            name="qualification"
            :options="$store.getters.qualifications"
            value-field="id"
            text-field="title"
            class="col-md-6 col-12"
            v-model="form.qualification_id"
          />
        </b-row>
        <hr />
        <h4 class="text-primary mb-4">{{ $t('users.account_access_info') }}</h4>
        <b-row>
          <TextInput
            class="col-md-6 col-12"
            rules="email|required"
            :label="$t('users.email')"
            name="email"
            type="email"
            v-model.trim="form.email"
          />
          <TextInput
            class="col-md-6 col-12"
            rules="required|phone"
            description="002189xxxxxxxx"
            :label="$t('users.phone')"
            name="phone_number"
            v-model="form.phone_number"
          />
          <TextInput
            class="col-md-6 col-12"
            :rules="`${!id ? 'required' : ''}|min:8`"
            :label="$t('users.password')"
            vid="password"
            name="password"
            type="password"
            v-model="form.password"
          />
          <TextInput
            class="col-md-6 col-12"
            :rules="`${!id ? 'required' : ''}|confirmed:password`"
            :label="$t('users.password_confirmation')"
            name="confirm_password"
            type="password"
            v-model="form.password_confirmation"
          />
        </b-row>
        <div class="d-flex justify-content-end">
          <b-button class="px-4 mr-2" to="./">{{ $t('form.back') }}</b-button>
          <v-button type="submit" variant="brand" wide :loading="loading">
            {{ id ? $t('form.save') : $t('form.add') }}
          </v-button>
        </div>
      </FormValidation>
      <!--end::Form-->
    </template>
  </Card>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput'
import SelectInput from '@/components/form-controls/SelectInput'
import DatepickerInput from '@/components/form-controls/DatepickerInput'
import RadioInputGroup from '@/components/form-controls/RadioInputGroup'
import FormValidation from '@/components/form-controls/FormValidation'
import Card from '@/views/partials/content/Card.vue'
import ApiService from '@/common/api.service'

export default {
  name: 'StudentForm',
  components: { Card, FormValidation, RadioInputGroup, DatepickerInput, SelectInput, TextInput },
  data() {
    return {
      pageLoading: true,
      loading: false,
      form: {
        national_id: null,
        nationality_id: 'ly',
        passport_number: '',
        personal_id: '',
        entity_id: null,
        email: '',
        password: '',
        password_confirmation: '',
        first_name_ar: '',
        second_name_ar: '',
        third_name_ar: '',
        last_name_ar: '',
        first_name_en: '',
        second_name_en: '',
        third_name_en: '',
        last_name_en: '',
        address: '',
        phone_number: '',
        dob: '',
        gender: 'male',

        employee_id: '',
        hiring_date: '',
        hiring_decision_number: '',
        job_title: '',
        job_level: null,
        qualification_id: null,
      },
    }
  },
  computed: {
    id() {
      return this.$route.params.employee_id
    },
    entities() {
      return this.$store.getters.flatEntities.slice().reverse()
    },
    jobLevels() {
      return this.$store.getters.jobLevels
    },
  },
  methods: {
    onSubmit() {
      this.loading = true
      this.request()
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.$router.push({ name: 'employees' })
        })
        .catch(this.$utils.errorHandlerWithValidation)
        .finally(() => (this.loading = false))
    },
    request() {
      if (!this.form.password) delete this.form.password
      if (this.form.nationality_id !== 'ly') delete this.form.national_id

      if (!this.id) {
        return ApiService.post('employees', this.form)
      } else {
        return ApiService.put(`employees/${this.id}`, this.form)
      }
    },
  },
  async created() {
    await Promise.all([this.$store.dispatch('getNationalities'), this.$store.dispatch('getQualifications')]).catch(
      this.$utils.errorHandler
    )

    this.id &&
      (await ApiService.get('employees', this.id)
        .then(({ data }) => {
          this.form = { ...data }
          if (data.employee) this.form = { ...data.employee, ...this.form }
        })
        .catch(this.$utils.errorHandlerWith404))

    this.pageLoading = false
  },
}
</script>
