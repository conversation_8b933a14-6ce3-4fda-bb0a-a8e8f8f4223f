<template>
  <Card
    :title="$t('halls.manage_in_building', { building: building_title })"
    title-icon="flaticon-users"
    body-fit
    :loading="loading"
  >
    <template slot="toolbar">
      <v-button v-if="$acl.can('create-halls')" @click="openForm()" variant="brand" icon="flaticon2-plus">
        {{ $t('halls.add') }}
      </v-button>
    </template>
    <template slot="body">
      <v-table :headers="headers" :data="datatable" pagination search>
        <template slot="td-is_active" slot-scope="{ row }">
          <b-badge v-if="row.is_active" variant="success">نعم</b-badge>
          <b-badge v-else variant="danger">لا</b-badge>
        </template>
        <template slot="td-actions" slot-scope="{ row }">
          <div class="d-flex align-items-center">
            <v-button
              v-if="$acl.can('update-halls')"
              @click="openForm(row.id)"
              icon="la la-edit"
              variant="label-brand"
              icon-only
            />
            <v-button
              v-if="$acl.can('delete-halls')"
              @click="changeStatus(row)"
              class="ml-2"
              :variant="row.is_active ? 'label-danger' : 'label-success'"
            >
              {{ row.is_active ? 'إلغاء تفعيل' : 'تفعيل' }}
            </v-button>
          </div>
        </template>
      </v-table>
      <hall-form
        v-if="$acl.can('create-halls|update-halls')"
        :data="hallData"
        :active.sync="isModalActive"
        @save="save"
      />
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import VTable from '@/components/datatable/VTable'
import Card from '@/views/partials/content/Card.vue'
import HallForm from '@/views/pages/control-panel/halls/HallForm'

export default {
  name: 'Halls',
  components: { HallForm, Card, VTable },
  data() {
    return {
      headers: [
        { key: 'title', name: this.$t('halls.title'), sort: true },
        { key: 'entity', name: this.$t('halls.entity'), sort: true, expend: true },
        { key: 'capacity', name: this.$t('halls.capacity'), sort: true, expend: true },
        { key: 'is_active', name: 'مفعلة', expend: true },
        { key: 'actions', name: this.$t('table.actions') },
      ],
      loading: true,
      isModalActive: false,
      hallData: {},
      building: {},
      halls: [],
    }
  },
  computed: {
    datatable() {
      return this.halls.map(hall => ({
        id: hall.id,
        title: this.$utils.getCurrentOrFirstLang(hall.title),
        entity: this.entities.find(i => i.id === hall.entity_id)?.title || '',
        capacity: hall.capacity,
        is_active: hall.is_active,
      }))
    },
    building_title() {
      return this.building.title ? this.$utils.getCurrentOrFirstLang(this.building.title) : ''
    },
    building_id() {
      return this.$route.params.building_id
    },
    entities() {
      return this.$store.getters.subEntities
    },
  },
  methods: {
    openForm(id = -1) {
      let data = id === -1 ? {} : this.halls.find(f => f.id === id)
      this.hallData = JSON.parse(JSON.stringify(data))
      this.isModalActive = true
    },
    save(data) {
      const index = this.halls.findIndex(hall => hall.id === data.id)
      index !== -1 ? this.$set(this.halls, index, data) : this.halls.push(data)
    },
    changeStatus(hall) {
      this.$utils
        .confirm(`هل انت متأكد بأنك تريد ${hall.is_active ? 'إلغاء تفعيل' : 'تفعيل'} القاعة؟`)
        .then(({ value }) => {
          if (!value) return
          ApiService.delete(`buildings/${this.building_id}/halls`, hall.id)
            .then(() => {
              hall.is_active = !hall.is_active
              this.$utils.alert(this.$t('msg.success'), 'success')
            })
            .catch(() => {
              this.$utils.alert(this.$t('msg.error'), 'error')
            })
        })
    },
  },
  created() {
    ApiService.get(`buildings/${this.building_id}/halls`)
      .then(({ data }) => {
        this.building = data
        this.halls = this.building.halls || []
      })
      .catch(this.$utils.errorHandler)
      .finally(() => (this.loading = false))
  },
}
</script>

<style scoped></style>
