<template>
  <Card :title="!id ? $t('roles.add') : $t('roles.edit')" title-icon="far fa-rolespaper" :loading="pageLoading">
    <template slot="body">
      <form-validation ref="form" :loading="loading" @submit="onSubmit">
        <b-tabs>
          <b-tab v-for="lang in $store.getters.languages" :key="lang.id" :title="lang.title">
            <b-row class="multiple-languages" dir="auto">
              <text-input
                class="col-12"
                :rules="{ required: !Object.values(form.name).some(value => !!value) }"
                :vid="`name_${lang.id}`"
                name="name[]"
                :label="$t('roles.name', lang.id)"
                v-model="form.name[lang.id]"
              />
            </b-row>
          </b-tab>
        </b-tabs>
        <b-row cols="form-group">
          <label class="col-xl-2 col-lg-2 col-form-label font-weight-bold text-dark">
            مستويات الدور
          </label>
          <div class="col-lg-9 d-flex flex-wrap">
            <label
              v-for="level in levels"
              :key="level.value"
              class="my-3 mx-4 select-none font-weight-bolder text-dark kt-checkbox kt-checkbox--tick kt-checkbox--brand"
            >
              <input type="checkbox" :value="level.value" v-model="form.level" />
              {{ level.text }}
              <span></span>
            </label>
          </div>
        </b-row>
        <hr />
        <b-tabs>
          <b-tab :title="$t('permissions.control_panel')">
            <user-permissions-panel :permissions="controlPanelPermissions" v-model="form.permissions" />
          </b-tab>
          <b-tab :title="$t('permissions.control_website')">
            <user-permissions-panel :permissions="controlWebsitePermissions" v-model="form.permissions" />
          </b-tab>
          <b-tab :title="$t('permissions.control_sms')">
            <user-permissions-panel :permissions="controlSmsPermissions" v-model="form.permissions" />
          </b-tab>
        </b-tabs>
        <div class="d-flex justify-content-end">
          <b-button class="px-4 mr-2" to="./">{{ $t('form.back') }}</b-button>
          <v-button type="submit" variant="brand" wide :loading="loading">
            {{ id ? $t('form.save') : $t('form.add') }}
          </v-button>
        </div>
      </form-validation>
    </template>
  </Card>
</template>

<script>
import ApiService from '@/common/api.service'
import FormValidation from '@/components/form-controls/FormValidation'
import TextInput from '@/components/form-controls/TextInput'
import SelectInput from '@/components/form-controls/SelectInput'
import Card from '@/views/partials/content/Card.vue'
import FormService from '@/common/form.service'
import UserPermissionsPanel from '@/views/pages/control-panel/permissions/UserPermissionsPanel'

export default {
  name: 'RolesForm',
  components: {
    Card,
    SelectInput,
    TextInput,
    FormValidation,
    UserPermissionsPanel,
  },
  data() {
    return {
      pageLoading: true,
      loading: false,
      permissions: [],
      form: {
        name: { translate: true },
        level: [],
        permissions: [],
      },
    }
  },
  computed: {
    id() {
      return this.$route.params.role_id
    },
    levels() {
      return this.$store.getters.levels
    },
    controlPanelPermissions() {
      return this.permissions.filter(p => p.panel === 'control-panel')
    },
    controlWebsitePermissions() {
      return this.permissions.filter(p => p.panel === 'control-website')
    },
    controlSmsPermissions() {
      return this.permissions.filter(p => p.panel === 'sms')
    },
  },
  methods: {
    onSubmit() {
      this.loading = true

      this.request()
        .then(() => {
          this.$utils.alert(this.$t('msg.success'), 'success')
          this.$router.push({ name: 'roles' })
        })
        .catch(({ response: { data } }) => {
          this.$utils.alert(data.errors ? Object.values(data.errors)[0][0] : this.$t('msg.error'), 'error')
        })
        .finally(() => (this.loading = false))
    },
    request() {
      if (!this.id) {
        return ApiService.post('roles', this.form)
      } else {
        return ApiService.put('roles/' + this.id, this.form)
      }
    },
    splitPermissionOperations(permissions) {
      permissions.forEach(p => {
        const [opName, mName] = p.name[this.$lang].split('-')
        const management = this.permissions.find(tp => tp.managementName === mName)
        const operation = { value: p.id, text: opName }

        if (management) {
          management.operations.push(operation)
        } else {
          this.permissions.push({
            managementName: mName,
            operations: [operation],
            panel: p.panel_name,
            level: p.level,
          })
        }
      })
    },
  },
  async created() {
    await Promise.all([
      ApiService.query('permissions').then(({ data }) => {
        this.splitPermissionOperations(data.permissions)
        this.roles = data.roles
      }),
    ]).catch(this.$utils.errorHandler)

    this.id &&
      (await ApiService.get('roles', this.id)
        .then(({ data }) => {
          this.form = {
            id: data.id,
            name: data.name,
            level: data.level,
            permissions: data.permissions.map(p => p.id),
          }
        })
        .catch(this.$utils.errorHandlerWith404))

    FormService.setTranslations(this.form, this.$store.getters.languages)

    this.pageLoading = false
  },
}
</script>
