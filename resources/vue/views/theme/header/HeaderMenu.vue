<template>
  <ul class="kt-menu__nav">
    <template v-for="(menu, i) in menuItems">
      <KTMenuItem v-bind:menu="menu" :key="i"></KTMenuItem>
    </template>
  </ul>
</template>

<script>
import KTMenuItem from './MenuItem.vue'
import menuConfig from '@/common/config/menu.config.js'

export default {
  name: 'KTHeaderMenu',
  components: {
    KTMenuItem,
  },
  computed: {
    menuItems() {
      const panel = this.$route.matched[0].path.split('/')[1]
      return menuConfig[panel]?.header?.items
    },
  },
}
</script>
