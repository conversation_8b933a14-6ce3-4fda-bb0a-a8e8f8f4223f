<template>
    <div id="kt_header_mobile" class="kt-header-mobile" v-bind:class="headerClasses">
        <div class="kt-header-mobile__logo">
            <a href="/">
                <img alt="Logo" :src="`/${headerLogo}`" height="45" />
            </a>
            <h6 class="text-dark m-0 ml-4">{{ $store.getters.entity.title }}</h6>
        </div>
        <div class="kt-header-mobile__toolbar">
            <button
                v-if="asideEnabled"
                class="kt-header-mobile__toggler kt-header-mobile__toggler--left"
                id="kt_aside_mobile_toggler"
            >
                <span></span>
            </button>

            <!--            <button class="kt-header-mobile__toggler" id="kt_header_mobile_toggler">-->
            <!--                <span></span>-->
            <!--            </button>-->

            <button
                class="kt-header-mobile__topbar-toggler"
                id="kt_header_mobile_topbar_toggler"
                ref="kt_header_mobile_topbar_toggler"
            >
                <span>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="24px"
                        height="24px"
                        viewBox="0 0 24 24"
                        version="1.1"
                    >
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24" />
                            <path
                                d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z"
                                fill="#000000"
                                fill-rule="nonzero"
                                opacity="0.3"
                            />
                            <path
                                d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z"
                                fill="#000000"
                                fill-rule="nonzero"
                            />
                        </g>
                    </svg>
                </span>
            </button>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import KTToggle from '@/assets/js/toggle.js'

export default {
    name: 'KTHeaderMobile',
    components: {},
    mounted() {
        new KTToggle(this.$refs['kt_header_mobile_topbar_toggler'], {
            target: 'body',
            targetState: 'kt-header__topbar--mobile-on',
            togglerState: 'kt-header-mobile__toolbar-topbar-toggler--active',
        })
    },
    computed: {
        ...mapGetters(['layoutConfig', 'getClasses']),

        /**
         * Get header logo
         * @returns {string}
         */
        headerLogo() {
            return this.layoutConfig('self.logo.dark')
        },

        /**
         * Get classes for mobile header
         * @returns {null|*}
         */
        headerClasses() {
            const classes = this.getClasses('header_mobile')
            if (typeof classes !== 'undefined') {
                return classes.join(' ')
            }
            return null
        },

        /**
         * Check if the left aside menu is enabled
         * @returns {boolean}
         */
        asideEnabled() {
            return !!this.layoutConfig('aside.self.display')
        },
    },
}
</script>
