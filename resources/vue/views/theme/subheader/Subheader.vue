<template>
    <div class="kt-subheader kt-grid__item" id="kt_subheader">
        <div class="kt-container" v-bind:class="{ 'kt-container--fluid': widthFluid }">
            <div class="kt-subheader__main">
                <h3 class="kt-subheader__title">
                    <button
                        v-if="subHeaderAsideEnabled"
                        class="kt-subheader__mobile-toggle kt-subheader__mobile-toggle--left"
                        id="kt_subheader_mobile_toggle"
                    >
                        <span></span>
                    </button>
                    {{ title }}
                </h3>
                <div class="kt-subheader__breadcrumbs">
                    <router-link :to="$route.matched[0].path" class="kt-subheader__breadcrumbs-home"
                        ><i class="flaticon2-shelter"></i
                    ></router-link>

                    <template v-for="(breadcrumb, i) in breadcrumbs">
                        <span :key="`${i}-${breadcrumb.id}`" class="kt-subheader__breadcrumbs-separator"></span>
                        <router-link
                            v-if="breadcrumb.route"
                            :key="i"
                            :to="breadcrumb.route"
                            class="kt-subheader__breadcrumbs-link"
                        >
                            {{ breadcrumb.translate ? $t(breadcrumb.translate) : breadcrumb.title }}
                        </router-link>
                        <span class="kt-subheader__desc" :key="i" v-if="!breadcrumb.route">
                            {{ breadcrumb.translate ? $t(breadcrumb.translate) : breadcrumb.title }}
                        </span>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    name: 'KTSubheader',
    props: {
        breadcrumbs: Array,
    },
    watch: {
        $route: 'setBreadcrumb',
    },
    computed: {
        ...mapGetters(['layoutConfig']),

        /**
         * Check if subheader width is fluid
         */
        title() {
            let last = this.breadcrumbs[this.breadcrumbs.length - 1]
            if (last && (last.title || last.translate)) {
                return last.translate ? this.$t(last.translate) : last.title
            } else return ''
        },
        widthFluid() {
            return this.layoutConfig('subheader.width') === 'fluid'
        },
        subHeaderAsideEnabled() {
            return !!this.layoutConfig('subheader.aside')
        },
    },
    methods: {
        setBreadcrumb() {
            if (this.$route.meta && this.$route.meta.breadcrumbs) {
                this.$store.dispatch('setBreadcrumb', this.$route.meta.breadcrumbs)
            }
        },
    },
    mounted() {
        this.setBreadcrumb()
    },
}
</script>
