<template>
  <!-- begin:: Header Topbar -->
  <div class="kt-header__topbar">
    <!--begin: Search -->
    <!--        <div class="kt-header__topbar-item kt-header__topbar-item&#45;&#45;search" id="kt_quick_search_toggle">-->
    <!--            <div class="kt-header__topbar-wrapper" data-toggle="dropdown">-->
    <!--                <span class="kt-header__topbar-icon">-->
    <!--                    <img svg-inline class="kt-svg-icon" src="/assets/media/icons/svg/General/Search.svg" alt="" />-->
    <!--                </span>-->
    <!--            </div>-->
    <!--            <div-->
    <!--                class="dropdown-menu dropdown-menu-fit dropdown-menu-lg"-->
    <!--                :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"-->
    <!--                v-on:click.stop-->
    <!--            >-->
    <!--                <KTSearchDefault></KTSearchDefault>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        &lt;!&ndash;end: Search &ndash;&gt;-->

    <!--        &lt;!&ndash;begin: Notifications &ndash;&gt;-->
    <!--        <div class="kt-header__topbar-item" id="kt_notification_toggle">-->
    <!--            <div class="kt-header__topbar-wrapper" data-toggle="dropdown">-->
    <!--                <span class="kt-header__topbar-icon kt-pulse kt-pulse&#45;&#45;brand">-->
    <!--                    <img svg-inline class="kt-svg-icon" src="/assets/media/icons/svg/Code/Compiling.svg" alt="" />-->
    <!--                    <span class="kt-pulse__ring"></span>-->
    <!--                </span>-->
    <!--            </div>-->
    <!--            <div-->
    <!--                class="dropdown-menu dropdown-menu-fit dropdown-menu-xl"-->
    <!--                :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"-->
    <!--                v-on:click.stop-->
    <!--            >-->
    <!--                <form>-->
    <!--                    <KTDropdownNotification></KTDropdownNotification>-->
    <!--                </form>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        &lt;!&ndash;end: Notifications &ndash;&gt;-->

    <!--        &lt;!&ndash;begin: Quick Actions &ndash;&gt;-->
    <!--        <div class="kt-header__topbar-item">-->
    <!--            <div class="kt-header__topbar-wrapper" id="kt_quick_action_toggle" data-toggle="dropdown">-->
    <!--                <span class="kt-header__topbar-icon">-->
    <!--                    <img svg-inline class="kt-svg-icon" src="/assets/media/icons/svg/Media/Equalizer.svg" alt="" />-->
    <!--                </span>-->
    <!--            </div>-->
    <!--            <div-->
    <!--                class="dropdown-menu dropdown-menu-fit dropdown-menu-xl"-->
    <!--                :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"-->
    <!--                v-on:click.stop-->
    <!--            >-->
    <!--                <KTDropdownQuickAction></KTDropdownQuickAction>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        &lt;!&ndash;end: Quick Actions &ndash;&gt;-->

    <!--        &lt;!&ndash;begin: My Cart &ndash;&gt;-->
    <!--        <div class="kt-header__topbar-item">-->
    <!--            <div class="kt-header__topbar-wrapper" id="kt_my_cart_toggle" data-toggle="dropdown">-->
    <!--                <span class="kt-header__topbar-icon">-->
    <!--                    <img svg-inline class="kt-svg-icon" src="/assets/media/icons/svg/Shopping/Cart3.svg" alt="" />-->
    <!--                </span>-->
    <!--            </div>-->
    <!--            <div-->
    <!--                class="dropdown-menu dropdown-menu-fit dropdown-menu-xl"-->
    <!--                :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"-->
    <!--                v-on:click.stop-->
    <!--            >-->
    <!--                <KTDropdownMyCart></KTDropdownMyCart>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        &lt;!&ndash;end: My Cart &ndash;&gt;-->

    <!--        &lt;!&ndash;begin: Quick panel toggler &ndash;&gt;-->
    <!--        <div-->
    <!--            class="kt-header__topbar-item kt-header__topbar-item&#45;&#45;quick-panel"-->
    <!--            v-b-tooltip.hover.bottom="'Quick panel'"-->
    <!--        >-->
    <!--            <span class="kt-header__topbar-icon" id="kt_quick_panel_toggler_btn">-->
    <!--                <img svg-inline class="kt-svg-icon" src="/assets/media/icons/svg/Layout/Layout-4-blocks.svg" alt="" />-->
    <!--            </span>-->
    <!--        </div>-->
    <!--        &lt;!&ndash;end: Quick panel toggler &ndash;&gt;-->

    <!--begin: Language bar -->
    <!-- <div class="kt-header__topbar-item kt-header__topbar-item--langs">
            <div class="kt-header__topbar-wrapper" id="kt_language_toggle" data-toggle="dropdown">
                <span class="kt-header__topbar-icon">
                    <img :src="language || getLanguage" alt="lang" />
                </span>
            </div>
            <div
                class="dropdown-menu dropdown-menu-fit"
                :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"
                v-on:click.stop
            >
                <KTDropdownLanguage v-on:language-changed="onLanguageChanged"></KTDropdownLanguage>
            </div>
        </div> -->
    <!--end: Language bar -->

    <!--begin: User Bar -->
    <div class="kt-header__topbar-item kt-header__topbar-item--user">
      <div class="kt-header__topbar-wrapper" id="kt_user_toggle" data-toggle="dropdown">
        <div class="kt-header__topbar-user">
          <span class="kt-header__topbar-username kt-hidden-mobile">{{ $store.getters.userShortName }}</span>
          <img alt="Pic" :src="$store.getters.auth.image" />
        </div>
      </div>
      <div
        class="dropdown-menu dropdown-menu-fit dropdown-menu-xl"
        :class="{ 'dropdown-menu-right': !$store.state.config.rtl }"
      >
        <!--                <router-link class="dropdown-item" to="/profile">الصفحة الشخصية</router-link>-->
        <KTDropdownUser></KTDropdownUser>
      </div>
    </div>
    <!--end: User Bar -->
  </div>
  <!-- end:: Header Topbar -->
</template>

<script>
import KTDropdownLanguage from './DropdownLanguage.vue'
import KTDropdownUser from './DropdownUser.vue'
import i18nService from '@/common/i18n.service.js'

export default {
  name: 'KTTopbar',
  data() {
    return {
      language: '',
      languages: i18nService.languages,
    }
  },
  components: {
    KTDropdownLanguage,
    KTDropdownUser,
  },
  methods: {
    onLanguageChanged() {
      this.language = this.languages.find(val => {
        return val.lang === i18nService.getActiveLanguage()
      }).flag
    },
  },
  computed: {
    getLanguage() {
      return this.onLanguageChanged()
    },
  },
}
</script>
