<script>
export default {
  data() {
    return {
      currentPage: 1,
      maxItems: 20,
      sortKey: null,
      sortType: null,
      total: 0,
      search: '',
      searchTimeout: null,
    }
  },
  computed: {
    filterData() {
      return {
        sort_key: this.sortType ? this.sortKey : null,
        sort_type: this.sortType ? this.sortType : 'desc',
        page: this.currentPage,
        search: this.search,
        per_page: this.maxItems,
      }
    },
  },
  methods: {
    changeSort(key, type) {
      this.sortKey = key
      this.sortType = type
      this.loadData()
    },
    changeSearch(value) {
      this.search = value
      clearTimeout(this.searchTimeout)
      this.searchTimeout = setTimeout(() => {
        // this.$router.replace({ query: { search: this.search } }).catch(() => { })

        this.loadData()
        this.currentPage = 1
        this.searchTimeout = null
      }, 500)
    },
    changePage(page) {
      if (this.searchTimeout) return
      this.currentPage = page
      this.loadData()
    },
    changeMaxItem(maxItems) {
      this.maxItems = maxItems
      this.loadData()
    },
  },
}
</script>
