<script>
export default {
  data() {
    const query = this.$route.query
    return {
      currentPage: +query.page || 1,
      maxItems: +query.per_page || 20,
      sortKey: query.sort_key || undefined,
      sortType: query.sort_type || undefined,
      total: 0,
    }
  },
  computed: {
    filterData() {
      return {
        page: this.currentPage,
        per_page: this.maxItems,
        sort_key: this.sortType ? this.sortKey : undefined,
        sort_type: this.sortType ? this.sortType : 'desc',
      }
    },
  },
  methods: {
    changeSort(key, type) {
      if (this.sortKey === key && this.sortType === type) return
      this.sortKey = key
      this.sortType = type
      this.$router.replace({ query: { ...this.$route.query, ...this.filterData } })
    },
    changePage(page) {
      if (page === this.currentPage) return
      this.currentPage = page
      this.$router.replace({ query: { ...this.$route.query, ...this.filterData } })
    },
    changeMaxItem(maxItems) {
      if (this.maxItems === maxItems) return
      this.maxItems = maxItems
      this.$router.replace({ query: { ...this.$route.query, ...this.filterData, page: 1 } })
    },
  },
}
</script>
