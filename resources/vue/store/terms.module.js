import ApiService from '@/common/api.service'
import AclService from '@/common/acl.service'

export default {
  state: {
    activeTerms: [],
  },
  mutations: {
    setActiveTerms(state, data) {
      state.activeTerms = data.term
    },
  },
  actions: {
    getActiveTerms({ commit, state }) {
      return new Promise(resolve => {
        if (state.activeTerms?.length > 0) return resolve(state.activeTerms)
        ApiService.query('sms/stages-statuses')
          .then(({ data }) => {
            AclService.setStages(data.term)
            commit('setActiveTerms', data)
            resolve(data.term)
          })
          .catch(err => {
            console.log(err)
            resolve(null)
          })
      })
    },
  },
}
