import ApiService from '@/common/api.service'

export default {
  state: {
    nationalities: [],
    qualifications: [],
    academicRanks: [],
    newsCategories: [],
    eventCategories: [],
    albumCategories: [],
    entityTypes: [],
    publicationTypes: [],
    martialStatus: [],
    languages: [],
    links: [],
    journalPositions: [],
    terms: [],
    termStages: [],
    noticeTypes: [],
    lang: localStorage.getItem('language') || 'ar',
    levels: [
      { value: 'university', text: 'جامعة' },
      { value: 'school', text: 'كلية' },
      { value: 'department', text: 'قسم' },
    ],
    studyPlans: [
      { value: 'semester', text: 'فصل' },
      { value: 'year', text: 'سنة' },
    ],
    days: [
      { id: 1, title: 'السبت' },
      { id: 2, title: 'الأحد' },
      { id: 3, title: 'الأثنين' },
      { id: 4, title: 'الثلاثاء' },
      { id: 5, title: 'الاربعاء' },
      { id: 6, title: 'الخميس' },
      { id: 7, title: 'الجمعة' },
    ],
    jobLevels: [
      { id: 1, title: 'الدرجة الاولى' },
      { id: 2, title: 'الدرجة الثانية' },
      { id: 3, title: 'الدرجة الثالثة' },
      { id: 4, title: 'الدرجة الرابعة' },
      { id: 5, title: 'الدرجة الخامسة' },
      { id: 6, title: 'الدرجة السادسة' },
      { id: 7, title: 'الدرجة السابعة' },
      { id: 8, title: 'الدرجة الثامنة' },
      { id: 9, title: 'الدرجة التاسعة' },
      { id: 10, title: 'الدرجة العاشرة' },
      { id: 11, title: 'الدرجة الحادية عشر' },
      { id: 12, title: 'الدرجة الثانية عشر' },
      { id: 13, title: 'الدرجة الثالثة عشر' },
      { id: 14, title: 'الدرجة الرابعة عشر' },
      { id: 15, title: 'الدرجة الخامسة عشر' },
    ],
    studentTermStatus: ['default', 'renewal', 'discontinuous', 'suspended registration', 'discontinuous for notice'],
    stagesEnum: {
      enrollment: 1,
    },
  },
  getters: {
    days: state => state.days,
    entityTypes(state) {
      return state.entityTypes
    },
    nationalities(state) {
      return state.nationalities
    },
    qualifications(state) {
      return state.qualifications
    },
    academicRanks(state) {
      return state.academicRanks
    },
    languages(state) {
      return state.languages.sort(lang => (lang.id === state.lang ? -1 : 0))
    },
    newsCategories(state) {
      return state.newsCategories
    },
    eventCategories(state) {
      return state.eventCategories
    },
    albumCategories(state) {
      return state.albumCategories
    },
    publicationTypes(state) {
      return state.publicationTypes
    },
    martialStatus(state) {
      return state.martialStatus
    },
    links(state) {
      return state.links
    },
    journalPositions(state) {
      return state.journalPositions
    },
    terms(state) {
      return state.terms
    },
    termStages(state) {
      return state.termStages
    },
    studentTermStatus(state) {
      return state.studentTermStatus
    },
    noticeTypes(state) {
      return state.noticeTypes
    },
    jobLevels(state) {
      return state.jobLevels
    },
    studyPlans(state) {
      return state.studyPlans
    },
    levels(state) {
      return state.levels
    },
  },
  mutations: {
    setEntityTypes(state, data) {
      state.entityTypes = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setNationalities(state, data) {
      state.nationalities = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setQualifications(state, data) {
      state.qualifications = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setAcademicRanks(state, data) {
      state.academicRanks = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setLanguages(state, data) {
      state.languages = data
    },
    setNewsCategories(state, data) {
      state.newsCategories = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setEventCategories(state, data) {
      state.eventCategories = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setAlbumCategories(state, data) {
      state.albumCategories = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setPublicationTypes(state, data) {
      state.publicationTypes = data.map(item => {
        item.title = item.title[state.lang]
        return item
      })
    },
    setMartialStatus(state, data) {
      state.martialStatus = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setLinks(state, data) {
      state.links = data.map(item => ({ id: item.id, title: item.title[state.lang], icon: item.icon }))
    },
    setJournalPositions(state, data) {
      state.journalPositions = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setTermStages(state, data) {
      state.termStages = data.map(item => ({ id: item.id, title: item.title[state.lang] }))
    },
    setTerms(state, data) {
      state.terms = data
    },
    setNoticeTypes(state, data) {
      state.noticeTypes = data
    },
  },
  actions: {
    async getEntityTypes({ state, commit }) {
      if (state.entityTypes.length) return
      const { data } = await ApiService.query('entity-types')
      return commit('setEntityTypes', data)
    },
    async getNationalities({ state, commit }) {
      if (state.nationalities.length) return
      const { data } = await ApiService.query('nationalities')
      return commit('setNationalities', data)
    },
    async getQualifications({ state, commit }) {
      if (state.qualifications.length) return
      const { data } = await ApiService.query('qualifications')
      return commit('setQualifications', data)
    },
    async getAcademicRanks({ state, commit }) {
      if (state.academicRanks.length) return
      const { data } = await ApiService.query('academic-ranks')
      return commit('setAcademicRanks', data)
    },
    async getLanguages({ state, commit }) {
      if (state.languages.length) return
      const { data } = await ApiService.query('languages')
      return commit('setLanguages', data)
    },
    async getNewsCategories({ state, commit }) {
      if (state.newsCategories.length) return
      const { data } = await ApiService.query('news-categories')
      return commit('setNewsCategories', data)
    },
    async getEventCategories({ state, commit }) {
      if (state.eventCategories.length) return
      const { data } = await ApiService.query('event-categories')
      return commit('setEventCategories', data)
    },
    async getAlbumCategories({ state, commit }) {
      if (state.albumCategories.length) return
      const { data } = await ApiService.query('album-categories')
      return commit('setAlbumCategories', data)
    },
    async getPublicationTypes({ state, commit }) {
      if (state.publicationTypes.length) return
      const { data } = await ApiService.query('publication-types')
      return commit('setPublicationTypes', data)
    },
    async getMartialStatus({ state, commit }) {
      if (state.martialStatus.length) return
      const { data } = await ApiService.query('martial-statuses')
      return commit('setMartialStatus', data)
    },
    async getLinks({ state, commit }) {
      if (state.links.length) return
      const { data } = await ApiService.query('links')
      return commit('setLinks', data)
    },
    async getJournalPositions({ state, commit }) {
      if (state.journalPositions.length) return
      const { data } = await ApiService.query('journal-positions')
      return commit('setJournalPositions', data)
    },
    async getTermStages({ state, commit }) {
      if (state.termStages.length) return
      const { data } = await ApiService.query('term-stages')
      return commit('setTermStages', data)
    },
    async getTerms({ state, commit }) {
      if (state.terms.length) return
      const { data } = await ApiService.query('terms')
      return commit('setTerms', data)
    },
    async getNoticeTypes({ state, commit }) {
      if (state.noticeTypes.length) return
      const { data } = await ApiService.query('sms/notice-types')
      return commit('setNoticeTypes', data)
    },
  },
}
