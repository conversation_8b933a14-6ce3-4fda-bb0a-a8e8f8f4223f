import ApiService from '@/common/api.service'

export default {
  namespaced: true,
  state: {
    lecturers: [],
    buildings: [],
    lang: localStorage.getItem('language') || 'ar',
    thesisProposalStatuses: ['pending', 'rejected', 'accepted'],
    thesisGrades: ['passed', 'passed but require updates', 'rediscussion', 'failed'],
  },
  getters: {
    lecturers(state) {
      return state.lecturers
    },
    buildings(state) {
      return state.buildings
    },
    thesisProposalStatuses(state) {
      return state.thesisProposalStatuses
    },
    thesisGrades(state) {
      return state.thesisGrades
    },
  },
  mutations: {
    setLecturers(state, lecturers) {
      state.lecturers = lecturers.map(item => ({ id: item.id, title: item['name_' + state.lang] }))
    },
    setBuildings(state, buildings) {
      state.buildings = buildings
    },
  },
  actions: {
    async getLecturers({ state, commit }, payload) {
      if (state.lecturers.length && !payload?.force) return state.lecturers
      const { data } = await ApiService.query('sms/lecturers?status=1')
      commit('setLecturers', data)
      return data
    },
    async getBuildings({ state, commit }, payload) {
      if (state.buildings.length && !payload?.force) return state.buildings
      const { data } = await ApiService.query('sms/buildings')
      commit('setBuildings', data)
      return data
    },
  },
}
