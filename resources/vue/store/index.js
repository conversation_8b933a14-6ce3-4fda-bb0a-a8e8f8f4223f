import Vue from 'vue'
import Vuex from 'vuex'

import auth from './auth.module'
import htmlClass from './htmlclass.module'
import config from './config.module'
import breadcrumbs from './breadcrumbs.module'
import entities from './entities.module'
import programs from './programs.module'
import staticData from './staticdata.module'
import buildings from './buildings.module'
import terms from './terms.module'
import sms from './sms.module'
import students from './students.module'
import student from './student.module'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    auth,
    htmlClass,
    config,
    entities,
    programs,
    staticData,
    buildings,
    breadcrumbs,
    terms,
    sms,
    students,
    student,
  },
})
