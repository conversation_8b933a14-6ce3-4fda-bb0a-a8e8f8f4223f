import Vue from 'vue'
import axios from 'axios'
import VueAxios from 'vue-axios'

/**
 * Service to call HTTP request via Axios
 */
const ApiService = {
  init() {
    Vue.use(VueAxios, axios)
    Vue.axios.defaults.baseURL = '/api/'
    Vue.axios.defaults.withCredentials = true
    axios.defaults.headers.accept = 'application/json'
    axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest'
    // Vue.axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8;multipart/form-data'

    axios.interceptors.response.use(
      response => response,
      error => {
        if (
          error.response?.status === 401 &&
          error.response?.config?.url &&
          !['login', 'login/students'].includes(error.response?.config?.url)
        ) {
          window.location.href = '/'
        }
        return Promise.reject(error)
      }
    )
  },

  cancelToken() {
    return Vue.axios.CancelToken
  },

  query(resource, params) {
    return Vue.axios.get(resource, { params: params })
  },

  get(resource, slug = '') {
    return Vue.axios.get(`${resource}${slug && '/'}${slug}`)
  },

  post(resource, params, config) {
    return Vue.axios.post(`${resource}`, params, config)
  },

  update(resource, slug, params) {
    return Vue.axios.put(`${resource}/${slug}`, params)
  },

  put(resource, params) {
    return Vue.axios.put(`${resource}`, params)
  },

  delete(resource, slug = '') {
    return Vue.axios.delete(`${resource}${slug && '/'}${slug}`)
  },
}

export default ApiService
