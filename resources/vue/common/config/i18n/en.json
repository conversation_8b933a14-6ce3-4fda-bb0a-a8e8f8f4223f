{"actions": {"delete": "", "edit": ""}, "administrators": {"confirm_remove_permissions": "Are you sure you want to remove permissions from this user?", "manage": "Manage administrators", "remove": "Remove permissions"}, "albums": {"add": "Add Album", "confirm_delete": "Are you sure you want to delete the album?", "date": "Date", "edit": "Edit Album", "images": "Album images", "manage": "Manage Albums", "required_images": "The album must contain at least one image.", "title": "Title", "type": "Type"}, "announcements": {"add": "Add Announcement", "change_status": "Cancel publishing | Publish", "change_status_confirm": "Are you sure you want to cancel publishing the announcement in {entity}? | Are you sure you want to publish the announcement in {entity}?", "confirm_delete": "Are you sure you want to delete the announcement", "content": "Content", "date": "Date", "edit": "Edit Announcement", "entity": "Entity", "expiration_date": "Expiration date", "image": "Announcement image", "manage": "Manage Announcements", "publish": "Publish announcement", "publish_requests": "Publish requests", "required_image": "The announcement must contain an image.", "targets": "Targets", "title": "Title"}, "articles": {"abstract": "Abstract", "add": "Add Articles", "add_another_author": "Add another author", "add_author_desc": "You can enter more than one author by \"Add another author\"", "author_affiliation": "Author affiliation", "author_email": "Author <PERSON><PERSON>", "author_name": "Author name", "authors": "Authors", "authors_number": "Number of authors", "confirm_delete": "Are you sure you want to delete the article?", "content": "Content", "cover": "Article cover", "date": "Date", "edit": "Edit Articles", "file": "Article file", "first_author": "First author", "issue_volume": "Volume {vol}, Issue {issue}", "lang": "Article language", "manage": "Manage Articles", "page_from": "From page", "page_to": "To page", "pages": "Pages", "required_cover": "The Article must contain a cover image.", "title": "Title"}, "buildings": {"add": "Add building", "confirm_delete": "Are you sure you want to delete the building?", "description": "Description", "edit": "Edit buildings", "latitude": "Latitude", "longitude": "Longitude", "manage": "Manage buildings", "mark_location": "Locate the place on the map", "required_location": "The building must be located on the map", "title": "Title", "view_halls": "View halls"}, "copyright": "© {year} All rights reserved to University of Gharyan, Powered by Esnadcom company", "department": "Department", "dir": "ltr", "editorials": {"add": "Add editorial", "affiliation": "Affiliation", "confirm_delete": "Are you sure you want to delete the editorial?", "edit": "Edit editorial", "email": "Email", "image": "Editorial photo", "manage": "Manage editorials", "name": "Full name", "position": "editorial position", "required_cover": "The editorial must contain an photo."}, "employee": {"confirm_change_status": "Are you sure you want to {status} account \"{name}\"?"}, "employees": {"add": "Add Employee", "agency": "Agency", "confirm_change_status": "Are you sure you want to {status} account \"{name}\"", "confirm_delete": "Are you sure you want to delete the employee?", "delete": "Delete", "department": "Department", "edit": "Edit Employee", "manage": "Manage employees", "name": "Name"}, "entities": {"add": "Add Entity", "buildings": "Buildings", "campus": "Campus", "confirm_delete": "Are you sure you want to delete the entity?", "cover": "Cover", "delete": "Delete Entity", "description": "Description", "details": "Entity details", "domain": "Domain", "edit": "Edit En<PERSON>", "email": "E-mail", "entity_head": "Dean/Head of Department", "goals": "Goals", "head_word": "Head word", "links": "Links", "main_info": "Main info", "manage": "Manage Entities", "message": "Mission", "mission": "Mission", "name": "Name", "phone": "Phone", "type": "Type", "values": "Values", "vision": "Vision"}, "events": {"add": "Add Event", "confirm_delete": "Are you sure you want to delete the event?", "content": "Content", "date": "Date", "edit": "Edit Event", "end_date": "End date", "end_time": "End time", "event_date": "Event date", "event_time": "Event time", "image": "Event image", "latitude": "Latitude", "longitude": "Longitude", "manage": "Manage Events", "place": "Place", "required_image": "The event must contain an image.", "start_date": "Start date", "start_time": "Start time", "time": "Time", "title": "Title", "type": "Type"}, "facts": {"add": "Add Fact", "confirm_delete": "Are you sure you want to delete the fact?", "edit": "Edit Fact", "manage": "Manage Facts", "number": "Number", "title": "Title"}, "file_uploader": {"cancel_all": "Cancel All", "upload_file": "Upload File | Upload Files"}, "form": {"add": "Add", "back": "Back", "delete": "", "save": "Save", "save_changes": "Save changes"}, "guides": {"add": "Add guide", "confirm_delete": "Are you sure you want to delete the guide?", "edit": "Edit guide", "file": "Guide file", "manage": "Manage guides", "required_file": "You must upload guide file", "title": "Title"}, "halls": {"add": "Add Hall", "building": "Building", "campus": "Campus", "capacity": "Capacity", "confirm_delete": "Are you sure you want to delete the hall?", "edit": "Edit Hall", "entity": "", "manage": "Manage Halls", "manage_in_building": "Manage halls in {building}", "title": "Title"}, "image_uploader": {"choice_image": "Browse image", "or_drop_here": "or drop here."}, "issues": {"add": "Add issue", "confirm_delete": "Are you sure you want to delete the issue?", "cover": "Cover", "delete": "Delete issue", "edit": "Edit issue", "issue": "Issue", "manage": "Manage issues", "month": "Month", "required_cover": "The issue must contain an image.", "view_articles": "View articles", "volume": "Volume", "year": "Year"}, "journals": {"about": "About journal", "add": "Add journal", "author_guidelines": "Author guidelines", "confirm_delete": "Are you sure you want to delete the journal?", "cover": "Journal cover", "delete": "Delete", "description": "Description", "domain": "Journal Domain", "edit": "Edit journal", "email": "Email", "manage": "Manage journals", "name": "Journal name", "phone": "Phone", "publication_rules": "Publication rules", "required_cover": "The journal must contain an image."}, "lecturer-panel": "Lecturer Panel", "lecturers": {"add": "Add Lecturer", "confirm_change_status": "Are you sure you want to {status} account \"{name}\"", "confirm_delete": "Are you sure you want to delete the lecturer?", "cooperative": "Cooperative", "delete": "Delete", "department": "Department", "edit": "Edit Lecturer", "manage": "Manage lecturers", "name": "Name", "permanent": "permanent", "type": "Type"}, "menu": {"administrators": "Administrators", "albums": "Albums", "announcements": "Announcements", "articles": "Articles", "buildings": "Buildings", "editorials": "Editorials", "employees": "Employees", "entities": "Entities", "events": "Events", "facts": "Facts", "guides": "Guides", "halls": "Halls", "home": "Home", "issues": "Issues", "journal_details": "Journal details", "journals": "Journals", "lecturers": "Lecturers", "news": "News", "program_types": "", "programs": "Programs", "publications": "Publications", "roles": "", "sliders": "Sliders", "students": "Students", "videos": "Videos"}, "msg": {"error": "Something went wrong, please try again!", "success": "The operation completed successfully", "uploading_image_error": "An error occurred while uploading the image. Please try again!"}, "news": {"add": "Add New", "cancel_publishing": "Cancel publishing", "change_status_confirm": "Are you sure you want to cancel publishing the new? | Are you sure you want to publish the new?", "comments_on_news": "Comment on the news", "confirm_delete": "Are you sure you want to delete the new?", "content": "Content", "date": "Date", "edit": "Edit New", "images": "News images", "manage": "Manage News", "not_published": "Not published", "published": "Published", "publishing": "Publishing", "required_images": "The news must contain at least one image!", "status": "Status", "title": "Title", "type": "News type"}, "panel": {"sms": "SMS"}, "permissions": {"check_all": "Check All", "control_panel": "Main Control panel", "control_sms": "SMS", "control_website": "Website Control panel", "entities": "Entities", "success_msg": "\"{name}\" permissions in the {entity} have been saved successfully", "uncheck_all": "Uncheck All", "user_permissions": "User Permissions", "user_permissions_in_entity": "Permissions in {entity}"}, "profile": {"change_password": "Change password", "confirm_password": "Confirm password", "contact_info": "Contact info", "current_password": "Current password", "image": "Profile image", "job_info": "Job info", "links": "Social links", "personal_information": "Personal info", "publications": "Publications", "student_subjects": "Subjects", "view_in_website": "View profile in website"}, "programs": {"add": "Add Program", "booklet": "Program booklet", "certificate_awarded": "Certificate awarded", "compulsory_credits": "Compulsory credits", "confirm_delete": "Are you sure you want to delete the program?", "delete": "Delete Program", "department": "Department", "description": "Description", "duration": "Duration", "duration_unit": "Duration unit", "edit": "Edit Program", "elective_credits": "Elective credits", "entry_requirements": "Entry requirements", "general_credits": "General credits", "image": "Program image", "job_market": "Job market", "lang": "Program language", "major": "Major", "manage": "Manage Programs", "objectives": "Objectives", "outcomes": "Outcomes", "required_booklet": "The program should contain booklet.", "required_image": "The program must contain an image.", "supportive_credits": "Supportive credits", "title": "Name", "total_credits": "Total credits", "training_credits": "Training credits", "type": "Academic degree", "view": "View Programs", "view_subjects": "View subjects", "weakly_lab_hours": "Weakly lab hours", "weakly_teaching_hours": "Weakly teaching hours"}, "publications": {"abstract": "Abstract", "add": "Add Publication", "add_another_author": "Add another author", "add_author_desc": "You can enter more than one author by \"Add another author\"", "all": "All", "already_exists": "This publication already exists", "author": "Author", "author_already_exists": "This author is already exists !", "author_name_in_publication": "Authors name in the publication", "check_copyright": "Check the copyright before making the file publicly available", "choice_author": "Choice author", "choice_author_desc": "Select the author if he is in the academy", "confirm_delete": "Are you sure you want to delete the publication", "date": "Date", "download": "Download", "edit": "Edit Publication", "file": "Publication File", "first_author": "First author", "from_pages": "From pages", "lang": "Publication language", "link": "Publication link", "location": "Publication place", "manage": "Manage Publications", "month": "Month", "not_authorized": "You cannot add a publication that you are not one of the authors", "number": "Number", "number_of_authors": "Number of authors", "only_in_university": "Academic staff only", "pages": "Pages", "publisher": "Publisher", "publisher_desc": "If you do not find the publisher you want within the options, you can write it and then choose it.", "title": "Title", "to_pages": "To pages", "type": "Publication type", "volume": "Volume", "who_can_view_file": "Who can view the file", "year": "year"}, "roles": {"add": "Add Role", "confirm_delete": "Are you sure you want to delete the role?", "edit": "Edit Role", "level": "Level", "manage": "Manage Roles", "name": "Role", "panel_name": "Panel name"}, "school": "School", "sliders": {"add": "<PERSON><PERSON>", "button_link": "Button link", "button_title": "Button title", "confirm_delete": "Are you sure you want to delete the slider?", "edit": "edit <PERSON><PERSON><PERSON>", "image": "Slider image", "lang": "Language", "manage": "Manage sliders", "required_image": "The slider must contain an image!", "subtitle": "Subtitle", "title": "Title"}, "statistics": {"all": "All", "cooperative": "Cooperative", "cooperatives_groups_count": "", "female": "Female", "groups_count": "Group count", "libyans": "Libyans", "male": "Male", "non_libyans": "Non libyans", "permanent": "Permanent", "permanents_groups_count": "", "renewed": "Renewed", "renewed_libyan_students": "", "renewed_non_libyan_students": "", "total": "Total"}, "status": {"active": "Active", "auto": "Auto", "inactive": "Inactive", "suspended": "Suspended"}, "student_status": {"default": "<PERSON><PERSON><PERSON>", "discontinuous": "Discontinuous", "discontinuous for notice": "Discontinuous for notice", "renewal": "Renewal", "suspended registration": "Suspended registration"}, "study_type": "Study Type", "subjects": {"add": "Add Subject", "add_from_other_program": "Add subject from another program", "code": "Code", "code_desc": "The code must consist of letters and numbers only (use a period instead of a space)\"", "confirm_delete": "Are you sure you want to delete the subject  \"{code}\"?", "credits": "Credits", "description": "Description", "edit": "Edit Subject", "final_mark": "Final_mark", "mid_mark": "Mid mark", "pass_mark": "Pass_mark", "prerequisites": "Prerequisites", "sub_semester": "<PERSON><PERSON><PERSON>", "subjects_in_other_program": "{program} subject from another program", "subjects_in_program": "{program} subjects", "title": "Name", "type": "Type", "weekly_lecture_hours": "weekly_lecture_hours", "weekly_tutorial_hours": "weekly_tutorial_hours"}, "table": {"actions": "Actions", "active": "Active", "delete": "Delete", "edit": "Edit", "from": "of", "more": "More", "no_data": "No data", "print": "Print", "search": "Search", "title": "", "view": "Showing"}, "targets": {"employees": "Employees", "lecturers": "Lecturers", "public": "Public", "students": "Students"}, "terms": {"add": "", "confirm_delete": "", "edit": "", "manage": "", "seasons": {"fall": "", "spring": "", "summer": "", "undefined": ""}, "stages": {"add": "", "confirm_delete": "", "edit": "", "end_at": "", "manage": "", "start_at": "", "status": "", "statuses": {"active": "", "auto": "", "suspended": ""}, "title": ""}, "status": "", "statuses": {"active": "", "completed": "", "pending": "", "suspended": ""}, "title": "", "year": ""}, "theses": {"grades": {"failed": "عدم إجازة الرسالة", "passed": "إجازة الرسالة بدون تعديلات", "passed but require updates": "", "passed but requires updates": "إجازة الرسالة شرط إجراء التعديلات المرفقة", "rediscussion": "إعادة المناقشة"}, "statuses": {"accepted": "Accepted", "pending": "Pending", "rejected": "Rejected"}}, "timetables": {"lectures": {"confirm_delete": ""}, "manage": ""}, "university": "University", "user_type": {"employee": "Employee", "lecturer": "Lecturer", "student": "Student"}, "users": {"academic_info": "Academic information", "academic_rank": "Academic rank", "account_access_info": "Account access information", "active": "Active", "address": "Address", "basic_info": "Basic information", "bio": "Bio", "birthday": "Birthday", "change_password": "Change Password", "confirm_password": "Confirm password", "current_password": "Current password", "cv": "CV", "department": "Department", "email": "E-mail", "female": "Female", "first_name": "First name", "gender": "Gender", "general_major": "General major", "job_title": "Job title", "job_title_desc": "users.job_title_desc", "last_name": "Last name", "logout": "Logout", "male": "Male", "name_ar": "Name [Arabic]", "name_en": "Name [English]", "national_id": "National ID", "nationality": "Nationality", "passport": "Passport", "password": "Password", "password_confirmation": "Confirm password", "permissions": "Permissions", "personal_website": "Personal website", "phone": "Phone", "profile": "Profile", "qualifications": "Qualifications", "second_name": "Second name", "secondary_info": "Secondary information", "specialization": "Specialization", "start_job_date": "Date of hiring", "status": "Status", "suspended": "Suspended", "third_name": "Third name", "website": "Website"}, "videos": {"add": "Add Video", "confirm_delete": "Are you sure you want to delete the video?", "edit": "Edit Video", "link": "Link", "manage": "Manage Videos", "title": "Title"}}