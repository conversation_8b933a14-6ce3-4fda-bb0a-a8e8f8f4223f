export default {
  sms: {
    header: {
      self: {},
      items: [],
    },
    aside: {
      self: {},
      items: [
        {
          title: 'Dashboard',
          root: true,
          icon: 'flaticon2-architecture-and-city',
          page: 'sms',
          translate: 'menu.home',
        },
        {
          title: 'إدارة الطلبة',
          root: true,
          icon: 'flaticon-users',
          page: 'sms/students',
          level: 'university|school|department',
          roles:
            'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
        },
        {
          title: 'السنوات الدراسية',
          root: true,
          icon: 'flaticon-calendar-2',
          page: 'sms/academic-years',
          level: 'university|school',
          roles: 'general registrar|general registrar data entry|study and exams|school registrar',
        },
        {
          title: 'الجداول الدراسية',
          root: true,
          icon: 'flaticon-calendar-2',
          page: 'sms/timetables',
          level: 'school|department',
          roles: 'study and exams|department head',
        },
        {
          title: 'الخطة الدراسية',
          root: true,
          icon: 'flaticon-calendar-2',
          page: 'sms/terms',
          level: 'school',
          roles: 'study and exams',
        },
        // {
        //   title: 'تحميل النتائج',
        //   root: true,
        //   icon: 'flaticon-folder-1',
        //   page: 'sms/import-results',
        //   level: 'school',
        //   roles: 'study and exams',
        // },
        {
          title: 'التقارير',
          root: true,
          icon: 'flaticon-folder-1',
          page: 'sms/reports',
          level: 'university|school|department',
          roles: 'general registrar|study and exams|department head|monitor',
        },
        // {
        //   section: 'الخريجين',
        //   level: 'university|school|department',
        //   roles:
        //     'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
        // },
        // {
        //   title: 'طلبات التخرج',
        //   root: true,
        //   icon: 'flaticon-users',
        //   page: 'sms/graduation-requests',
        //   level: 'university|school|department',
        //   roles: 'general registrar|general registrar data entry|study and exams|department head|school registrar',
        // },
        // {
        //   title: 'إدارة الخريجين',
        //   root: true,
        //   icon: 'flaticon-users',
        //   page: 'sms/graduations',
        //   level: 'university|school|department',
        //   roles:
        //     'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
        // },
        {
          section: 'تسجيل الطلبة الجدد',
          level: 'university|school',
          roles: 'general registrar|general registrar data entry|study and exams|school registrar',
        },
        {
          title: 'طلبات التحاق الطلبة',
          root: true,
          icon: 'flaticon-users',
          page: 'sms/student-applications',
          level: 'university|school',
          roles: 'general registrar|general registrar data entry|study and exams|school registrar',
        },
        {
          title: 'السجل الدراسي',
          root: true,
          icon: 'flaticon-calendar-2',
          page: 'sms/academic-record',
          level: 'department',
          roles: 'student',
        },
        {
          title: 'الجدول الدراسي للقسم',
          root: true,
          icon: 'flaticon-calendar-2',
          page: 'sms/timetable',
          level: 'department',
          roles: 'student',
        },
        // {
        //   section: 'التقارير',
        //   level: 'department',
        //   roles: 'student',
        // },
        // {
        //   title: 'نموذج ايقاف القيد',
        //   root: true,
        //   icon: 'flaticon-folder-1',
        //   href: '/sms/reports/suspend-registration',
        //   level: 'department',
        //   roles: 'student',
        // },
        // {
        //   title: 'نموذج الإنسحاب',
        //   root: true,
        //   icon: 'flaticon-folder-1',
        //   href: '/sms/reports/withdraw',
        //   level: 'department',
        //   roles: 'student',
        // },
        // {
        //   title: 'نموذج غير مكمل في مقرر',
        //   root: true,
        //   icon: 'flaticon-folder-1',
        //   href: '/sms/reports/incomplete',
        //   level: 'department',
        //   roles: 'student',
        // },
      ],
    },
  },
  'lecturer-panel': {
    header: {
      self: {},
      items: [],
    },
    aside: {
      self: {},
      items: [
        {
          title: 'Dashboard',
          root: true,
          icon: 'flaticon2-architecture-and-city',
          page: 'lecturer-panel',
          roles: 'lecturer',
          level: 'department',
          translate: 'menu.home',
        },
      ],
    },
  },
  'control-website': {
    header: {
      self: {},
      items: [],
    },
    aside: {
      self: {},
      items: [
        {
          translate: 'menu.home',
          title: 'Home',
          root: true,
          icon: 'flaticon2-architecture-and-city',
          page: 'control-website',
        },
        {
          translate: 'menu.sliders',
          title: 'Sliders',
          root: true,
          icon: 'far fa-object-ungroup',
          page: 'control-website/sliders',
          permissions: 'view-sliders',
          level: 'not:journal',
        },
        {
          translate: 'menu.news',
          title: 'News',
          root: true,
          icon: 'far fa-newspaper',
          page: 'control-website/news',
          permissions: 'view-news',
          level: 'not:journal',
        },
        {
          translate: 'menu.events',
          title: 'Events',
          root: true,
          icon: 'flaticon-event-calendar-symbol',
          page: 'control-website/events',
          permissions: 'view-events',
          level: 'not:journal',
        },
        {
          translate: 'menu.albums',
          title: 'Albums',
          root: true,
          icon: 'far fa-images',
          page: 'control-website/albums',
          permissions: 'view-albums',
          level: 'not:journal',
        },
        {
          translate: 'menu.announcements',
          title: 'Announcements',
          root: true,
          icon: 'fas fa-bullhorn',
          page: 'control-website/announcements',
          permissions: 'view-announcements',
          level: 'not:journal',
        },
        {
          translate: 'menu.guides',
          title: 'Guides',
          root: true,
          icon: 'fas fa-poll',
          page: 'control-website/guides',
          permissions: 'view-guides',
          level: 'university|school|department',
        },
        {
          translate: 'menu.publications',
          title: 'Publications',
          root: true,
          icon: 'flaticon2-copy',
          page: 'control-website/publications',
          permissions: 'view-publications',
          level: 'university|school|department',
        },
        {
          translate: 'menu.videos',
          title: 'Videos',
          root: true,
          icon: 'fas fa-video',
          page: 'control-website/videos',
          permissions: 'view-videos',
          level: 'university|school|department',
        },
        {
          translate: 'menu.facts',
          title: 'Facts',
          root: true,
          icon: 'fas fa-poll',
          page: 'control-website/facts',
          permissions: 'view-facts',
          level: 'university|school|department',
        },
        {
          translate: 'menu.journal_details',
          title: 'Journal details',
          root: true,
          icon: 'fa fa-journal-whills',
          page: 'control-website/journal-details',
          permissions: 'control-journal',
          level: 'journal',
        },
        {
          translate: 'menu.issues',
          title: 'Issues',
          root: true,
          icon: 'fa fa-hashtag',
          page: 'control-website/issues',
          permissions: 'control-journal',
          level: 'journal',
        },
        {
          translate: 'menu.editorials',
          title: 'Editorials',
          root: true,
          icon: 'fa fa-users',
          page: 'control-website/editorials',
          permissions: 'control-journal',
          level: 'journal',
        },
      ],
    },
  },
  'control-panel': {
    header: {
      self: {},
      items: [],
    },
    aside: {
      self: {},
      items: [
        {
          title: 'Home',
          translate: 'menu.home',
          root: true,
          icon: 'flaticon2-architecture-and-city',
          page: 'control-panel',
        },
        {
          translate: 'menu.entities',
          title: 'Entities',
          root: true,
          icon: 'la la-university',
          page: 'control-panel/entities',
          permissions: 'view-entities',
          level: 'university|school|department',
        },
        {
          translate: 'menu.journals',
          title: 'Journals',
          root: true,
          icon: 'fa fa-journal-whills',
          page: 'control-panel/journals',
          permissions: 'view-journals',
          level: 'university',
        },
        {
          title: 'Buildings',
          translate: 'menu.buildings',
          root: true,
          icon: 'la la-building',
          page: 'control-panel/buildings',
          permissions: 'view-buildings',
          level: 'university',
        },
        {
          title: 'Program Types',
          translate: 'menu.program_types',
          root: true,
          icon: 'flaticon-interface-3',
          page: 'control-panel/program-types',
          level: 'university',
        },
        {
          title: 'Programs',
          translate: 'menu.programs',
          root: true,
          icon: 'flaticon-interface-3',
          page: 'control-panel/programs',
          permissions: 'view-programs',
          level: 'university|school|department',
        },
        {
          title: 'Lecturers',
          translate: 'menu.lecturers',
          root: true,
          icon: 'flaticon-users',
          page: 'control-panel/lecturers',
          permissions: 'view-lecturers',
          level: 'university|school|department',
        },
        {
          title: 'Employees',
          translate: 'menu.employees',
          root: true,
          icon: 'flaticon-users',
          page: 'control-panel/employees',
          permissions: 'view-employees',
          level: 'university|school|department',
        },
        {
          title: 'Administrators',
          translate: 'menu.administrators',
          root: true,
          icon: 'flaticon-lock',
          page: 'control-panel/administrators',
          permissions: 'view-privileges',
          level: 'university',
        },
        {
          title: 'roles',
          translate: 'menu.roles',
          root: true,
          icon: 'flaticon-lock',
          page: 'control-panel/roles',
          permissions: 'view-roles',
          level: 'university',
        },
      ],
    },
  },
}
