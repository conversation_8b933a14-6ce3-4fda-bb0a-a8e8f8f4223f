class Acl {
  constructor() {
    this.userPermissions = null
    this.userRoles = null
    this.level = null
    this.stages = {}
  }
  setUserPermissions(permissions) {
    this.userPermissions = permissions
  }
  setUserRoles(roles) {
    this.userRoles = roles
  }
  setLevel(level) {
    this.level = level
  }
  setStages(terms) {
    terms.forEach(term => {
      term.stages.forEach(stage => {
        if (!this.stages[term.study_plan]) {
          this.stages[term.study_plan] = {}
        }
        this.stages[term.study_plan][stage.title.en.toLowerCase()] = stage.is_active
      })
    })
  }
  can(permissions) {
    // if (process.env.MIX_APP_ENV === 'local') return true
    if (this.userRoles.includes('superadmin')) return true
    if (!this.userPermissions) return false
    return permissions
      .split('|')
      .map(orSymbol => orSymbol.split('&'))
      .some(andSymbol => {
        return andSymbol.every(permission => {
          return this.userPermissions.includes(permission)
        })
      })
  }
  cant(permissions) {
    return !this.can(permissions)
  }
  has(roles) {
    const rolesList = roles.split('|')
    if (!this.userRoles) return false
    const notTeacherOrStudent = !rolesList.every(role => ['lecturer', 'student'].includes(role))
    if (notTeacherOrStudent && this.userRoles.includes('superadmin')) return true
    return rolesList.some(role => this.userRoles.includes(role))
  }
  isStageActive(stage, study_plan = null) {
    if (this.userRoles.includes('superadmin')) return true

    // if study plan is null
    if (!study_plan) {
      return Object.values(this.stages).some(stages => stages[stage])
    }

    return this.stages[study_plan]?.[stage]
  }
  middleware(routeLevels) {
    if (!this.level) return false
    const containsNot = routeLevels.indexOf('not:') !== -1
    const levels = routeLevels.split(':').pop()
    const result = levels.split('|').some(level => level === this.level)
    return containsNot ? !result : result
  }
}

const AclService = new Acl()

export default AclService
