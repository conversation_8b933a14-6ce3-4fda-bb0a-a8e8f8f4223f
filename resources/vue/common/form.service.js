const isUndefined = value => value === undefined

const isNull = value => value === null

const isBoolean = value => typeof value === 'boolean'

const isObject = value => value === Object(value)

const isArray = value => Array.isArray(value)

const FormService = {
  objectToFormData(object, key = null, fd = null) {
    fd = fd || new FormData()

    if (isUndefined(object)) {
      return fd
    } else if (isNull(object)) {
      fd.append(key, '')
    } else if (isBoolean(object)) {
      fd.append(key, object ? 1 : 0)
    } else if (isArray(object)) {
      object.length &&
        object.forEach((value, index) => {
          if (!value.id) {
            FormService.objectToFormData(value, `${key}[${index}]`, fd)
          } else if (value.removed) {
            FormService.objectToFormData(value.id, `removed_${key}[${index}]`, fd)
          }
        })
    } else if (object.file) {
      fd.append(key, object.file, object.file.name)
    } else if (isObject(object)) {
      Object.entries(object).forEach(([pKey, pValue]) => {
        FormService.objectToFormData(pValue, key ? `${key}[${pKey}]` : pKey, fd)
      })
    } else {
      fd.append(key, object)
    }

    return fd
  },

  setTranslations(object, languages) {
    let langObject = {}
    languages.forEach(({ id }) => (langObject[id] = ''))

    Object.keys(object).forEach(key => {
      if (typeof object[key] === 'object' && object[key]?.translate) {
        object[key] = { ...langObject }
      }
    })
  },

  formatDate(date) {
    let d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear()

    if (month.length < 2) month = '0' + month
    if (day.length < 2) day = '0' + day

    return [year, month, day].join('-')
  },
}

export default FormService
