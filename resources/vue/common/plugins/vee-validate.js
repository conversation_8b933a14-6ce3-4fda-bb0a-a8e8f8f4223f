import i18n from './vue-i18n'
import {
  required,
  confirmed,
  length,
  email,
  digits,
  min,
  max,
  numeric,
  integer,
  alpha,
  alpha_dash,
  alpha_num,
  regex,
  min_value,
  max_value,
} from 'vee-validate/dist/rules'
import { extend } from 'vee-validate'
import { configure } from 'vee-validate'
import en from 'vee-validate/dist/locale/en'
import ar from 'vee-validate/dist/locale/ar'

i18n._vm.messages.en['validations'] = en.messages
i18n._vm.messages.ar['validations'] = ar.messages

configure({
  // this will be used to generate messages.
  defaultMessage: (field, values) => {
    // values._field_ = i18n.t(`fields.${field}`);
    return i18n.t(`validations.${values._rule_}`, values)
  },
})

extend('required', required)
extend('email', email)
extend('confirmed', confirmed)
extend('length', length)
extend('digits', digits)
extend('min', min)
extend('max', max)
extend('numeric', numeric)
extend('min_value', min_value)
extend('max_value', max_value)
extend('integer', integer)
extend('alpha', alpha)
extend('alpha_dash', alpha_dash)
extend('alpha_num', alpha_num)
extend('regex', regex)

extend('arabic', value => {
  if (new RegExp(/^[\u0600-\u06FF \s]+$/u).test(value)) return true
  return 'الحقل يجب ان يكون باللغة العربية فقط'
})

extend('english', value => {
  if (new RegExp(/(^([a-zA-Z\s]+)$)/u).test(value)) return true
  return 'الحقل يجب ان يكون باللغة الانجليزية فقط'
})

extend('phone', value => {
  if (new RegExp(/^(002189)([1-5])((\d{7,}))$/u).test(value)) return true
  return 'حقل رقم الهاتف يجب ان يكون على الصيغة 002189XXXXXXXX'
})
