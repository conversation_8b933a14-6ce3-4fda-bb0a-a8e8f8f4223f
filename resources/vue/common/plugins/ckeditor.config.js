import ApiService from '@/common/api.service'
import Compressor from 'compressorjs'

class imageUploadAdapter {
    constructor(loader) {
        this.loader = loader
    }

    // Starts the upload process.
    upload() {
        return this.loader.file.then(
            file =>
                new Promise((resolve, reject) => {
                    new Compressor(file, {
                        quality: 0.6,
                        convertSize: '1000000',
                        maxWidth: '1280',
                        success: file => {
                            const formData = new FormData()
                            formData.append('image', file, file.name)

                            this.source = ApiService.cancelToken().source()

                            let config = {
                                cancelToken: this.source.token,
                                onUploadProgress: progressEvent => {
                                    if (progressEvent.lengthComputable) {
                                        this.loader.uploadTotal = progressEvent.total
                                        this.loader.uploaded = progressEvent.loaded
                                    }
                                },
                            }

                            ApiService.post('articles/image', formData, config)
                                .then(({ data }) => resolve({ default: data }))
                                .catch(response =>
                                    reject(
                                        response && response.error ? response.error.message : 'failed uploading image.'
                                    )
                                )
                        },
                        error: () => {
                            console.log('failed uploading image.')
                            // this.$utils.alert(this.$t('msg.uploading_image_error'), 'error')
                        },
                    })
                })
        )
    }

    // Aborts the upload process.
    abort() {
        if (this.source) {
            this.source.cancel()
        }
    }
}

function imageUploadAdapterPlugin(editor) {
    editor.plugins.get('FileRepository').createUploadAdapter = loader => {
        return new imageUploadAdapter(loader)
    }
}

export default {
    language: localStorage.getItem('language') || 'ar',
    extraPlugins: [imageUploadAdapterPlugin],
    mediaEmbed: { previewsInData: true },
}
