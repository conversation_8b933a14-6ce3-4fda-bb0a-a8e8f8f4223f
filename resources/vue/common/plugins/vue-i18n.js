import Vue from 'vue'
import VueI18n from 'vue-i18n'

// Localisation language list
import en from '../config/i18n/en'
import ar from '../config/i18n/ar'

Vue.use(VueI18n)

let messages = {}
messages = { ...messages, en, ar }

// get current selected language
const lang = localStorage.getItem('language') || 'ar'
Vue.prototype.$lang = lang

// Create VueI18n instance with options
const i18n = new VueI18n({
    locale: lang, // set locale
    fallbackLocale: ['ar', 'en'],
    silentFallbackWarn: true,
    messages, // set locale messages
})

export default i18n
