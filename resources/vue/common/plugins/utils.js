export default {
  install: function(Vue, router) {
    let $this = Vue.prototype

    $this.$utils = Object.freeze({
      getCurrentOrFirstLang(obj) {
        if (!obj) return ''
        return obj[$this.$lang] || Object.values(obj).find(value => value)
      },
      alert(title, icon = 'info', text) {
        $this.$swal({
          title: title,
          text: text,
          icon: icon,
          confirmButtonText: 'حسناً',
        })
      },
      toast(title, icon = 'success') {
        $this.$swal({
          icon: icon,
          title: title,
          toast: true,
          position: 'bottom-start',
          showConfirmButton: false,
          timer: 2000,
          onOpen: toast => {
            toast.addEventListener('mouseenter', $this.stopTimer)
            toast.addEventListener('mouseleave', $this.resumeTimer)
          },
        })
      },
      confirm(html, title = 'تأكيد !', icon = 'warning') {
        return $this.$swal({
          title,
          html,
          icon,
          showCancelButton: true,
          confirmButtonText: 'نعم متأكد!',
          cancelButtonText: 'رجوع',
        })
      },
      errorHandler() {
        $this.$utils.toast('حدث خطأ في جلب البيانات، الرجاء إعادة المحاولة!', 'error')
      },
      errorHandlerWithValidation({ response }) {
        $this.$utils.alert(
          response?.data?.errors ? Object.values(response.data.errors)[0][0] : 'حدث خطأ الرجاء إعادة المحاولة!',
          'error'
        )
      },
      errorHandlerWith404({ response }) {
        if (response && response.status === 404) {
          router.replace(router.currentRoute.matched[0].path + '/404')
        } else {
          $this.$utils.toast('حدث خطأ في جلب البيانات، الرجاء إعادة المحاولة!', 'error')
        }
      },
    })
  },
}
