//
// Helper Functions
//




@function kt-unitless($number) {
    @if type-of($number) == 'number' and not unitless($number) {
        @return $number / ($number * 0 + 1);
    }

    @return $number;
}

@function kt-get($map, $keys...) {
    @if length($keys) == 1 {
        $keys: nth($keys, 1);
    }

    $warn: "#{nth($keys, 1)}";
    $length: length($keys);
    $get: map-get($map, nth($keys, 1));

    @if $length > 1 {
        @for $i from 2 through $length {
            @if $get != null and type-of($get) == 'map' {
                $warn: $warn + "->#{nth($keys, $i)}";
                $get: map-get($get, nth($keys, $i));

                @if $get == null {
                    @return null;
                }
            }
            @else {
                @return kt-get-warning($warn, $get, nth($keys, $i));
            }
        }
    }

    @return $get;
}

@function kt-has($map, $keys...) {
    @if length($keys) == 1 {
        $keys: nth($keys, 1);
    }

    $warn: "#{nth($keys, 1)}";
    $length: length($keys);
    $get: map-get($map, nth($keys, 1));

    @if $length > 1 {
        @for $i from 2 through $length {
            @if $get != null and type-of($get) == 'map' {
                $warn: $warn + "->#{nth($keys, $i)}";
                $get: map-get($get, nth($keys, $i));

                @if $get == null {
                    @return false;
                }
            }
            @else {
                 @return false;
            }
        }
    }

    @if $get != null {
        @return true;
    }
    @else {
        @return false;
    }
}

@function kt-get-warning($warn, $get, $key) {
    @if $get == null {
        @warn "Map has no value for key search `#{$warn}`";
    }
    @else if type-of($get) != 'map' {
        @warn "Non-map value found for key search `#{$warn}`, cannot search for key `#{$key}`";
    }
    @return null;
}

@function kt-display-color($color) {
    @if (lightness( $color ) > 50) {
      @return darken($color, 50%);
    }
    @else {
      @return lighten($color, 50%);
    }
}

@function kt-state-color($name, $type: null) {
    @if ($type == null) {
        $type: base;
    }

    @return kt-get($kt-state-colors, $name, $type);
}

@function kt-brand-color($type: null) {
    @return kt-state-color(brand, $type);
}

@function kt-base-color($type, $level) {
    @return kt-get($kt-base-colors, $type, $level);
}

@function kt-media-breakpoint($type) {
    @if (kt-has($kt-media-breakpoints, $type)) {
        @return kt-get($kt-media-breakpoints, $type);
    } @else {
        @return $type;
    }
}
