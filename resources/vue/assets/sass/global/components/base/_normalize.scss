/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */

//
// 1. Set default font family to sans-serif.
// 2. Prevent iOS and IE text size adjust after device orientation change,
//    without disabling user zoom.
// 3. Set 100% height for document body

html {
  font-family: sans-serif; // 1
  text-size-adjust: 100%; // 2
  -ms-text-size-adjust: 100%; // 2
  -webkit-text-size-adjust: 100%; // 2
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

html,
body {
    height: 100%;
    margin: 0px;
    padding: 0px;

    font-size: kt-get($kt-font-size, size, desktop) !important;
    font-weight: kt-get($kt-font-size, weight);
    font-family: #{kt-get($kt-font-families, regular)};

    -ms-text-size-adjust: 100%; // 2
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    a:hover,
    a:active,
    a:focus {
        text-decoration: none !important;
    }

    a,
    button {
        outline: none !important;
    }
}

body {
    display: flex;
    flex-direction: column;
    color: kt-get($kt-font-color, text);
}

// Angular integration
router-outlet {
    display: none;
}

canvas {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

/* Customize website's scrollbar like Mac OS */
/*
::-webkit-scrollbar {
    background-color:#f6f6f6;
    width:14px;
}

::-webkit-scrollbar-track {
    transition: all 0.3s ease;
    background-color:#f6f6f6;
    width:14px;
}

::-webkit-scrollbar-track:hover {
    transition: all 0.3s ease;
    background-color:#f6f6f6;
}

::-webkit-scrollbar-thumb {
    transition: all 0.3s ease;
    background-color:#c1c1c1;
}

::-webkit-scrollbar-thumb:hover {
    transition: all 0.3s ease;
    background-color: darken(#c1c1c1, 3%);
}
*/

/* set button(top and bottom of the scrollbar) */
::-webkit-scrollbar-button {display:none}

@include kt-tablet {
    html,
    body {
        font-size: kt-get($kt-font-size, size, tablet) !important;
    }
}

@include kt-mobile {
    html,
    body {
        font-size: kt-get($kt-font-size, size, mobile) !important;
    }
}
