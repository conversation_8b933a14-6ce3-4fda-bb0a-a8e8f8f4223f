body {
    overflow-y: scroll;
}

.custom-radio input,
.custom-radio label {
  cursor: pointer;
}
.custom-radio.b-custom-control-lg .custom-control-label {
  font-size: 1.25rem !important;
}
.swal2-container {
  .swal2-styled {
    margin: 0.3125em !important;
  }
}
.swal2-validation-message {
  margin-top: 1rem;
}
input[type='number'] {
  width: 100%;
}
textarea.form-control {
  overflow-y: initial !important;
}
.kt-input-icon__icon {
  z-index: 5;
}
.modal-header .close {
  padding: 1rem;
  font-size: 2rem;
  font-weight: bold;
  &:before {
    content: none !important;
  }
}
.ck.ck-sticky-panel .ck-sticky-panel__content_sticky {
  top: 119px !important;
}

@media (max-width: map-get($kt-media-breakpoints, 'lg')) {
  .ck.ck-sticky-panel .ck-sticky-panel__content_sticky {
    top: 50px !important;
  }
}

.kt-nav .kt-nav__item > .kt-nav__link .kt-nav__link-text {
  line-height: 2;
}

.multiple-languages {
  text-align: initial;
}

.text-gray {
  color: var(--gray);
}

.form-control-solid {
  input {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #3f4254;
  }
  input:active,
  input:focus {
    background-color: #ebedf3;
    border-color: #ebedf3;
    color: #3f4254;
  }
}
.cursor-pointer {
  cursor: pointer;
}

.kt-datatable__cell--sort {
  user-select: none;
}

.kt-avatar .kt-avatar__holder {
  height: initial !important;
}

.kt-app__aside {
  .kt-portlet {
    box-shadow: initial !important;
  }
}

[dir='rtl'] .swal2-actions {
  flex-direction: row-reverse;
}

.swal2-popup .swal2-title {
  line-height: 1.5;
}

.select-none {
  user-select: none;
}

.lead {
  font-weight: 500 !important;
}

.bullet {
  border-radius: 50%;
  width: 7px;
  height: 7px;
  display: inline-block;
}

.swal2-toast {
  background: var(--dark) !important;

  .swal2-title {
    color: #fff;
  }
}

.max-w-450 {
  width: 100%;
  max-width: 450px;
}

.mb-2rem {
    margin-bottom: 2rem;
}

/*rtl:begin:ignore*/
input[type='email'] {
  direction: ltr;
  &.form-control.is-invalid {
    background-position: right calc(0.375em + 0.325rem) center;
  }
}
/*rtl:end:ignore*/
