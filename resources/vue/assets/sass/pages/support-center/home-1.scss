//
// Support Center/Home 1
// Pages SASS files are compiled into separate css files
//



// Global config
@import "../../config";

// Layout config
@import "../../global/layout/config";


// Base
.kt-sc {
	background-position: center top;
	background-size: cover;
	background-repeat: no-repeat;
	margin-bottom: 2rem;

	.kt-sc__top {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 2rem;
		padding-bottom: 2rem;
		border-bottom: 1px solid kt-state-color(brand, inverse);

		.kt-sc__title {
			margin-bottom: 0;
			font-size: 1.45rem;
			color: kt-base-color(label, 4);
		}

		.kt-sc__nav {

			.kt-link{
				margin-left: 2rem;
				font-size: 1.1rem;
				font-weight: 400;

				&:first-child{
					margin-left: 0;
				}
			}
		}
	}

	.kt-sc__bottom{
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 10rem;
		padding-bottom: 10rem;

		.kt-sc__heading{
			margin-bottom: 4rem;
		}

		.kt-sc__form {
			.input-group{
				width: 630px;
				box-shadow: 0px 0px 9px 0px rgba(122,104,181,0.09);

				.input-group-text{
					border-top-right-radius: 0;
					border-bottom-right-radius: 0;
					border: none;
					padding: 0.65rem 2rem;
					background-color: kt-state-color(brand, inverse);
				}

				.form-control{
					border: none;
					padding: 2.5rem 0;
					font-weight: 400;
					font-size: 1.1rem;
				}
			}
		}
	}
}


@include kt-tablet-and-mobile(){
	.kt-sc {
		.kt-content--fit-top & {
			margin-top: -15px;
		}

		.kt-sc__top{
			.kt-sc__nav {
				.kt-link{
					margin-left: 1rem;

					&:first-child{
						margin-left: 0;
					}
				}
			}
		}

		.kt-sc__bottom{
			.kt-sc__form{
				.input-group{
					width:100%;
				}
			}
		}
	}
}
