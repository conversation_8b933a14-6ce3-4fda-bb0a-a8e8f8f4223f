//
// User Login v6
// Pages SASS files are compiled into separate css files
//



// Global config
@import "../../config";

// Layout config
@import "../../global/layout/config";

// Login Base
.kt-login.kt-login--v6 {
    background: #fff;

    // Page Aside
    .kt-login__aside {
        padding: 2rem;
        background: #fff;
        width: 600px;

        .kt-login__wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 2rem;

            .kt-login__container {
                flex: 1;
                width: 400px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                padding-bottom: 5rem;

                .kt-login__body {
                    width: 100%;
                    flex: 1;
                }

                .kt-login__logo {
                    text-align: center;
                    margin: 1rem auto 4rem auto;
                }

                .kt-login__head {
                    margin-top: 1rem;
                    text-align: center;

                    .kt-login__title {
                        text-align: center;
                        font-size: 1.5rem;
                        color: kt-base-color(label, 3);
                        font-weight: 500;
                    }

                    .kt-login__desc {
                        text-align: center;
                        font-size: 1rem;
                        color: kt-base-color(label, 2);
                        font-weight: 400;
                        margin-top: 1rem;
                    }
                }

                .kt-login__form {
                    margin-top: 4rem;

                    .form-group {
                        margin: 0;
                        padding: 0;
                        position: relative;
                    }

                    .form-control {
                        height: 46px;
                        border-radius: 0;
                        border: 0;
                        border-bottom: 1px solid rgba(kt-base-color(grey, 2), 0.8);
                        padding: 1rem 0;
                        margin-top: 0.1rem;
                        color: kt-base-color(label, 3);
                        @include kt-input-placeholder(kt-base-color(label, 2));

                        &.form-control-last {
                            border: 0;
                        }
                    }
                }

                .kt-login__actions {
                    margin: 3rem 0;
                    text-align: center;
                }

                .kt-login__extra {
                    margin-top: 2rem;
                    display: flex;
                    justify-content: space-between;

                    label {
                        margin: 0;
                    }

                    a {
                        font-weight: 500;
                        color: kt-base-color(label, 3);
                        transition: color 0.3s ease;
                        display: inline-block;

                        &:hover {
                            transition: color 0.3s ease;
                            color: kt-brand-color();
                        }
                    }
                }
            }

            .kt-login__account {
                text-align: center;

                .kt-login__account-msg {
                    font-size: 1rem;
                    font-weight: 500;
                    color: kt-base-color(label, 2);
                }

                .kt-login__account-link {
                    font-size: 1rem;
                    font-weight: 500;
                    color: kt-base-color(label, 3);
                    transition: color 0.3s ease;

                    &:hover {
                        transition: color 0.3s ease;
                        color: kt-brand-color();
                    }
                }
            }
        }
    }

    // Button
    .btn {
        height: 46px;
        padding-left: 3rem;
        padding-right: 3rem;

        & ~ .btn {
            margin-left: 0.5rem;
        }
    }

    // Page Content
    .kt-login__content {
        background-size: cover;
        background-repeat: no-repeat;

        .kt-login__section {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;

            .kt-login__title {
                color: #ffffff;
                font-size: 3.5rem;
                font-weight: 500;
                margin-bottom: 2rem;
            }

            .kt-login__desc {
                color: rgba(#ffffff, 0.8);
                font-size: 1.1rem;
                font-weight: 400;
            }
        }
    }
}

// Login Modes
.kt-login.kt-login--v6 {
    &.kt-login--signin {
        .kt-login__signup {
            display: none;
        }
        .kt-login__signin {
            display: block;
        }
        .kt-login__forgot {
            display: none;
        }
    }
    &.kt-login--signup {
        .kt-login__signup {
            display: block;
        }
        .kt-login__signin {
            display: none;
        }
        .kt-login__forgot {
            display: none;
        }
        .kt-login__account {
            display: none;
        }
    }
    &.kt-login--forgot {
        .kt-login__signup {
            display: none;
        }
        .kt-login__signin {
            display: none;
        }
        .kt-login__forgot {
            display: block;
        }
    }
}

@include kt-tablet-and-mobile() {
    .kt-login.kt-login--v6 {
        height: 100%;

        .kt-login__aside {
            padding: 20px 15px;
            width: 100%;

            .kt-login__wrapper {
                width: 100%;
                margin: 0 auto;
                padding: 15px;

                .kt-login__container {
                    width: 100%;
                    padding-bottom: 20px;

                    .kt-login__form {
                        margin-top: 20px;
                    }

                    .kt-login__actions {
                        margin: 20px 0;
                    }
                }

                .kt-login__logo {
                    margin: 20px auto;
                }
            }

            .kt-login__account {
                margin-top: 1.5rem;
            }
        }

        .kt-login__content {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 15px;

            .kt-login__section {
               .kt-login__title {
                    font-size: 1.3rem;
                    margin-bottom: 1rem;
                }

                .kt-login__desc {
                    font-size: 1rem;
                    margin: 0;
                }
            }
        }
    }
}
