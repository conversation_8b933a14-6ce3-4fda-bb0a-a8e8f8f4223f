//
// Pricing Tables 4
// Pages SASS files are compiled into separate css files
//



// Global config
@import "../../config";

// Layout config
@import "../../global/layout/config";

.kt-pricing-4 {
	padding-bottom: 4rem;

	.kt-pricing-4__top {
		background-color: #E0306E;

		.kt-pricing-4__top-container {
			&.kt-pricing-4__top-container--fixed {
				width: 80%;
				margin: 0 auto;

				.kt-pricing-4__top-header {
					.kt-pricing-4__top-title {
						padding-top: 5rem;
						margin-bottom: 5rem;
						text-align: center;

						> h1 {
							font-size: 2.3rem;
						}
					}
				}

				.kt-pricing-4__top-body {
					margin-left: 14.3rem;

					.kt-pricing-4__top-items {
						display: table;
						width: 100%;
						table-layout: fixed;
						box-shadow: 0 -1.3rem 1.07rem 0.71rem #D93371;

						.kt-pricing-4__top-item {
							display: table-cell;
							text-align: center;
							border-right: 1px solid #e1f1ff;
							background-color: white;

							&:last-child {
								border-right: none;
							}

							.kt-pricing-4__icon {
								> i {
									font-size: 5rem;
									margin-top: 3.6rem;
								}
							}

							.kt-pricing-4__subtitle {
								font-size: 1.64rem;
								color: kt-base-color(label, 3);
								margin-top: 2.14rem;
								font-weight: 500;
							}

							.kt-pricing-4__features {
								font-size: 1rem;
								margin-top: 2.14rem;
								font-weight: 400;
								margin-bottom: 1.43rem;
								padding: 0 0.71rem 0 0.71rem;
								color: kt-base-color(label, 2);
							}

							.kt-pricing-4__price {
								font-size: 3.2rem;
								color: kt-base-color(label, 3);
								font-weight: 700;
							}

							.kt-pricing-4__label {
								font-size: 1.64rem;
								vertical-align: 43%;
								color: kt-base-color(label, 2);
							}

							.kt-pricing-4__btn {
								margin-top: 1.43rem;
								margin-bottom: 3.6rem;

								.btn {
									padding: 0.8rem 3.6rem 0.8rem 3.6rem;
								}
							}

							.kt-pricing-4__top-items-mobile {
								display: none;

								> span {
									display: block;
								}

								.kt-pricing-4__top-item-mobile {
									color: kt-base-color(label, 2);

									> span {
										&:first-child {
											color: kt-base-color(label, 3);
										}
									}

									&:nth-child(odd) {
										background-color: #FAFBFF;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.kt-pricing-4__bottom {
		.kt-pricing-4__bottok-container {
			&.kt-pricing-4__bottok-container--fixed {
				width: 80%;
				margin: 0 auto;

				.kt-pricing-4__bottom-items {
					display: table;
					width: 100%;
					text-align: center;
					table-layout: fixed;

					.kt-pricing-4__bottom-item {
						display: table-cell;
						padding: 1.43rem 0;
						font-weight: 700;
						color: kt-base-color(label, 2);

						&:first-child {
							width: 14.3rem;
							text-align: left;
							padding-left: 1.43rem;
							font-weight: 700;
							color: #696B84;
						}
					}

					&:nth-child(odd) {
						background-color: #FAFBFF;
					}
				}
			}
		}
	}
}


@include kt-minimal-desktop() {
	.kt-pricing-4 {
		.kt-pricing-4__top {
			.kt-pricing-4__top-container {
				&.kt-pricing-4__top-container--fixed {
					width: 90%;
					margin: 0 auto;

					.kt-pricing-4__top-body {
						margin-left: 14.3rem;
					}
				}
			}
		}

		.kt-pricing-4__bottom {
			.kt-pricing-4__bottok-container {
				&.kt-pricing-4__bottok-container--fixed {
					width: 90%;
					margin: 0 auto;
				}
			}
		}
	}
}

@include kt-tablet() {
	.kt-pricing-4 {
		.kt-pricing-4__top {
			background: none;

			.kt-pricing-4__top-container {
				&.kt-pricing-4__top-container--fixed {
					width: 90%;
					margin: 0 auto;

					.kt-pricing-4__top-header {
						background-color: #E0306E;
						padding-bottom: 21.43rem;
					}

					.kt-pricing-4__top-body {
						width: 80%;
						margin: 0 auto;
						margin-top: -21.4rem;

						.kt-pricing-4__top-items {
							box-shadow: none;

							.kt-pricing-4__top-item {
								display: block;
								border-right: none;
								padding-bottom: 3.6rem;

								.kt-pricing-4__features {
									padding: 0 0.71rem 0 0.71rem;
								}

								.kt-pricing-4__btn {
									display: none;
								}

								.kt-pricing-4__top-items-mobile {
									display: block;
									margin:0 auto;

									.kt-pricing-4__top-item-mobile {
										padding: 0.71rem 0 0.71rem 0;

										> span {
											&:first-child {
												font-weight: 700;
											}
										}
									}
								}

								.kt-pricing-4__top-btn {
									margin-top: 2.86rem;

									.btn {
										padding: 0.8rem 3.6rem 0.8rem 3.6rem;
									}
								}

								border-bottom: 1px solid #e1f1ff;
							}
						}
					}
				}
			}
		}

		.kt-pricing-4__bottom {
			.kt-pricing-4__bottok-container {
				&.kt-pricing-4__bottok-container--fixed {
					display: none;
				}
			}
		}
	}
}

@include kt-mobile() {
	.kt-pricing-4 {
		.kt-pricing-4__top {
			background: none;

			.kt-pricing-4__top-container {
				&.kt-pricing-4__top-container--fixed {
					width: 100%;
					margin: 0 auto;

					.kt-pricing-4__top-header {
						background-color: #E0306E;
						padding-bottom: 21.43rem;

						.kt-pricing-4__top-title {
							> h1 {
								font-size: 1.8rem;
							}
						}
					}

					.kt-pricing-4__top-body {
						width: 80%;
						margin: 0 auto;
						margin-top: -21.43rem;

						.kt-pricing-4__top-items {
							box-shadow: none;

							.kt-pricing-4__top-item {
								display: block;
								border-right: none;
								padding-bottom: 1rem;

								.kt-pricing-4__btn {
									display: none;
								}

								.kt-pricing-4__features {
									padding: 0 0.71rem 0 0.71rem;
								}

								.kt-pricing-4__top-items-mobile {
									display: block;
									margin: 2.14rem auto;

									.kt-pricing-4__top-item-mobile {
										padding: 0.71rem 0 0.71rem 0;

										> span {
											&:first-child {
												font-weight: 700;
											}
										}
									}

									.kt-pricing-4__top-btn {
										margin-top: 2.86rem;
										.btn {
											padding: 0.8rem 3.6rem 0.8rem 3.6rem;
										}
									}
								}

								border-bottom: 1px solid #e1f1ff;
							}
						}
					}
				}
			}
		}

		.kt-pricing-4__bottom {
			.kt-pricing-4__bottok-container {
				&.kt-pricing-4__bottok-container--fixed {
					display: none;
				}
			}
		}
	}
}
