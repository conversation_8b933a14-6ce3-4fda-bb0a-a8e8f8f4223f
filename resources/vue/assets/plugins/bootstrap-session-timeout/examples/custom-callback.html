<!DOCTYPE html>
<html>

<head>
    <title>Bootstrap-session-timeout - Countdown Timer</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../bower_components/bootstrap/dist/css/bootstrap.min.css">
</head>

<body>
    <div class="container">
        <h1>Bootstrap-session-timeout</h1>
        <h2>Custom Callback</h2>
        <hr>
        <p>Shows an example of using custom callback functions for warning and redirect.</p>
        <div class="jumbotron" style="background: #398439;">
            <h3>Session Status:</h3>
            <p class="hidden" id="fine">Session is fine.</p>
            <p class="hidden" id="warn">Warning: session will expire in less than 17 seconds.</p>
        </div>


        <pre>
            $.sessionTimeout({
                keepAliveUrl: 'keep-alive.html',
                logoutUrl: 'login.html',
                warnAfter: 3000,
                redirAfter: 20000,
                onStart: function () {
                    $('.jumbotron').css('background', '#398439').find('p').addClass('hidden');
                    $('#fine').removeClass('hidden')
                },
                onWarn: function () {
                    $('.jumbotron').css('background', '#b92c28').find('p').addClass('hidden');
                    $('#warn').removeClass('hidden')
                },
                onRedir: function (opt) {
                    window.location = opt.logoutUrl;
                }
            });
        </pre>

        <a class="btn btn-primary" href="../index.html" role="button">Back to Demo Index</a>

    </div>
    <script src="../bower_components/jquery/dist/jquery.min.js"></script>
    <script src="../bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
    <script src="../dist/bootstrap-session-timeout.js"></script>

    <script>
    $.sessionTimeout({
        keepAliveUrl: 'keep-alive.html',
        logoutUrl: 'login.html',
        warnAfter: 3000,
        redirAfter: 20000,
        onStart: function () {
            $('.jumbotron').css('background', '#398439').find('p').addClass('hidden');
            $('#fine').removeClass('hidden');
        },
        onWarn: function () {
            $('.jumbotron').css('background', '#b92c28').find('p').addClass('hidden');
            $('#warn').removeClass('hidden');
        },
        onRedir: function (opt) {
            window.location = opt.logoutUrl;
        }
    });
    </script>
</body>

</html>
