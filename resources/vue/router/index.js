import Vue from 'vue'
import Router from 'vue-router'

import SmsRouter from './sms.router'
import ControlPanelRouter from './control-panel.router'
import ControlWebsiteRouter from './control-website.router'
import ProfileRouter from './profile.router'
import lecturerPanelRouter from './lecturer-panel.router'

Vue.use(Router)

export default new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      component: () => import('../views/pages/auth/Auth.vue'),
      children: [
        {
          name: 'students-login',
          path: 'students-login',
          component: () => import('../views/pages/auth/StudentsLogin.vue'),
        },
        {
          name: 'login',
          path: 'login',
          component: () => import('../views/pages/auth/Login.vue'),
        },
        {
          name: 'requestAcademicEmail',
          path: 'request-academic-email',
          component: () => import('../views/pages/auth/RequestAcademicEmail.vue'),
          level: 'university',
        },
        {
          name: 'apply',
          path: 'apply',
          component: () => import('../views/pages/auth/Apply.vue'),
          level: 'university',
        },
        {
          name: 'register',
          path: 'register/:type',
          component: () => import('../views/pages/auth/Register.vue'),
          level: 'university',
        },
        {
          name: 'requestResetPassword',
          path: 'request-reset-password',
          component: () => import('../views/pages/auth/RequestResetPassword.vue'),
        },
        {
          name: 'requestReceipt',
          path: 'request-application-receipt',
          component: () => import('../views/pages/auth/RequestReceipt.vue'),
        },
        {
          name: 'RequestApplyEdit',
          path: 'update-application-data',
          component: () => import('../views/pages/auth/RequestApplyEdit.vue'),
        },
        {
          name: 'RequestApplyEditDoctorate',
          path: 'update-application-data/doctorate',
          component: () => import('../views/pages/auth/RequestApplyEdit.vue'),
        },
        {
          name: 'SentEmailResetPassword',
          path: 'sent-email-reset-password',
          component: () => import('../views/pages/auth/SentEmailResetPassword.vue'),
        },
        {
          name: 'resetPassword',
          path: 'reset-password',
          component: () => import('../views/pages/auth/ResetPassword.vue'),
        },
        {
          name: 'verifyEmail',
          path: 'verify-email',
          component: () => import('../views/pages/auth/VerifyEmail.vue'),
        },
        {
          name: 'verifyNewStudentEmail',
          path: 'verify-application-email',
          component: () => import('../views/pages/auth/VerifyNewStudentEmail.vue'),
        },
      ],
    },
    {
      path: '/',
      component: () => import('../views/theme/Base.vue'),
      children: [ProfileRouter],
    },
    SmsRouter,
    lecturerPanelRouter,
    ControlPanelRouter,
    ControlWebsiteRouter,
    {
      path: '*',
      component: () => import('../views/pages/error/Error.vue'),
      meta: { backTo: '/' },
    },
  ],
})
