import ProfileRouter from './profile.router'

export default {
  path: '/lecturer-panel',
  component: () => import('../views/theme/Base'),
  children: [
    {
      path: '/',
      name: 'lecturer-panel',
      component: () => import('../views/pages/sms/lecturer/Dashboard.vue'),
      meta: {
        breadcrumbs: [{ title: 'الرئيسية' }],
        level: 'department',
        roles: 'lecturer',
      },
    },
    ProfileRouter,
    {
      path: 'subjects/:subject_id',
      name: 'lecturer-panel-subject',
      component: () => import('../views/pages/sms/lecturer/Subject.vue'),
      meta: {
        breadcrumbs: [{ title: 'الرئيسية', route: '/lecturer-panel' }, { title: 'طلاب المادة الدراسية' }],
        level: 'department',
        roles: 'lecturer',
      },
    },
    {
      path: 'subjects/:subject_id/grades',
      name: 'lecturer-panel-subject-grades',
      component: () => import('../views/pages/sms/lecturer/SubjectGrades.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الرئيسية', route: '/lecturer-panel' },
          { title: 'طلاب المادة الدراسية', route: { name: 'lecturer-panel-subject' } },
          { title: 'الدرجات' },
        ],
        level: 'department',
        roles: 'lecturer',
      },
    },
    {
      path: 'subjects/:subject_id/appeals',
      name: 'lecturer-panel-subject-appeals',
      component: () => import('../views/pages/sms/lecturer/SubjectAppeals.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الرئيسية', route: '/lecturer-panel' },
          { title: 'طلاب المادة الدراسية', route: { name: 'lecturer-panel-subject' } },
          { title: 'الطعون' },
        ],
        level: 'department',
        roles: 'lecturer',
      },
    },
    {
      path: '404',
      name: 'lecturer-panel-404',
      component: () => import('../views/pages/error/Error.vue'),
      meta: { backTo: '/lecturer-panel' },
    },
    {
      path: '*',
      redirect: '/lecturer-panel/404',
    },
  ],
}
