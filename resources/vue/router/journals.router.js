export default [
  // Manage issues
  {
    path: 'journal-details',
    name: 'currentJournalDetails',
    component: () => import('../views/pages/control-panel/journals/JournalForm'),
    meta: {
      breadcrumbs: [{ translate: 'journals.edit' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'issues',
    name: 'issues',
    component: () => import('../views/pages/control-website/journals/issues/Issues'),
    meta: {
      breadcrumbs: [{ translate: 'issues.manage' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'issues/new',
    name: 'newIssue',
    component: () => import('../views/pages/control-website/journals/issues/IssueForm'),
    meta: {
      breadcrumbs: [{ translate: 'issues.manage', route: { name: 'issues' } }, { translate: 'issues.add' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'issues/:issue_id',
    name: 'editIssue',
    component: () => import('../views/pages/control-website/journals/issues/IssueForm'),
    meta: {
      breadcrumbs: [{ translate: 'issues.manage', route: { name: 'issues' } }, { translate: 'issues.edit' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },

  // Manage issue articles
  {
    path: 'issues/:issue_id/articles',
    name: 'issueArticles',
    component: () => import('../views/pages/control-website/journals/issues/articles/Articles'),
    meta: {
      breadcrumbs: [{ translate: 'issues.manage', route: { name: 'issues' } }, { translate: 'articles.manage' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'issues/:issue_id/articles/new',
    name: 'newIssueArticle',
    component: () => import('../views/pages/control-website/journals/issues/articles/ArticleForm'),
    meta: {
      breadcrumbs: [
        { translate: 'issues.manage', route: { name: 'issues' } },
        { translate: 'articles.manage', route: { name: 'issueArticles' } },
        { translate: 'articles.add' },
      ],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'issues/:issue_id/articles/:article_id',
    name: 'editIssueArticle',
    component: () => import('../views/pages/control-website/journals/issues/articles/ArticleForm'),
    meta: {
      breadcrumbs: [
        { translate: 'issues.manage', route: { name: 'issues' } },
        { translate: 'articles.manage', route: { name: 'issueArticles' } },
        { translate: 'articles.edit' },
      ],
      rules: 'control-journal',
      level: 'journal',
    },
  },

  // Manage editorials
  {
    path: 'editorials',
    name: 'editorials',
    component: () => import('../views/pages/control-website/journals/editorials/Editorials'),
    meta: {
      breadcrumbs: [{ translate: 'editorials.manage' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'editorials/new',
    name: 'newEditorial',
    component: () => import('../views/pages/control-website/journals/editorials/EditorialForm'),
    meta: {
      breadcrumbs: [{ translate: 'editorials.manage', route: './' }, { translate: 'editorials.add' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
  {
    path: 'editorials/:editorial_id',
    name: 'editEditorial',
    component: () => import('../views/pages/control-website/journals/editorials/EditorialForm'),
    meta: {
      breadcrumbs: [{ translate: 'editorials.manage', route: './' }, { translate: 'editorials.edit' }],
      rules: 'control-journal',
      level: 'journal',
    },
  },
]
