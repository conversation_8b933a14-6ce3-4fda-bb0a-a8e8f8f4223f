import ProfileRouter from './profile.router'
import Journals from './journals.router'

export default {
  path: '/control-website',
  component: () => import('../views/theme/Base'),
  children: [
    {
      path: '/',
      name: 'controlWebsite',
      component: () => import('../views/pages/control-website/Dashboard.vue'),
      meta: {
        breadcrumbs: [{ title: 'الرئيسية', translate: 'menu.home' }],
      },
    },
    ProfileRouter,
    ...Journals,
    {
      path: 'sliders',
      name: 'sliders',
      component: () => import('../views/pages/control-website/sliders/Sliders'),
      meta: {
        breadcrumbs: [{ title: 'شرائح العرض', translate: 'sliders.manage' }],
        permissions: 'view-sliders',
      },
    },
    {
      path: 'sliders/new',
      name: 'newSlider',
      component: () => import('../views/pages/control-website/sliders/SliderForm'),
      meta: {
        breadcrumbs: [
          { title: 'شرائح العرض', translate: 'sliders.manage', route: { name: 'sliders' } },
          { title: 'إضافة شريحة', translate: 'sliders.add' },
        ],
        permissions: 'create-sliders',
      },
    },
    {
      path: 'sliders/:slider_id',
      name: 'editSlider',
      component: () => import('../views/pages/control-website/sliders/SliderForm'),
      meta: {
        breadcrumbs: [
          { title: 'شرائح العرض', translate: 'sliders.manage', route: { name: 'sliders' } },
          { title: 'تعديل شريحة', translate: 'sliders.edit' },
        ],
        permissions: 'update-sliders',
      },
    },
    {
      path: 'publications',
      name: 'publications',
      component: () => import('../views/pages/control-website/publications/Publications'),
      meta: {
        breadcrumbs: [{ title: 'إدارة المنشورات العلمية', translate: 'publications.manage' }],
        permissions: 'view-publications',
      },
    },
    {
      path: 'publications/new',
      name: 'newPublication',
      component: () => import('../views/pages/control-website/publications/PublicationForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المنشورات العلمية', translate: 'publications.manage', route: { name: 'publications' } },
          { title: 'إضافة منشور', translate: 'publications.add' },
        ],
        permissions: 'create-publications',
      },
    },
    {
      path: 'publications/:publication_id',
      name: 'editPublication',
      component: () => import('../views/pages/control-website/publications/PublicationForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المنشورات العلمية', translate: 'publications.manage', route: { name: 'publications' } },
          { title: 'تعديل منشور', translate: 'publications.edit' },
        ],
        permissions: 'update-publications',
      },
    },
    {
      path: 'articles',
      name: 'articles',
      component: () => import('../views/pages/control-website/articles/Articles'),
      meta: {
        breadcrumbs: [{ title: 'إدارة المقالات', translate: 'articles.manage' }],
        permissions: 'view-articles',
      },
    },
    {
      path: 'articles/new',
      name: 'newArticle',
      component: () => import('../views/pages/control-website/articles/ArticleForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المقالات', translate: 'articles.manage', route: { name: 'articles' } },
          { title: 'إضافة مقالة', translate: 'articles.add' },
        ],
        permissions: 'create-articles',
      },
    },
    {
      path: 'articles/:article_id',
      name: 'editArticle',
      component: () => import('../views/pages/control-website/articles/ArticleForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المقالات', translate: 'articles.manage', route: { name: 'articles' } },
          { title: 'تعديل مقالة', translate: 'articles.edit' },
        ],
        permissions: 'update-articles',
      },
    },
    {
      path: 'news',
      name: 'news',
      component: () => import('../views/pages/control-website/news/News'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الاخبار', translate: 'news.manage' }],
        permissions: 'view-news',
      },
    },
    {
      path: 'news/new',
      name: 'newNews',
      component: () => import('../views/pages/control-website/news/NewsForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الاخبار', translate: 'news.manage', route: { name: 'news' } },
          { title: 'إضافة خبر', translate: 'news.add' },
        ],
        permissions: 'create-news',
      },
    },
    {
      path: 'news/:news_id',
      name: 'editNews',
      component: () => import('../views/pages/control-website/news/NewsForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الاخبار', translate: 'news.manage', route: { name: 'news' } },
          { title: 'تعديل خبر', translate: 'news.edit' },
        ],
        permissions: 'update-news',
      },
    },
    {
      path: 'events',
      name: 'events',
      component: () => import('../views/pages/control-website/events/Events'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الأحداث', translate: 'events.manage' }],
        permissions: 'view-events',
      },
    },
    {
      path: 'events/new',
      name: 'newEvent',
      component: () => import('../views/pages/control-website/events/EventForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الأحداث', translate: 'events.manage', route: { name: 'events' } },
          { title: 'إضافة حدث', translate: 'events.add' },
        ],
        permissions: 'create-events',
      },
    },
    {
      path: 'events/:event_id',
      name: 'editEvent',
      component: () => import('../views/pages/control-website/events/EventForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الأحداث', translate: 'events.manage', route: { name: 'events' } },
          { title: 'تعديل حدث', translate: 'events.edit' },
        ],
        permissions: 'update-events',
      },
    },
    {
      path: 'albums',
      name: 'albums',
      component: () => import('../views/pages/control-website/albums/Albums'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الألبومات', translate: 'albums.manage' }],
        permissions: 'view-albums',
      },
    },
    {
      path: 'albums/new',
      name: 'newAlbum',
      component: () => import('../views/pages/control-website/albums/AlbumForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الألبومات', translate: 'albums.manage', route: { name: 'albums' } },
          { title: 'إضافة الألبوم', translate: 'albums.add' },
        ],
        permissions: 'create-albums',
      },
    },
    {
      path: 'albums/:album_id',
      name: 'editAlbum',
      component: () => import('../views/pages/control-website/albums/AlbumForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الألبومات', translate: 'albums.manage', route: { name: 'albums' } },
          { title: 'تعديل الألبوم', translate: 'albums.edit' },
        ],
        permissions: 'update-albums',
      },
    },
    {
      path: 'announcements',
      name: 'announcements',
      component: () => import('../views/pages/control-website/announcements/Announcements'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الاعانات', translate: 'announcements.manage' }],
        permissions: 'view-announcements',
      },
    },
    {
      path: 'announcements/requests',
      name: 'announcementRequests',
      component: () => import('../views/pages/control-website/announcements/AnnouncementRequests'),
      meta: {
        breadcrumbs: [
          { translate: 'announcements.manage', route: { name: 'announcements' } },
          { translate: 'announcements.publish_requests' },
        ],
        permissions: 'create-announcements',
      },
    },
    {
      path: 'announcements/new',
      name: 'newAnnouncement',
      component: () => import('../views/pages/control-website/announcements/AnnouncementForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الاعانات', translate: 'announcements.manage', route: { name: 'announcements' } },
          { title: 'إضافة اعلان', translate: 'announcements.add' },
        ],
        permissions: 'create-announcements',
      },
    },
    {
      path: 'announcements/:announcement_id',
      name: 'editAnnouncement',
      component: () => import('../views/pages/control-website/announcements/AnnouncementForm'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الاعانات', translate: 'announcements.manage', route: { name: 'announcements' } },
          { title: 'تعديل اعلان', translate: 'announcements.edit' },
        ],
        permissions: 'update-announcements',
      },
    },
    {
      path: 'videos',
      name: 'videos',
      component: () => import('../views/pages/control-website/videos/Videos'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الفيديوات', translate: 'videos.manage' }],
        permissions: 'view-videos',
      },
    },
    {
      path: 'facts',
      name: 'facts',
      component: () => import('../views/pages/control-website/facts/Facts'),
      meta: {
        breadcrumbs: [{ title: 'الحقائق', translate: 'facts.manage' }],
        permissions: 'view-facts',
      },
    },
    {
      path: 'guides',
      name: 'guides',
      component: () => import('../views/pages/control-website/guides/Guides'),
      meta: {
        breadcrumbs: [{ title: 'الوثائق', translate: 'guides.manage' }],
        permissions: 'view-guides',
      },
    },
    {
      path: '404',
      name: 'control-website-404',
      component: () => import('../views/pages/error/Error.vue'),
      meta: { backTo: '/control-website' },
    },
    {
      path: '*',
      redirect: '/control-website/404',
    },
  ],
}
