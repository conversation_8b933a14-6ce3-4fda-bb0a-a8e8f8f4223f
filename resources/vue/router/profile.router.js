export default {
    path: 'profile',
    component: () => import('../views/pages/profile/Profile'),
    children: [
        {
            path: '/',
            component: () => import('../views/pages/profile/PersonalInformation'),
            meta: {
                breadcrumbs: [{ title: 'الصفحة الشخصية', translate: 'users.profile' }],
            },
        },
        {
            path: 'job-info',
            component: () => import('../views/pages/profile/JobInfo'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: './' },
                    { translate: 'profile.job_info' },
                ],
                type: 'lecturer',
            },
        },
        {
            path: 'change-password',
            component: () => import('../views/pages/profile/ChangePassword'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: './' },
                    { translate: 'profile.change_password' },
                ],
            },
        },
        {
            path: 'links',
            component: () => import('../views/pages/profile/Links'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: './' },
                    { translate: 'profile.links' },
                ],
                type: 'lecturer',
            },
        },
        {
            path: 'publications',
            component: () => import('../views/pages/profile/Publications'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: './' },
                    { translate: 'profile.publications' },
                ],
                type: 'lecturer',
            },
        },
        {
            path: 'publications/new',
            component: () => import('../views/pages/control-website/publications/PublicationForm'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: '../' },
                    { translate: 'profile.publications', route: './' },
                    { translate: 'publications.add' },
                ],
                type: 'lecturer',
            },
        },
        {
            path: 'publications/:publication_id',
            component: () => import('../views/pages/control-website/publications/PublicationForm'),
            meta: {
                breadcrumbs: [
                    { title: 'الصفحة الشخصية', translate: 'users.profile', route: '../' },
                    { translate: 'profile.publications', route: './' },
                    { translate: 'publications.edit' },
                ],
                type: 'lecturer',
            },
        },
    ],
}
