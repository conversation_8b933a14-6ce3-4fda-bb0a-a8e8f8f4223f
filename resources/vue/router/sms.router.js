import ProfileRouter from './profile.router'

export default {
  path: '/sms',
  component: () => import('../views/theme/Base'),
  children: [
    {
      path: '/',
      name: 'sms',
      component: () => import('../views/pages/sms/Dashboard.vue'),
      meta: {
        breadcrumbs: [{ title: 'الرئيسية' }],
      },
    },
    ProfileRouter,
    {
      path: 'terms',
      name: 'terms',
      component: () => import('../views/pages/sms/terms/Terms.vue'),
      meta: {
        breadcrumbs: [{ title: 'الخطة الدراسية' }],
        level: 'school',
        roles: 'general registrar|study and exams',
      },
    },
    {
      path: 'terms/:term_id/stages',
      name: 'term-stages',
      component: () => import('../views/pages/sms/terms/TermStages.vue'),
      meta: {
        breadcrumbs: [{ title: 'الخطة الدراسية', route: { name: 'terms' } }, { title: 'الجدول الزمني للخطة الدراسية' }],
        level: 'school',
        roles: 'study and exams',
      },
    },
    {
      path: 'terms/:term_id/settings',
      name: 'term-settings',
      component: () => import('../views/pages/sms/terms/TermSettings.vue'),
      meta: {
        breadcrumbs: [{ title: 'الخطة الدراسية', route: { name: 'terms' } }, { title: 'إعدادات الخطة الدراسية' }],
        level: 'school',
        roles: 'study and exams',
      },
    },
    {
      path: 'terms/:term_id/subjects',
      name: 'term-subjects',
      component: () => import('../views/pages/sms/terms/TermSubjects.vue'),
      meta: {
        breadcrumbs: [{ title: 'الفصول الدراسية', route: { name: 'terms' } }, { title: 'تسجيلات المواد الدراسية' }],
        level: 'university|school|department',
        roles: 'general registrar|study and exams|department head',
      },
    },
    {
      path: 'terms/:term_id/reports',
      name: 'term-reports',
      component: () => import('../views/pages/sms/terms/TermReports.vue'),
      meta: {
        breadcrumbs: [{ title: 'الفصول الدراسية', route: { name: 'terms' } }, { title: 'تقارير الفصل الدراسي' }],
        level: 'school',
        roles: 'study and exams',
      },
    },
    {
      path: 'terms/:term_id/subjects/:subject_id/students',
      name: 'term-subject-students',
      component: () => import('../views/pages/sms/terms/TermSubjectStudents.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الفصول الدراسية', route: { name: 'terms' } },
          { title: 'تسجيلات المواد الدراسية', route: { name: 'term-subjects' } },
          { title: 'تسجيلات الطلبة' },
        ],
        level: 'university|school|department',
        roles: 'general registrar|study and exams|department head',
      },
    },
    {
      path: 'terms/:term_id/subjects/:subject_id/appeals',
      name: 'term-subject-appeals',
      component: () => import('../views/pages/sms/terms/TermSubjectAppeals.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الفصول الدراسية', route: { name: 'terms' } },
          { title: 'تسجيلات المواد الدراسية', route: { name: 'term-subjects' } },
          { title: 'طعون الطلبة' },
        ],
        level: 'university|school|department',
        roles: 'general registrar|study and exams|department head',
      },
    },
    {
      path: 'terms/:term_id/import-results',
      name: 'term-import-results',
      component: () => import('../views/pages/sms/ImportResults.vue'),
      meta: {
        breadcrumbs: [{ title: 'الفصول الدراسية', route: { name: 'terms' } }, { title: 'تحميل النتائج' }],
        level: 'school',
        roles: 'study and exams',
      },
    },
    {
      path: 'timetables',
      name: 'timetables',
      component: () => import('../views/pages/sms/timetables/Timetables.vue'),
      meta: {
        breadcrumbs: [{ title: 'الجداول الدراسية' }],
        level: 'school|department',
        roles: 'study and exams|department head',
      },
    },
    {
      path: 'timetables/:timetable_id',
      name: 'timetable',
      component: () => import('../views/pages/sms/timetables/Schedule.vue'),
      meta: {
        breadcrumbs: [{ title: 'الجداول الدراسية', route: { name: 'timetables' } }, { title: 'الجدول الدراسي' }],
        level: 'school|department',
        roles: 'study and exams|department head',
      },
    },
    {
      path: 'student-applications',
      name: 'studentApplications',
      component: () => import('../views/pages/sms/students/StudentApplications'),
      meta: {
        breadcrumbs: [{ title: 'طلبات التحاق الطلبة' }],
        level: 'university|school',
        roles: 'general registrar|general registrar data entry|study and exams|school registrar',
      },
    },
    {
      path: 'student-applications/create',
      name: 'createStudentApplication',
      component: () => import('../views/pages/sms/students/StudentApplicationForm'),
      meta: {
        breadcrumbs: [
          { title: 'طلبات التحاق الطلبة', route: { name: 'studentApplications' } },
          { title: 'إضافة طالب' },
        ],
        level: 'university|school',
        roles: 'general registrar|general registrar data entry',
      },
    },
    {
      path: 'student-applications/:student_id',
      name: 'editStudentApplication',
      component: () => import('../views/pages/sms/students/StudentApplicationForm'),
      meta: {
        breadcrumbs: [
          { title: 'طلبات التحاق الطلبة', route: { name: 'studentApplications' } },
          { title: 'تعديل طالب' },
        ],
        level: 'university|school',
        roles: 'general registrar|general registrar data entry|study and exams|school registrar',
      },
    },
    {
      path: 'students',
      name: 'students',
      component: () => import('../views/pages/sms/students/Students'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الطلبة' }],
        level: 'university|school|department',
        roles:
          'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
      },
    },
    {
      path: 'graduations',
      name: 'graduations',
      component: () => import('../views/pages/sms/students/Graduations'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الخريجين' }],
        level: 'university|school|department',
        roles:
          'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
      },
    },
    {
      path: 'graduation-requests',
      name: 'graduation-requests',
      component: () => import('../views/pages/sms/students/GraduationRequests'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الطلبة', route: { name: 'students' } }, { title: 'طلبات التخرج' }],
        level: 'university|school|department',
        roles: 'general registrar|general registrar data entry|study and exams|department head|school registrar',
      },
    },
    {
      path: 'students/new',
      name: 'newStudent',
      component: () => import('../views/pages/sms/students/StudentForm'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الطلبة', route: { name: 'students' } }, { title: 'إضافة طالب' }],
        level: 'university|school|department',
        roles: 'general registrar|general registrar data entry|study and exams|department head|school registrar',
      },
    },
    {
      path: 'students/:student_id',
      component: () => import('../views/pages/sms/students/profile/StudentProfile.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الطلبة', route: { name: 'students' } }, { title: 'صفحة الطالب' }],
        level: 'university|school|department',
        roles:
          'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
      },
      children: [
        {
          path: '/',
          name: 'view-student',
          component: () => import('../views/pages/sms/students/profile/StudentPersonalInfo.vue'),
          meta: {
            breadcrumbs: [{ title: 'إدارة الطلبة', route: { name: 'students' } }, { title: 'صفحة الطالب' }],
            level: 'university|school|department',
            roles:
              'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
          },
        },
        {
          path: 'academic-record',
          name: 'student-academic-record',
          component: () => import('../views/pages/sms/students/profile/StudentAcademicRecord.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'السجل الدراسي' },
            ],
            level: 'university|school|department',
            roles:
              'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
          },
        },
        {
          path: 'terms',
          name: 'student-terms',
          component: () => import('../views/pages/sms/students/profile/StudentTerms.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الفصول الدراسية للطالب' },
            ],
            level: 'university|school|department',
            roles:
              'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
          },
        },
        {
          path: 'terms/:term_id/subjects',
          name: 'student-subjects',
          component: () => import('../views/pages/sms/students/profile/StudentSubjects.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الفصول الدراسية', route: { name: 'student-terms' } },
              { title: 'المواد الدراسية للطالب' },
            ],
            level: 'university|school|department',
            roles:
              'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
          },
        },
        {
          path: 'notices',
          name: 'student-notices',
          component: () => import('../views/pages/sms/students/profile/StudentNotices.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الإنذارات' },
            ],
            level: 'university|school|department',
            roles:
              'general registrar|general registrar data entry|study and exams|department head|school registrar|monitor',
          },
        },
        {
          path: 'internal-transfer',
          name: 'student-internal-transfer',
          component: () => import('../views/pages/sms/students/profile/StudentInternalTransfer.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'السجل الدراسي', route: { name: 'student-academic-record' } },
              { title: 'معادلة داخلية' },
            ],
            level: 'university|school',
            roles: 'general registrar|study and exams',
          },
        },
        {
          path: 'outer-transfer',
          name: 'student-outer-transfer',
          component: () => import('../views/pages/sms/students/profile/StudentOuterTransfer.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'السجل الدراسي', route: { name: 'student-academic-record' } },
              { title: 'معادلة خارجية' },
            ],
            level: 'university',
            roles: 'general registrar',
          },
        },
        {
          path: 'theses',
          name: 'student-theses',
          component: () => import('../views/pages/sms/students/profile/StudentTheses.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الرسائل العلمية' },
            ],
            level: 'university|school|department',
          },
        },
        {
          path: 'theses/create',
          name: 'create-student-thesis',
          component: () => import('../views/pages/sms/students/profile/StudentThesisForm.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الرسائل العلمية', route: { name: 'student-theses' } },
              { title: 'إضافة مقترح رسالة علمية' },
            ],
            level: 'university|school',
            roles: 'general registrar|school registrar|general registrar data entry',
          },
        },
        {
          path: 'theses/:thesis_id',
          name: 'edit-student-thesis',
          component: () => import('../views/pages/sms/students/profile/StudentThesisForm.vue'),
          meta: {
            breadcrumbs: [
              { title: 'إدارة الطلبة', route: { name: 'students' } },
              { title: 'صفحة الطالب', route: { name: 'view-student' } },
              { title: 'الرسائل العلمية', route: { name: 'student-theses' } },
              { title: 'تحرير الرسالة العلمية' },
            ],
            level: 'university|school',
            roles: 'general registrar|school registrar|general registrar data entry',
          },
        },
      ],
    },
    {
      path: 'students/:student_id/edit',
      name: 'editStudent',
      component: () => import('../views/pages/sms/students/StudentForm'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الطلبة', route: { name: 'students' } }, { title: 'تعديل طالب' }],
        level: 'university|school|department',
        roles: 'general registrar|general registrar data entry|study and exams|department head|school registrar',
      },
    },
    {
      path: 'reports',
      name: 'sms-reports',
      component: () => import('../views/pages/sms/reports/Reports.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة التقارير' }],
        level: 'university|school|department',
        roles: 'general registrar|study and exams|department head|monitor',
      },
    },
    // Student Routes
    {
      path: 'timetable',
      name: 'student-timetable',
      component: () => import('../views/pages/sms/student/Timetable.vue'),
      meta: {
        breadcrumbs: [{ title: 'الجدول الدراسي للقسم' }],
        level: 'department',
        roles: 'student',
      },
    },
    {
      path: 'academic-record',
      name: 'academic-record',
      component: () => import('../views/pages/sms/student/AcademicRecord.vue'),
      meta: {
        breadcrumbs: [{ title: 'السجل الدراسي' }],
        level: 'department',
        roles: 'student',
      },
    },
    {
      path: 'academic-record/:term_id/subjects',
      name: 'academic-record-subjects',
      component: () => import('../views/pages/sms/student/AcademicRecordSubjects.vue'),
      meta: {
        breadcrumbs: [{ title: 'السجل الدراسي', route: { name: 'academic-record' } }, { title: 'المواد الدراسية' }],
        level: 'department',
        roles: 'student',
      },
    },
    {
      path: 'academic-record/:term_id/enrollment',
      name: 'enrollment',
      component: () => import('../views/pages/sms/student/Enrollment.vue'),
      meta: {
        breadcrumbs: [{ title: 'السجل الدراسي', route: { name: 'academic-record' } }, { title: 'تنزيل المواد' }],
        level: 'department',
        roles: 'student',
      },
    },
    {
      path: 'academic-years',
      name: 'academic-years',
      component: () => import('../views/pages/sms/academic-years/AcademicYears.vue'),
      meta: {
        breadcrumbs: [{ title: 'السنوات الدراسية' }],
        level: 'university|school',
      },
    },
    {
      path: 'academic-years/:academic_year_id/faculty-registrations',
      name: 'academic-years-faculty-registrations',
      component: () => import('../views/pages/sms/academic-years/AcademicYearFacultyRegistration.vue'),
      meta: {
        breadcrumbs: [{ title: 'السنوات الدراسية', route: { name: 'academic-years' } }, { title: 'تسجيل الكليات' }],
        level: 'university',
      },
    },
    {
      path: 'academic-years/:academic_year_id/program-registrations',
      name: 'academic-years-program-registrations',
      component: () => import('../views/pages/sms/academic-years/AcademicYearProgramRegistration.vue'),
      meta: {
        breadcrumbs: [
          { title: 'السنوات الدراسية', route: { name: 'academic-years' } },
          { title: 'تسجيل البرامج الدراسية' },
        ],
        level: 'university|school',
      },
    },
    // {
    //   path: 'settings',
    //   name: 'settings',
    //   component: () => import('../views/pages/sms/settings/Settings.vue'),
    //   meta: {
    //     breadcrumbs: [{ title: 'الإعدادات' }],
    //     level: 'university|school',
    //   },
    // },
    {
      path: '404',
      name: 'sms-404',
      component: () => import('../views/pages/error/Error.vue'),
      meta: { backTo: '/sms' },
    },
    {
      path: '*',
      redirect: '/sms/404',
    },
  ],
}
