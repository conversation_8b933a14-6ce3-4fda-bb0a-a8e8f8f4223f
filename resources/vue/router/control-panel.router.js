import ProfileRouter from './profile.router'

export default {
  path: '/control-panel',
  component: () => import('../views/theme/Base.vue'),
  children: [
    {
      path: '/',
      name: 'controlPanel',
      component: () => import('../views/pages/control-panel/Dashboard.vue'),
      meta: {
        breadcrumbs: [{ title: 'الرئيسية', translate: 'menu.home' }],
      },
    },
    ProfileRouter,
    {
      path: 'buildings',
      name: 'buildings',
      component: () => import('../views/pages/control-panel/buildings/Buildings.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة المباني', translate: 'buildings.manage' }],
        permissions: 'view-buildings',
        level: 'university',
      },
    },
    {
      path: 'buildings/:building_id/halls',
      name: 'halls',
      component: () => import('../views/pages/control-panel/halls/Halls.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المباني', translate: 'buildings.manage', route: { name: 'buildings' } },
          { title: 'إدارة القاعات', translate: 'halls.manage' },
        ],
        permissions: 'view-halls',
        level: 'university',
      },
    },
    {
      path: 'entities',
      name: 'entities',
      component: () => import('../views/pages/control-panel/entities/Entities.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الكيانات', translate: 'entities.manage' }],
        permissions: 'view-entities',
      },
    },
    {
      path: 'entities/new',
      name: 'newEntity',
      component: () => import('../views/pages/control-panel/entities/EntityForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الكيانات', translate: 'entities.manage', route: { name: 'entities' } },
          { title: 'إضافة كيان', translate: 'entities.add' },
        ],
        permissions: 'create-entities',
        level: 'university|school',
      },
    },
    {
      path: 'entities/:entity_id',
      name: 'editEntity',
      component: () => import('../views/pages/control-panel/entities/EntityForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الكيانات', translate: 'entities.manage', route: { name: 'entities' } },
          { title: 'تعديل كيان', translate: 'entities.edit' },
        ],
        permissions: 'update-entities',
      },
    },
    {
      path: 'journals',
      name: 'journals',
      component: () => import('../views/pages/control-panel/journals/Journals.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الكيانات', translate: 'journals.manage' }],
        permissions: 'view-journals',
      },
    },
    {
      path: 'journals/new',
      name: 'newJournal',
      component: () => import('../views/pages/control-panel/journals/JournalForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المجلات', translate: 'journals.manage', route: { name: 'journals' } },
          { title: 'إضافة مجلة', translate: 'journals.add' },
        ],
        permissions: 'create-journals',
        level: 'university',
      },
    },
    {
      path: 'journals/:journal_id',
      name: 'editJournal',
      component: () => import('../views/pages/control-panel/journals/JournalForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة المجلات', translate: 'journals.manage', route: { name: 'journals' } },
          { title: 'تعديل مجلة', translate: 'journals.edit' },
        ],
        permissions: 'update-journals',
        level: 'university',
      },
    },
    {
      path: 'program-types',
      name: 'programTypes',
      component: () => import('../views/pages/control-panel/programTypes/ProgramTypes.vue'),
      meta: {
        breadcrumbs: [{ title: 'انواع الرامج الدراسية' }],
        level: 'university',
      },
    },
    {
      path: 'programs',
      name: 'programs',
      component: () => import('../views/pages/control-panel/programs/Programs.vue'),
      meta: {
        breadcrumbs: [{ title: 'البرامج الدراسية', translate: 'programs.manage' }],
        permissions: 'view-programs',
        level: 'university|school|department',
      },
    },
    {
      path: 'programs/new',
      name: 'newProgram',
      component: () => import('../views/pages/control-panel/programs/ProgramForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'البرامج الدراسية', translate: 'programs.manage', route: { name: 'programs' } },
          { title: 'إضافة برنامج دراسي', translate: 'programs.add' },
        ],
        permissions: 'create-programs',
        level: 'university|school|department',
      },
    },
    {
      path: 'programs/:program_id',
      name: 'viewProgram',
      component: () => import('../views/pages/control-panel/programs/Program.vue'),
      meta: {
        breadcrumbs: [
          { title: 'البرامج الدراسية', translate: 'programs.manage', route: { name: 'programs' } },
          { title: 'عرض برنامج دراسي', translate: 'programs.view' },
        ],
        permissions: 'view-subjects',
        level: 'university|school|department',
      },
    },
    {
      path: 'programs/:program_id/edit',
      name: 'editProgram',
      component: () => import('../views/pages/control-panel/programs/ProgramForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'البرامج الدراسية', translate: 'programs.manage', route: { name: 'programs' } },
          { title: 'تعديل', translate: 'programs.edit' },
        ],
        permissions: 'update-programs',
        level: 'university|school|department',
      },
    },
    {
      path: 'programs/:program_id/subjects/new',
      name: 'newSubject',
      component: () => import('../views/pages/control-panel/programs/subjects/SubjectForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'البرامج الدراسية', translate: 'programs.manage', route: { name: 'programs' } },
          { title: 'عرض برنامج دراسي', translate: 'programs.view', route: { name: 'viewProgram' } },
          { title: 'إضافة مادة دراسية', translate: 'subjects.add' },
        ],
        permissions: 'create-subjects',
        level: 'university|school|department',
      },
    },
    {
      path: 'programs/:program_id/subjects/:subject_id',
      name: 'editSubject',
      component: () => import('../views/pages/control-panel/programs/subjects/SubjectForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'البرامج الدراسية', translate: 'programs.manage', route: { name: 'programs' } },
          { title: 'عرض برنامج دراسي', translate: 'programs.view', route: { name: 'viewProgram' } },
          { title: 'تعديل مادة دراسية', translate: 'subjects.edit' },
        ],
        permissions: 'update-subjects',
        level: 'university|school|department',
      },
    },
    {
      path: 'lecturers',
      name: 'lecturers',
      component: () => import('../views/pages/control-panel/lecturers/Lecturers.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة أعضاء هيئة التدريس', translate: 'lecturers.manage' }],
        permissions: 'view-lecturers',
      },
    },
    {
      path: 'lecturers/new',
      name: 'newLecturer',
      component: () => import('../views/pages/control-panel/lecturers/LecturerForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة أعضاء هيئة التدريس', translate: 'lecturers.manage', route: { name: 'lecturers' } },
          { title: 'إضافة عضو هيئة تدريس', translate: 'lecturers.add' },
        ],
        permissions: 'create-lecturers',
      },
    },
    {
      path: 'lecturers/:lecturer_id',
      name: 'editLecturer',
      component: () => import('../views/pages/control-panel/lecturers/LecturerForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة أعضاء هيئة التدريس', translate: 'lecturers.manage', route: { name: 'lecturers' } },
          { title: 'تعديل عضو هيئة تدريس', translate: 'lecturers.edit' },
        ],
        permissions: 'update-lecturers',
      },
    },
    {
      path: 'employees',
      name: 'employees',
      component: () => import('../views/pages/control-panel/employees/Employees.vue'),
      meta: {
        breadcrumbs: [{ title: 'إدارة الموظفين', translate: 'employees.manage' }],
        permissions: 'view-lecturers',
      },
    },
    {
      path: 'employees/new',
      name: 'newEmployee',
      component: () => import('../views/pages/control-panel/employees/EmployeeForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الموظفين', translate: 'employees.manage', route: { name: 'employees' } },
          { title: 'إضافة موظف', translate: 'employees.add' },
        ],
        permissions: 'create-employees',
      },
    },
    {
      path: 'employees/:employee_id',
      name: 'editEmployee',
      component: () => import('../views/pages/control-panel/employees/EmployeeForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'إدارة الموظفين', translate: 'employees.manage', route: { name: 'employees' } },
          { title: 'تعديل موظف', translate: 'employees.edit' },
        ],
        permissions: 'update-employees',
      },
    },
    {
      path: 'administrators',
      name: 'administrators',
      component: () => import('../views/pages/control-panel/administrators/Administrators.vue'),
      meta: {
        breadcrumbs: [{ translate: 'menu.administrators' }],
        permissions: 'view-privileges',
        level: 'university',
      },
    },
    {
      path: 'user-permissions/:user_id',
      name: 'userPermissions',
      component: () => import('../views/pages/control-panel/permissions/UserPermissions.vue'),
      meta: {
        breadcrumbs: [{ title: 'صلاحيات المستخدم', translate: 'permissions.user_permissions' }],
        permissions: 'view-privileges',
      },
    },
    {
      path: 'roles',
      name: 'roles',
      component: () => import('../views/pages/control-panel/roles/Roles.vue'),
      meta: {
        breadcrumbs: [{ translate: 'menu.roles' }],
        permissions: 'view-roles',
        level: 'university',
      },
    },
    {
      path: 'roles/new',
      name: 'newRole',
      component: () => import('../views/pages/control-panel/roles/RoleForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الادوار', translate: 'roles.manage', route: { name: 'roles' } },
          { title: 'إضافة دور جديد', translate: 'roles.add' },
        ],
        permissions: 'create-roles',
      },
    },
    {
      path: 'roles/:role_id',
      name: 'editRole',
      component: () => import('../views/pages/control-panel/roles/RoleForm.vue'),
      meta: {
        breadcrumbs: [
          { title: 'الادوار', translate: 'roles.manage', route: { name: 'roles' } },
          { title: 'تعديل دور', translate: 'roles.edit' },
        ],
        permissions: 'update-roles',
      },
    },
    {
      path: 'logs',
      name: 'logs',
      component: () => import('../views/pages/control-panel/logs/Logs.vue'),
      meta: {
        breadcrumbs: [{ title: 'السجلات' }],
        level: 'university',
        roles: 'superadmin',
      },
    },
    {
      path: '404',
      name: 'control-panel-404',
      component: () => import('../views/pages/error/Error.vue'),
      meta: { backTo: '/control-panel' },
    },
    {
      path: '*',
      redirect: '/control-panel/404',
    },
  ],
}
