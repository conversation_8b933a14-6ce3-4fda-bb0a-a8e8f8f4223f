<template>
  <div class="table-responsive">
    <table :class="{ 'timetable-editable': canEdit }" class="timetable table table-bordered">
      <thead>
      <tr>
        <th>#</th>
        <th v-for="(time, index) in times" :key="index" :style="`width: ${widthPerTime}%`">
          {{ time.format('HH:mm') }}
        </th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(day, index) in days" :key="index">
        <th scope="row">{{ day }}</th>
        <td :colspan="times.length" class="timetable-row">
          <timetable-row :data="rowTimetable(index)" :duration="duration" :times="times"/>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import TimetableRow from '@/components/timetable/TimetableRow.vue'

export default {
  name: 'Timetable',
  components: {TimetableRow},
  props: {
    data: {
      type: Array,
      required: true,
    },
    days: {
      type: Array,
      default: () => ['السبت', 'الأحد', 'الأثنين', 'الثلاثاء', 'الاربعاء', 'الخميس', 'الجمعة'],
    },
    startTime: {
      type: String,
      default: '09:00',
    },
    endTime: {
      type: String,
      default: '14:00',
    },
    duration: {
      type: Number,
      default: 30,
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
    showProgram: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    rowTimetable() {
      return dayIndex =>
        this.data
          .filter(tt => tt.day === dayIndex + 1)
          .map(item => {
            return {
              ...item,
              start_time: this.$dayjs(item.start_time),
              end_time: this.$dayjs(item.end_time),
            }
          })
    },
    widthPerTime() {
      return 100 / this.times.length
    },
    times() {
      const times = []

      let startTime = this.$dayjs(this.startTime)
      let endTime = this.$dayjs(this.endTime)

      while (startTime.isSameOrBefore(endTime)) {
        times.push(startTime.clone())
        startTime = startTime.add(this.duration, 'm')
      }

      return times
    },
  },
}
</script>

<style lang="scss">
.timetable {
  th {
    vertical-align: middle;
  }

  .timetable-row {
    padding: 0;

    .timetable-cell {
      border-radius: 0.25rem;
      // color: #fff;
      text-align: center;
      margin: 0.5rem 0.25rem;
      padding: 0.8rem;
      transition: 0.25s ease-in all;

      > span {
        display: block;
        font-size: 12px;
      }
    }
  }
}

.timetable-editable {
  .timetable-row {
    .timetable-cell {
      cursor: pointer;

      &:hover {
        transform: scale(0.97);
      }
    }
  }
}
</style>
