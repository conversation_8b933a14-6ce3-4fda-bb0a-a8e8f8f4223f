<template>
  <div @click="clicked" :class="['timetable-cell']" :style="background">
    <span>
      {{ lecture.subject.code }} ({{ lecture.group }}) -
      {{ lecture.subject && $utils.getCurrentOrFirstLang(lecture.subject.title) }}
    </span>
    <span v-if="showProgram">{{ lecture.program && $utils.getCurrentOrFirstLang(lecture.program.title) }}</span>
    <span>{{ lecture.lecturer.name_ar }}</span>
    <span>{{ lecture.hall && $utils.getCurrentOrFirstLang(lecture.hall.title) }}</span>
    <!-- <span>{{ lecture.start_time.format('HH:mm') }} - {{ lecture.end_time.format('HH:mm') }}</span> -->
  </div>
</template>

<script>
import seedRandom from 'seedrandom'

export default {
  name: 'TimetableCell',
  props: {
    lecture: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      colors: ['primary', 'success', 'info', 'danger', 'dark'],
    }
  },
  computed: {
    showProgram() {
      return this.$parent.$parent.showProgram
    },
    background() {
      const random = seedRandom(this.lecture.subject.code)
      const value = Math.floor(random() * 360)
      return `background-color: hsl(${value}, 50%, 85%)`
      // return this.colors[value]
    },
  },
  methods: {
    clicked() {
      this.$parent.$parent.$emit('cell-click', this.lecture.id)
    },
  },
}
</script>
