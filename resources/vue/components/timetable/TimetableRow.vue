<template>
  <div class="timetable-row">
    <div v-for="(row, index) in rows" :key="index" class="d-flex align-items-center">
      <div v-for="(lecture, lectIndex) in row" :key="`${index}-${lectIndex}`" :style="`width: ${lecture.width}%`">
        <timetable-cell v-if="lecture.id" :lecture="lecture" />
      </div>
    </div>
  </div>
</template>

<script>
import TimetableCell from '@/components/timetable/TimetableCell.vue'

export default {
  name: 'TimetableRow',
  components: { TimetableCell },
  props: {
    data: {
      type: Array,
      required: true,
    },
    times: {
      type: Array,
      required: true,
    },
    duration: {
      type: Number,
      default: 30,
    },
  },
  data() {
    return {
      timetable: [],
    }
  },
  computed: {
    widthPerTime() {
      return 100 / this.times.length
    },
    sortedLectures() {
      return this.data.slice().sort((a, b) => a.start_time - b.start_time)
    },
    rows() {
      const rows = []
      this.sortedLectures.forEach(lecture => {
        lecture.width = (lecture.end_time.diff(lecture.start_time, 'm') / this.duration) * this.widthPerTime
        let rowIndex = 0
        for (const row of rows) {
          if (this.isRowTimeFull(row, lecture.start_time)) rowIndex++
          else break
        }
        rows[rowIndex] ? rows[rowIndex].push(lecture) : (rows[rowIndex] = [lecture])
      })
      return rows.map(row => this.fillEmptyCells(row))
    },
  },
  methods: {
    fillEmptyCells(row) {
      const fillRow = []
      this.times.forEach(time => {
        if (!this.isTimeBusy(row, time)) {
          const lecture = row.find(lecture => lecture.start_time.isSame(time))
          lecture ? fillRow.push(lecture) : fillRow.push({ width: this.widthPerTime })
        }
      })
      return fillRow
    },
    isRowTimeFull(row, time) {
      return row.find(lecture => time.isSameOrAfter(lecture.start_time) && time.isBefore(lecture.end_time))
    },
    isTimeBusy(row, time) {
      return row.find(lecture => time.isAfter(lecture.start_time) && time.isBefore(lecture.end_time))
    },
  },
}
</script>
