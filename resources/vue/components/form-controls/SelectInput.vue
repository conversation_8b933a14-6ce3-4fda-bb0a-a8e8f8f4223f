<template>
  <ValidationProvider :vid="vid" :name="$attrs.label || $attrs.placeholder" :rules="rules" v-slot="{ errors }">
    <b-form-group :label-size="labelSize" v-bind="$attrs" :class="inputClass" :state="errors[0] ? false : null">
      <template v-if="$attrs.label" slot="label">
        <label>
          {{ $attrs.label }}
          <span v-if="isRequired" class="text-danger">*</span>
        </label>
      </template>
      <v-select
        :class="[errors[0] ? 'is-invalid' : null, 'form-control']"
        :dir="''"
        :options="options"
        :reduce="reduce"
        :label="text"
        :searchable="searchable"
        :clearable="clearable"
        v-bind="$attrs"
        v-model="innerValue"
      >
        <template #no-options>
          لا يوجد خيارات.
        </template>

        <template v-if="loading" slot="spinner">
          <b-spinner class="ml-2" variant="secondary" />
        </template>
      </v-select>

      <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

export default {
  name: 'SelectInput',
  components: {
    ValidationProvider,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    text: {
      type: String,
      default: 'title',
    },
    reduce: {
      type: Function,
      default: object => object.id,
    },
    searchable: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    inputClass: {
      type: String,
      default: null,
    },
    options: {
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    innerValue: '',
  }),
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal
    },
  },
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      } else {
        return this.rules.split('|').includes('required')
      }
    },
  },
  created() {
    if (this.value) {
      this.innerValue = this.value
    }
  },
}
</script>

<style lang="scss">
@import 'vue-select/src/scss/vue-select.scss';

.v-select {
  padding: 0;
  &.is-valid,
  &.is-invalid {
    padding-right: calc(1.5em + 0.3rem);
  }
  .vs__dropdown-toggle {
    padding: 0 1rem;
    height: 100%;
    border: none;
  }
  .vs__selected {
    height: 80%;
    border: none;
    background-color: #f3f6f9;
    padding: 0 1rem;
  }
  .vs__search {
    margin: 0;
    border: none;
  }
  .vs__actions {
    padding: 4px 3px 0 3px;
  }
  .vs__clear {
    height: 14px;
    width: 10px;
    margin-top: -10px;
  }
}
.vs--single .vs__selected {
  height: 100%;
  margin: 0 2px;
  padding: 0 0.25em;
  background-color: transparent;
}
.vs__search[aria-controls] {
  cursor: inherit !important;
}
</style>
