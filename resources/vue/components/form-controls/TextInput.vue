<template>
  <ValidationProvider
    :vid="vid"
    :tag="tag"
    :name="$attrs.label || $attrs.placeholder"
    :rules="rules"
    v-slot="{ errors }"
  >
    <b-form-group :label-size="labelSize" v-bind="$attrs" :class="inputClass">
      <slot name="label">
        <template v-if="$attrs.label" slot="label">
          <label>
            {{ $attrs.label }}
            <span v-if="isRequired && requiredStar" class="text-danger">*</span>
          </label>
        </template>
      </slot>

      <b-input-group
        v-if="append || prepend"
        :append="append"
        :prepend="prepend"
        :class="[{ 'is-invalid': !!errors[0] }]"
      >
        <b-form-input v-model="innerValue" v-bind="$attrs" :state="errors[0] ? false : null"> </b-form-input>
      </b-input-group>

      <div v-else :class="[{ 'is-invalid': !!errors[0] }, { 'kt-input-icon kt-input-icon--left': icon }]">
        <b-form-input v-model="innerValue" v-bind="$attrs" :state="errors[0] ? false : null"> </b-form-input>
        <span v-if="icon" class="kt-input-icon__icon kt-input-icon__icon--left">
          <span><i :class="[icon]"></i></span>
        </span>
      </div>

      <b-form-invalid-feedback v-if="rules">{{ errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

export default {
  name: 'TextInput',
  components: {
    ValidationProvider,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    icon: {
      type: String,
      default: null,
    },
    inputClass: {
      type: String,
      default: null,
    },
    prepend: {
      type: String,
      default: null,
    },
    append: {
      type: String,
      default: null,
    },
    tag: {
      type: String,
      default: 'div',
    },
    requiredStar: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    innerValue: '',
  }),
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal
    },
  },
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      } else {
        return this.rules.split('|').includes('required')
      }
    },
  },
  created() {
    if (typeof this.value !== 'undefined' || this.value !== null) {
      this.innerValue = this.value
    }
  },
}
</script>
