<template>
  <ValidationObserver ref="observer" tag="div" slim>
    <b-form ref="form" @submit.stop.prevent="submit">
      <slot></slot>
    </b-form>
  </ValidationObserver>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import KTUtil from '@/assets/js/util'

export default {
  name: 'FormValidation',
  components: { ValidationObserver },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    loading(val) {
      const elements = this.$refs.form.elements
      for (let i = 0; i < elements.length; i++) {
        elements[i].disabled = val
      }
      // this.$refs.form.elements.forEach(element => (element.disabled = val))
    },
  },
  methods: {
    async submit() {
      const result = await this.$refs.observer.validate()
      if (result) {
        this.$emit('submit')
      } else {
        KTUtil.scrollTo('is-invalid', 0, 500, -170)
      }
    },
  },
}
</script>
