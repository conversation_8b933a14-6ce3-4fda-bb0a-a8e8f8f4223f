<template>
  <b-form-group :label-size="labelSize" v-bind="$attrs">
    <span :class="[`kt-switch kt-switch-${color} kt-switch-${size}`, { 'kt-switch-outline': outline }]">
      <label :class="labelClass">
        <input type="checkbox" v-bind="$attrs" v-model="innerValue" />
        <span></span>
      </label>
    </span>
  </b-form-group>
</template>

<script>
export default {
  name: 'SwitchInput',
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
    },
    checkedValue: {
      default: true,
    },
    uncheckedValue: {
      default: false,
    },
    color: {
      type: String,
      default: 'brand',
    },
    outline: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'md',
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    labelClass: {
      type: String,
      default: '',
    },
  },
  data: () => ({
    innerValue: '',
  }),
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal ? this.checkedValue : this.uncheckedValue
    },
  },
  created() {
    if (this.value) {
      this.innerValue = this.value
    }
  },
}
</script>
