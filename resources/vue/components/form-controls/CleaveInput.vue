<template>
  <ValidationProvider
    v-slot="{ valid, changed, errors }"
    :vid="vid"
    :tag="tag"
    :name="$attrs.label || $attrs.placeholder"
    :rules="rules"
  >
    <b-form-group
      :label-size="labelSize"
      v-bind="$attrs"
      :class="inputClass"
    >
      <slot name="label">
        <template
          v-if="$attrs.label"
          slot="label"
        >
          <label class="mb-0">
            {{ $attrs.label }}
            <span
              v-if="isRequired && requiredStar"
              class="text-danger"
            >*</span>
          </label>
        </template>
      </slot>

      <cleave
        v-model="innerValue"
        class="form-control"
        v-bind="$attrs"
        :state="errors[0] ? false : valid && changed && rules ? true : null"
      />

      <b-form-invalid-feedback v-if="rules">
        {{ errors[0] }}
      </b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'
import Cleave from 'vue-cleave-component'

export default {
  name: 'CleaveInput',
  components: {
    ValidationProvider,
    Cleave,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
      default: null,
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    inputClass: {
      type: String,
      default: null,
    },
    tag: {
      type: String,
      default: 'div',
    },
    requiredStar: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    innerValue: '',
  }),
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      }
      return this.rules.split('|').includes('required')
    },
  },
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal
    },
  },
  created() {
    if (typeof this.value !== 'undefined' || this.value !== null) {
      this.innerValue = this.value
    }
  },
}
</script>
