<template>
  <ValidationProvider v-slot="{ valid, errors }" :vid="vid" :name="$attrs.label || $attrs.placeholder" :rules="rules">
    <b-form-group :label-size="labelSize" v-bind="$attrs">
      <template v-if="$attrs.label" slot="label">
        <label>
          {{ $attrs.label }}
          <span v-if="isRequired" class="text-danger">*</span>
        </label>
      </template>
      <date-picker
        append-to-body
        v-model="innerValue"
        v-bind="$attrs"
        :value-type="valueType"
        :format="format"
        :lang="$lang"
        :type="type"
        :class="errors[0] ? 'is-invalid' : valid && rules ? 'is-valid' : null"
      />
      <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'
import DatePicker from 'vue2-datepicker'

import 'vue2-datepicker/locale/ar'

export default {
  name: 'DatepickerInput',
  components: {
    ValidationProvider,
    DatePicker,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    type: {
      type: String,
      default: 'date',
    },
  },
  data: () => ({
    innerValue: '',
    format: 'DD MMM, YYYY',
    valueType: 'YYYY-MM-DD',
  }),
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      } else {
        return this.rules.split('|').includes('required')
      }
    },
  },
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal
    },
  },
  created() {
    if (this.value) {
      this.innerValue = this.value
    }
    if (this.type === 'datetime') {
      this.format = 'DD MMM, YYYY | HH:mm'
      this.valueType = 'YYYY-MM-DD HH:mm:ss'
    }
  },
}
</script>
