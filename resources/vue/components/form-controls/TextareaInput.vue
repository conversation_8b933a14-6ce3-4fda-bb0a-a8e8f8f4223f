<template>
  <ValidationProvider
    :vid="vid"
    :tag="tag"
    :name="$attrs.label || $attrs.placeholder"
    :rules="rules"
    v-slot="{ errors }"
  >
    <b-form-group :label-size="labelSize" v-bind="$attrs" :class="inputClass">
      <template v-if="$attrs.label" slot="label">
        <label>
          {{ $attrs.label }}
          <span v-if="isRequired" class="text-danger">*</span>
        </label>
      </template>
      <b-form-textarea v-model="innerValue" v-bind="$attrs" :state="errors[0] ? false : null"></b-form-textarea>
      <b-form-invalid-feedback v-if="rules">{{ errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

export default {
  name: 'TextareaInput',
  components: {
    ValidationProvider,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: null,
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    inputClass: {
      type: String,
      default: null,
    },
    tag: {
      type: String,
      default: 'div',
    },
  },
  data: () => ({
    innerValue: '',
  }),
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal
    },
  },
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      } else {
        return this.rules.split('|').includes('required')
      }
    },
  },
  created() {
    if (this.value) {
      this.innerValue = this.value
    }
  },
}
</script>
