<template>
    <transition name="image-viewer">
        <div ref="view" class="image-viewer" @click="$emit('close')">
            <img :src="viewImageSrc" alt="image" />
        </div>
    </transition>
</template>

<script>
export default {
    name: 'ImageViewer',
    props: {
        viewImageSrc: {
            default: null,
            type: String,
        },
    },
    mounted() {
        document.body.insertBefore(this.$refs.view, document.body.firstChild)
    },
    updated() {
        document.body.insertBefore(this.$refs.view, document.body.firstChild)
    },
}
</script>

<style scoped>
.image-viewer-enter-active,
.image-viewer-leave-active {
    transition: opacity 0.5s;
}
.image-viewer-enter,
.image-viewer-leave-to {
    opacity: 0;
}
.image-viewer-enter,
.image-viewer-leave-to img {
    transform: scale(0.4);
}
.image-viewer {
    position: fixed;
    left: 0;
    z-index: 15000;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    top: 0;
    padding: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.image-viewer img {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 10px;
    position: relative;
    display: block;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    max-width: 100%;
    max-height: 100%;
    margin: auto;
    -webkit-animation: imageRebound 0.7s ease !important;
    animation: imageRebound 0.7s ease !important;
    -webkit-box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.25);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.25);
    background: #fff;
}
@keyframes imageRebound {
    0% {
        transform: scale(0.4);
        border-radius: 30%;
        pointer-events: none;
    }
    40% {
        transform: scale(1.03);
        border-radius: 14px;
        pointer-events: none;
    }
    70% {
        transform: scale(0.98);
        border-radius: 18px;
        pointer-events: none;
    }
    100% {
        transform: scale(1);
        border-radius: 10px;
        pointer-events: none;
    }
}
</style>
