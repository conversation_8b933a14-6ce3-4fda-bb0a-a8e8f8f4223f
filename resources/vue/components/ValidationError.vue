<template functional>
  <b-alert v-if="props.errors" class="mt-2" variant="danger" :show="true">
    <div class="alert-text">
      <h4 v-if="!props.noTitle" class="alert-heading">
        تحقق من صحة البيانات
      </h4>

      <ul class="errors-list">
        <li v-for="(error, index) in Object.values(props.errors)" :key="index">
          {{ error[0] }}
        </li>
      </ul>
    </div>
  </b-alert>
</template>

<script>
export default {
  name: 'ValidationError',
  props: {
    noTitle: {
      type: Boolean,
      default: false,
    },
    errors: {
      type: Object,
      default: null,
    },
  },
}
</script>

<style scoped>
.errors-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  font-weight: 500;
  font-size: 1.2rem;
  margin-top: 1rem;
}
</style>
