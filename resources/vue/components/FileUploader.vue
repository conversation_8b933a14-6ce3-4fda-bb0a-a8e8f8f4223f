<template>
    <div class="file-uploader mb-4">
        <input
            @change="getFiles"
            hidden
            class="file-uploader__input"
            type="file"
            ref="fileInput"
            :multiple="multiple"
            :accept="accept"
        />
        <div class="d-flex">
            <v-button v-if="currentFiles.length === 0" @click="$refs.fileInput.click()" variant="label-brand">
                {{ $tc('file_uploader.upload_file', multiple ? 2 : 1) }}
            </v-button>
            <v-button v-if="files.length > 1" class="ml-2" variant="label-brand">
                {{ $t('file_uploader.cancel_all') }}
            </v-button>
        </div>
        <div class="file-uploader__flies">
            <div
                v-for="(file, index) in currentFiles"
                :key="index"
                class="file-uploader__file shadow-sm d-flex align-items-center justify-content-between"
            >
                <a :href="file.path || ''" target="_blank" class="d-flex">
                    <span class="text-truncate">{{ file.name }}</span>
                    <strong v-if="file.size" class="ml-2" dir="ltr">({{ file.size | formatBytes }})</strong>
                </a>
                <div>
                    <v-button
                        v-if="!multiple"
                        @click="$refs.fileInput.click()"
                        class="rounded-circle"
                        icon-only
                        icon="la la-edit"
                        size="sm"
                        variant="label-brand"
                    />
                    <v-button
                        @click="remove(file)"
                        class="rounded-circle"
                        icon-only
                        icon="la la-remove"
                        size="sm"
                        variant="label-danger"
                    />
                </div>
            </div>
        </div>
        <span v-if="description" class="form-text text-muted">{{ description }}</span>
    </div>
</template>

<script>
export default {
    name: 'FileUploader',
    props: {
        files: {
            type: Array,
            required: true,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        description: {
            type: String,
            default: null,
        },
        accept: {
            type: String,
            default: '*/*',
        },
    },
    filters: {
        formatBytes(bytes) {
            if (bytes == 0) return '0 Bytes'
            let k = 1024,
                sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
                i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        },
    },
    computed: {
        currentFiles() {
            return this.files.filter(file => !file.removed)
        },
    },
    methods: {
        remove(file) {
            const index = this.files.indexOf(file)
            this.files.splice(index, 1)
            if (file.id) {
                file.removed = true
                this.files.push(file)
            }
        },
        getFiles(event) {
            let files = event.target.files
            for (let index in files) {
                if (files.hasOwnProperty(index)) {
                    let file = files[index]
                    if (!this.multiple) this.files.splice(0, this.files.length)
                    this.files.push({
                        file: file,
                        name: file.name,
                        size: file.size,
                    })
                }
            }
            this.$emit(
                'upload',
                this.files.filter(file => !!file.file)
            )
            let input = this.$refs.fileInput
            input.type = 'text'
            input.type = 'file'
        },
    },
}
</script>

<style lang="scss">
.file-uploader {
    &__file {
        border-radius: 0.42rem;
        padding: 0.5rem 1rem;
        background-color: #f3f6f9;
    }
    &__file:not(:first-child) {
        margin-top: 0.75rem;
    }
}
</style>
