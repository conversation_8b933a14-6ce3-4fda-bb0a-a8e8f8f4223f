<template functional>
    <b-button
        v-bind="data.attrs"
        v-on="listeners"
        :class="[
            {
                'btn-wide': props.wide,
                'btn-bold': props.bold,
                'btn-icon': props.iconOnly,
                'kt-spinner kt-spinner--light kt-spinner--right': props.loading,
                ...data.class,
            },
            data.staticClass,
        ]"
    >
        <div class="align-items-center justify-content-center d-flex">
            <v-icon v-if="props.icon" :icon="props.icon" />
            <slot></slot>
        </div>
    </b-button>
</template>

<script>
export default {
    name: 'VButton',
    props: {
        icon: {
            type: String,
            default: '',
        },
        iconPack: {
            type: String,
            default: '',
        },
        iconOnly: {
            type: Boolean,
            default: false,
        },
        wide: {
            type: Boolean,
            default: false,
        },
        bold: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
}
</script>
