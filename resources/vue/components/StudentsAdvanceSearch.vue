<template>
  <form @submit.prevent="apply">
    <div class="row">
      <b-col col lg="4">
        <TextInput v-model="advanceSearch.search" placeholder="بحث" />
      </b-col>
      <b-col col lg="4">
        <b-button-group>
          <b-button type="submit" variant="brand"><i class="fa fa-search"></i></b-button>
          <b-button @click="collapse = !collapse" variant="outline-brand">بحث متقدم</b-button>
          <b-button v-if="isFilter" variant="outline-brand" @click="clearFilter">
            الغاء الفرز
          </b-button>
        </b-button-group>
      </b-col>
      <div class="col-12 mb-4">
        <b-collapse v-model="collapse">
          <div class="row">
            <div v-if="$acl.middleware('university')" class="col-lg-3">
              <select-input v-model="advanceSearch.school_id" :options="schools" clearable label="الكلية" searchable />
            </div>
            <div v-if="$acl.middleware('university|school')" class="col-lg-3">
              <select-input
                v-model="advanceSearch.department_id"
                :options="departments"
                clearable
                label="القسم"
                searchable
              />
            </div>
            <div v-if="$acl.middleware('university|school|department')" class="col-lg-3">
              <select-input
                v-model="advanceSearch.program_id"
                :options="programs"
                clearable
                label="البرنامج الدراسي"
                searchable
              />
            </div>
            <div v-if="type === 'graduations'" class="col-lg-3">
              <select-input
                v-model="advanceSearch.term_id"
                :options="terms"
                label="الفصل الدراسي"
                searchable
                clearable
              />
            </div>
            <!-- <div v-if="type !== 'graduation-requests'" class="col-lg-3">
              <select-input v-model="advanceSearch.program_type_id" :options="programTypes" clearable label="الدرجة" />
            </div> -->
            <div class="col-lg-3">
              <select-input v-model="advanceSearch.status" :options="statusOptions" clearable label="الحالة" />
            </div>
            <!-- <div class="col-lg-3">
              <TextInput v-model="advanceSearch.sponsor" label="الجهة المانحة" />
            </div> -->
            <div class="col-lg-3">
              <DatepickerInput
                v-model="advanceSearch.start_date"
                description="يرجى ترك حقول التاريخ فارغة في حالة لم ترد تقييد البحث بزمن معين."
                label="من تاريخ"
              />
            </div>
            <div class="col-lg-3">
              <DatepickerInput v-model="advanceSearch.end_date" label="إلى تاريخ" />
            </div>
            <b-col align="end" cols="12">
              <b-button @click="collapse = false" variant="plain">إلغاء</b-button>
              <b-button type="submit" variant="brand">بحث</b-button>
            </b-col>
          </div>
        </b-collapse>
      </div>
    </div>
  </form>
</template>

<script>
import TextInput from '@/components/form-controls/TextInput.vue'
import DatepickerInput from '@/components/form-controls/DatepickerInput.vue'
import SelectInput from '@/components/form-controls/SelectInput.vue'

export default {
  components: { TextInput, DatepickerInput, SelectInput },
  props: {
    type: {
      type: String,
      default: 'student',
    },
  },
  data() {
    return {
      collapse: false,
      advanceSearch: {
        search: undefined,
        program_id: undefined,
        status: undefined,
        start_date: undefined,
        end_date: undefined,
        sponsor: undefined,
        school_id: undefined,
        department_id: undefined,
        term_id: undefined,
        program_type_id: undefined,
      },
      statusOptions: [
        { id: 'pending', title: 'غير مفعل' },
        { id: 'active', title: 'مفعل' },
        { id: 'suspended', title: 'موقوف' },
      ],
    }
  },
  computed: {
    isFilter() {
      return Object.keys(this.advanceSearch).some(key => this.advanceSearch[key])
    },
    schools() {
      return this.$store.getters.schools
    },
    departments() {
      return this.advanceSearch.school_id
        ? this.$store.getters.departments.filter(e => e.parent_entity_id === this.advanceSearch.school_id)
        : this.$store.getters.departments
    },
    programs() {
      return this.advanceSearch.department_id
        ? this.$store.getters.programsByEntityId(this.advanceSearch.department_id)
        : this.$store.getters.programsOptions
    },
    terms() {
      return this.$store.getters.terms.map(term => ({
        id: term.id,
        title: `${this.$t('terms.seasons.' + term.title)} - ${term.year}`,
      }))
    },
    programTypes() {
      return this.$store.getters.programTypes
    },
  },
  methods: {
    clearFilter() {
      Object.keys(this.advanceSearch).forEach(key => (this.advanceSearch[key] = undefined))
      this.$router.replace({ query: this.advanceSearch })
    },
    apply() {
      this.$router.replace({ query: this.advanceSearch }).catch(() => {})
      this.$emit('apply', this.advanceSearch)
    },
  },
  mounted() {
    const query = this.$route.query
    this.$nextTick(() => {
      Object.keys(this.advanceSearch).forEach(key => {
        if (query[key]) {
          this.advanceSearch[key] = isNaN(query[key]) ? query[key] : +query[key]
          if (!this.collapse && key !== 'search') this.collapse = true
        }
      })
    })

    if (this.type !== 'graduation-requests') {
      this.$store.dispatch('getProgramTypes')
    }
    if (this.type === 'graduations') {
      this.$store.dispatch('getTerms')
    }
  },
}
</script>
