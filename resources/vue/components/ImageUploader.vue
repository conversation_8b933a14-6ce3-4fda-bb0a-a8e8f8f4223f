<template>
    <div class="image-uploader row">
        <image-viewer v-if="viewImageSrc" :viewImageSrc="viewImageSrc" @close="viewImageSrc = null" />
        <div v-for="(image, index) in currentImages" :key="index" class="col-lg-3 col-md-4 col-12 mb-4">
            <div v-if="!image.load" class="img-thumbnail">
                <span
                    v-if="!required || multiple"
                    @click="remove(image)"
                    class="remove-btn btn btn-sm btn-icon btn-circle shadow"
                >
                    <i class="la la-remove"></i>
                </span>
                <span
                    v-else
                    @click="$refs.fileInput.click()"
                    class="uploader-btn btn btn-sm btn-icon btn-circle shadow"
                >
                    <i class="fa fa-pen"></i>
                </span>
                <img class="w-100 h-100" :src="image.image" alt="Image 1" @click="viewImageSrc = image.image" />
            </div>
            <div v-else class="img-thumbnail d-flex align-items-center justify-content-center">
                <b-spinner variant="primary" label="Text Centered"></b-spinner>
            </div>
        </div>
        <div v-show="multiple || images.length === 0" class="col-lg-3 col-md-4 col-12 mb-4">
            <div
                :class="[{ 'is-dragover': dragover }, 'box']"
                @dragenter="dragover = true"
                @dragleave="dragover = false"
            >
                <input
                    @change="getImages"
                    accept="image/*"
                    class="box__file"
                    type="file"
                    ref="fileInput"
                    :multiple="multiple"
                />
                <div class="box__input">
                    <p class="box__text"><strong>اختر صورة</strong><span> أو قم بإلقائها هنا</span>.</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ImageViewer from '@/components/ImageViewer'
import Compressor from 'compressorjs'

export default {
    name: 'ImageUploader',
    components: { ImageViewer },
    props: {
        images: {
            type: Array,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        required: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            dragover: false,
            viewImageSrc: null,
        }
    },
    computed: {
        currentImages() {
            return this.images.filter(image => !image.removed)
        },
    },
    methods: {
        remove(image) {
            const index = this.images.indexOf(image)
            this.images.splice(index, 1)
            if (image.id && this.multiple) {
                image.removed = true
                this.images.push(image)
            }
        },
        getImages(event) {
            this.dragover = false
            let files = event.target.files
            for (let index in files) {
                if (files.hasOwnProperty(index)) {
                    let file = files[index]
                    if (!this.multiple) this.images.splice(0, this.images.length)
                    if (/image.*/.test(file.type)) {
                        this.compress(file)
                    }
                }
            }
            let input = this.$refs.fileInput
            input.type = 'text'
            input.type = 'file'
        },
        compress(file) {
            const imageObj = { load: true }
            this.images.push(imageObj)

            new Compressor(file, {
                quality: 0.6,
                convertSize: '1000000',
                maxWidth: '1920',
                success: result => {
                    this.reader(result, imageObj)
                },
                error: () => {
                    this.images.splice(this.images.indexOf(imageObj), 1)
                    this.$utils.alert(this.$t('msg.uploading_image_error'), 'error')
                },
            })
        },
        reader(file, imageObj) {
            const reader = new FileReader()
            reader.onload = readerEvent => {
                imageObj.file = file
                imageObj.image = readerEvent.target.result
                imageObj.load = false
            }
            reader.readAsDataURL(file)
        },
    },
}
</script>

<style scoped>
.remove-btn,
.uploader-btn {
    position: absolute;
    top: -10px;
    right: -5px;
    z-index: 9;
    background: white;
}
.remove-btn:hover {
    background: #fd397a;
}
.uploader-btn:hover {
    background: #5d78ff;
}
.remove-btn i,
.uploader-btn i {
    color: #74788d;
}
.remove-btn:hover i,
.uploader-btn:hover i {
    color: white;
}

.box {
    font-size: 1.25rem;
    background-color: #f3f6f9;
    border: 1px solid #ecf0f3;
    position: relative;
    padding: 0;
    display: block;
    outline: 2px dashed #3699ff;
    outline-offset: -10px;
    -webkit-transition: 0.15s ease-in-out, background-color 0.15s linear;
    transition: 0.15s ease-in-out, background-color 0.15s linear;
}
.box__file {
    width: 100%;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: 9;
}
.box__input {
    align-items: center;
    justify-content: center;
    align-content: center;
    display: flex;
}
.box__text {
    white-space: nowrap;
    padding: 10px;
    text-overflow: ellipsis;
    overflow: hidden;
}
.box__file,
.box__input,
.img-thumbnail {
    cursor: pointer;
    height: 250px;
}
.img-thumbnail img {
    object-fit: contain;
}
.box.is-dragover,
.box:hover {
    outline-offset: -20px;
    /*outline-color: #c8dadf;*/
    background-color: #fff;
}
.box.is-dragover .box__text strong,
.box:hover .box__text strong {
    color: #3699ff;
}
</style>
