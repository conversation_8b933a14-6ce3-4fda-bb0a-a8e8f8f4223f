<template>
  <ValidationProvider
    :vid="vid"
    :tag="tag"
    :name="$attrs.label || $attrs.placeholder"
    :rules="rules"
    v-slot="{ errors }"
  >
    <b-form-group :label-size="labelSize" v-bind="$attrs" :class="inputClass">
      <template v-if="$attrs.label" slot="label">
        <label>
          {{ $attrs.label }}
          <span v-if="isRequired" class="text-danger">*</span>
        </label>
      </template>
      <div :class="[{ 'is-invalid': !!errors[0] }]">
        <quill-editor class="editor" :value="innerValue" :options="options" @change="onEditorChange" />
        <!-- <quill-editor class="editor" v-model="innerValue" :options="options" @change="onEditorChange" /> -->
        <!-- <ckeditor :editor="editor" :config="editorConfig" v-model="innerValue"> </ckeditor> -->
      </div>
      <b-form-invalid-feedback v-if="rules">{{ errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </ValidationProvider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

import { Quill, quillEditor } from 'vue-quill-editor'
import Compressor from 'compressorjs'
import ImageUploader from 'quill-image-uploader'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'

import ApiService from '@/common/api.service'

Quill.register('modules/imageUploader', ImageUploader)

export default {
  name: 'RichEditor',
  components: {
    ValidationProvider,
    quillEditor,
  },
  props: {
    vid: {
      type: String,
    },
    rules: {
      type: [Object, String],
      default: '',
    },
    // must be included in props
    value: {
      type: String,
      default: '',
    },
    labelSize: {
      type: String,
      default: 'md',
    },
    inputClass: {
      type: String,
      default: null,
    },
    tag: {
      type: String,
      default: 'div',
    },
    lang: {
      type: String,
      default: null,
    },
    advance: {
      type: Boolean,
      default: false,
    },
    withImage: {
      type: Boolean,
      default: false,
    },
    uploadImagePath: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      innerValue: '',
      options: {
        theme: 'snow',
        placeholder: this.$attrs?.placeholder || '',
        modules: {
          toolbar: { container: [] },
          imageUploader: null,
        },
      },
    }
  },
  watch: {
    // Handles internal model changes.
    innerValue(newVal) {
      this.$emit('input', newVal)
    },
    // Handles external model changes.
    value(newVal) {
      this.innerValue = newVal || ''
    },
  },
  computed: {
    isRequired() {
      if (typeof this.rules === 'object') {
        return !!this.rules.required
      } else {
        return this.rules.split('|').includes('required')
      }
    },
  },
  created() {
    if (this.value) {
      this.innerValue = this.value || ''
    }

    if (this.advance) {
      this.options.modules.toolbar.container = [
        ['bold', 'italic', 'underline', 'clean'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        ['blockquote'],
        [{ direction: 'rtl' }, { align: [] }],
        [{ color: [] }, { background: [] }],
        ['link', 'image', 'video'],
      ]
    } else {
      this.options.modules.toolbar.container = [
        ['bold', 'italic', 'underline', 'clean'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ direction: 'rtl' }, { align: [] }],
        ['link'],
      ]
      if (this.withImage) {
        this.options.modules.toolbar.container.push(['image'])
      }
    }

    if (this.uploadImagePath) {
      this.options.modules.imageUploader = {
        upload: this.setUpImageUploader,
      }
    }
  },
  methods: {
    setUpImageUploader(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 2 * 1024 * 1024) {
          this.$notifyError('يجب ان يكون حجم الصورة اقل من 2MB')
          reject('error')
          return
        }
        const formData = new FormData()
        new Compressor(file, {
          quality: 0.7,
          maxWidth: '1280',
          success: result => {
            formData.append('image', result, file.name)
            ApiService.post(this.uploadImagePath, formData)
              .then(({ data }) => resolve(data))
              .catch(response => {
                const msg = response && response.error ? response.error.message : this.$t('msg.error')
                this.$notifyError(msg)
                reject(msg)
              })
          },
          error: error => {
            this.$notifyError(this.$t('msg.error'))
            reject(error)
          },
        })
      })
    },
    onEditorChange(value) {
      this.innerValue = value.html
      this.$emit('change', value.html)
    },
  },
}
</script>

<style lang="scss">
[dir='rtl'] .ql-snow .ql-tooltip a.ql-action::after {
  margin-left: 16px;
  margin-right: initial;
}
[dir='rtl'] .ql-snow .ql-tooltip::before {
  margin-right: 8px;
  margin-left: initial;
}
.ql-editor {
  font-size: 1rem;
  line-height: 1.5em;
  font-weight: 400;
  color: #495057 !important;
}
.ql-video {
  width: 500px;
  height: 390px;
  margin: auto;
}
.ql-direction {
  transform: scaleX(-1);
}
.ql-align {
  .ql-picker-label,
  .ql-picker-item {
    transform: scaleX(-1);
  }
}
</style>
