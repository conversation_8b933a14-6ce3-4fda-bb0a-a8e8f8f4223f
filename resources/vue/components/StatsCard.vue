<template>
  <div class="stats-card d-flex justify-content-between align-items-center">
    <div>
      <span class="stats-card__title font-weight-bolder font-size-h2 mb-0 mt-6 d-block">{{ title }}</span>
      <span class="stats-card__subtitle">{{ subtitle }}</span>
    </div>
    <span :class="`btn btn-label-${iconColor} btn-lg btn-icon btn-circle`">
      <i :class="icon"></i>
    </span>
  </div>
</template>

<script>
export default {
  name: 'StatsCard',
  props: {
    title: {
      type: [String, Number],
      default: '',
    },
    subtitle: {
      type: String,
      default: '',
    },
    icon: {
      type: String,
      default: '',
    },
    iconColor: {
      type: String,
      default: 'brand',
    },
  },
}
</script>

<style lang="scss">
.stats-card {
  padding: 2rem 2.25rem;
  background-color: white;
  box-shadow: 0 0 30px 0 rgba(82, 63, 105, 0.05);
  border-radius: 0.42rem;
  color: #3f4254 !important;

  &__title {
    font-size: 2rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
  }

  &__subtitle {
    font-size: 1.25rem !important;
    font-weight: 400 !important;
  }
}
</style>
