<template>
  <div class="kt-pagination kt-pagination--brand">
    <b-pagination
      class="kt-pagination__links"
      v-model="current"
      :total-rows="sizeArray"
      :per-page="maxItems"
      last-number
      first-number
    >
      <template #prev-text><i class="fa fa-angle-left"></i></template>
      <template #next-text><i class="fa fa-angle-right"></i></template>
    </b-pagination>

    <div class="kt-pagination__toolbar">
      <b-dropdown
        v-if="!$parent.hidePerPage"
        :text="maxItems.toString()"
        variant="label-brand"
        block
        menu-class="w-100"
      >
        <b-dropdown-item
          v-for="(number, index) in [10, 20, 30, 50, 100]"
          :key="index"
          :active="number === maxItems"
          @click="changeRowMaxItems(number)"
        >
          {{ number }}
        </b-dropdown-item>
      </b-dropdown>
      <span class="pagination__desc ml-3">
        {{ $t('table.view') }} {{ dataMeta.from }} - {{ dataMeta.to }} {{ $t('table.from') }} {{ dataMeta.of }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VPagination',
  props: {
    color: {
      type: String,
      default: 'primary',
    },
    sizeArray: {
      type: Number,
      required: true,
    },
    maxItems: {
      type: [Number, String],
      required: true,
    },
    value: {
      type: Number,
      required: true,
      default: 1,
    },
    type: {
      type: String,
    },
  },
  data() {
    return {
      pages: [],
      current: 1,
      minRows: 0,
      maxRows: 0,
    }
  },
  watch: {
    current(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    },
    value(val) {
      setTimeout(() => {
        this.current = val
      }, 1000)
    },
  },
  computed: {
    dataMeta() {
      const to = this.maxItems * (this.current - 1) + (this.sizeArray ? this.maxItems : 0)
      const from = this.maxItems * (this.current - 1) + (this.sizeArray ? 1 : 0)
      return { from: from, to: to > this.sizeArray ? this.sizeArray : to, of: this.sizeArray }
    },
  },
  mounted() {
    this.current = this.value
  },
  methods: {
    changeRowMaxItems(value) {
      this.$emit('changeMaxItems', value)
      this.current = 1
    },
  },
}
</script>

<style lang="scss">
.kt-pagination {
  .page-item.active,
  .page-item {
    background-color: #fff;
    &.active,
    &:hover {
      .page-link {
        color: #fff !important;
        background-color: #5d78ff;
      }
    }

    .page-link {
      border: none;
      border-radius: 4px;
      color: #74788d;

      &[role='menuitem'] {
        color: #5d78ff;
        background-color: rgba(93, 120, 255, 0.1);
      }
    }
  }
}
</style>
