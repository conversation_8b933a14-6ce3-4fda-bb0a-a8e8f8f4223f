<template>
    <transition name="tr-expand">
        <tr v-if="active" class="kt-datatable__row-detail">
            <td class="kt-datatable__detail" :colspan="colspan">
                <slot></slot>
                <!--                    <button v-if="close" class="tr-expand&#45;&#45;close" @click="$emit('click', $event)">-->
                <!--                        clear-->
                <!--                    </button>-->
            </td>
        </tr>
    </transition>
</template>

<script>
export default {
    props: {
        close: {
            type: Boolean,
            default: false,
        },
        colspan: {
            default: 1,
            type: Number,
        },
    },
    data: () => ({
        active: false,
    }),
    mounted() {
        this.active = true
    },
}
</script>
