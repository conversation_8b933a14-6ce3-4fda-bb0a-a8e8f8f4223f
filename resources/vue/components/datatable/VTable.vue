<template>
  <div class="">
    <div class="row px-4 mt-4">
      <div v-if="search" class="col-lg-4">
        <text-input v-model="searchx" :placeholder="searchPlaceholder || $t('table.search')" icon="la la-search" />
      </div>
      <slot name="toolbar"></slot>
    </div>
    <div class="kt-datatable kt-datatable--default kt-datatable--brand kt-datatable--loaded">
      <table ref="table" class="kt-datatable__table table-striped d-block">
        <thead class="kt-datatable__head" ref="thead">
          <tr class="kt-datatable__row">
            <th v-if="hasExpadableData" class="kt-datatable__cell kt-datatable__toggle-detail">
              <span></span>
            </th>
            <th v-if="multiple" class="kt-datatable__cell--center kt-datatable__cell kt-datatable__cell--check">
              <span style="width: 20px;">
                <label class="kt-checkbox kt-checkbox--single kt-checkbox--all kt-checkbox--solid">
                  <!-- <input type="checkbox" :checked="isCheckedMultiple" @click="changeCheckedMultiple">-->
                  <input type="checkbox" @click="changeCheckedMultiple" />
                  <!--                                    &nbsp;<span></span>-->
                </label>
              </span>
            </th>
            <v-th v-if="indexed">#</v-th>
            <v-th v-for="head in heads" :key="head.key" :sort-key="head.sort && head.key">
              {{ head.name }}
            </v-th>
          </tr>
        </thead>
        <tbody class="kt-datatable__body">
          <v-tr v-for="(row, index) in datax" :key="index">
            <v-td v-if="indexed">{{ index + 1 + maxItemsx * (currentx - 1) }}</v-td>
            <v-td v-for="head in heads" :key="`${head.key}_${index}`">
              <slot :name="`td-${head.key}`" :row="row">{{ row[head.key] }}</slot>
            </v-td>
            <template v-if="hasExpadableData" slot="expand">
              <table>
                <tr v-for="(head, headIndex) in expendHeads" :key="`${headIndex}_${index}`"
                  :colspan="expendHeads.length" class="kt-datatable__row">
                  <td class="kt-datatable__cell">{{ head.name }}</td>
                  <td class="kt-datatable__cell">
                    <slot :name="`td-${head.key}`" :row="row">{{ row[head.key] }}</slot>
                  </td>
                </tr>
              </table>
            </template>
          </v-tr>

          <slot name="append-tbody"></slot>
        </tbody>
      </table>
      <div v-if="isNoData" class="p-5 d-flex justify-content-center">
        {{ noDataText || $t('table.no_data') }}
      </div>
    </div>
    <v-pagination v-if="pagination" class="p-4" :sizeArray="!sst ? queriedResults.length : total" :maxItems="maxItemsx"
      :description="description" :descriptionItems="descriptionItems" @changeMaxItems="changeMaxItems"
      v-model="currentx" />
  </div>
</template>

<script>
import VPagination from '@/components/VPagination.vue'
import TextInput from '@/components/form-controls/TextInput'

import KTUtil from '@/assets/js/util'
import VTd from '@/components/datatable/VTd'
import VTr from '@/components/datatable/VTr'
import VTh from '@/components/datatable/VTh'

export default {
  name: 'VTable',
  components: { VTh, VTr, VTd, TextInput, VPagination },
  props: {
    value: {},
    color: {
      default: 'brand',
      type: String,
    },
    noDataText: {
      default: null,
      type: String,
    },
    stripe: {
      default: false,
      type: Boolean,
    },
    hoverFlat: {
      default: false,
      type: Boolean,
    },
    maxHeight: {
      default: 'auto',
      type: String,
    },
    multiple: {
      default: false,
      type: Boolean,
    },
    data: {
      default: null,
    },
    headers: {
      required: true,
    },
    notSpacer: {
      default: false,
      type: Boolean,
    },
    search: {
      default: false,
      type: Boolean,
    },
    maxItems: {
      default: 20,
      type: [Number, String],
    },
    pagination: {
      default: false,
      type: Boolean,
    },
    indexed: {
      type: Boolean,
      default: false,
    },
    description: {
      default: false,
      type: Boolean,
    },
    descriptionItems: {
      default: () => [],
      type: Array,
    },
    currentPage: {
      default: 1,
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: Number | String,
    },
    sst: {
      default: false,
      type: Boolean,
    },
    total: {
      type: Number,
      default: 0,
    },
    expendedInMobile: {
      type: Boolean,
      default: true,
    },
    hidePerPage: {
      type: Boolean,
      default: false,
    },
    searchPlaceholder: {
      default: null,
      type: String,
    },
  },
  data: () => ({
    headerWidth: '100%',
    trs: [],
    datax: [],
    searchx: null,
    currentx: 1,
    maxItemsx: 10,
    hasExpadableData: false,
    currentSortKey: null,
    currentSortType: null,
  }),
  computed: {
    heads() {
      return this.headers.filter(h => (this.hasExpadableData ? !h.expend : true))
    },
    expendHeads() {
      return this.headers.filter(h => h.expend)
    },
    queriedResults() {
      let queriedResults = this.data
      if (this.searchx && this.search) {
        let dataBase = this.data
        queriedResults = dataBase.filter(tr => {
          let values = this.getValues(tr)
            .toString()
            .toLowerCase()
          return values.indexOf(this.searchx.toLowerCase()) != -1
        })
      }
      return queriedResults
    },
    isNoData() {
      if (typeof this.datax == Object) {
        return this.datax ? Object.keys(this.datax).length == 0 : false && this.search
      } else {
        return this.datax ? this.datax.length == 0 : false && this.search
      }
    },
    isCheckedLine() {
      let lengthx = this.data.length
      let lengthSelected = this.value.length
      return lengthx !== lengthSelected
    },
    isCheckedMultiple() {
      return this.value.length > 0
    },
    styleConTbody() {
      return {
        maxHeight: this.maxHeight,
        overflow: this.maxHeight != 'auto' ? 'auto' : null,
      }
    },
    getThs() {
      let ths = this.$slots.thead.filter(item => item.tag)
      return ths.length
    },
    tableHeaderStyle() {
      return {
        width: this.headerWidth,
      }
    },
  },
  watch: {
    currentPage() {
      this.currentx = this.currentPage
    },
    currentx() {
      if (this.sst) {
        this.$emit('change-page', this.currentx)
      } else {
        this.loadData()
      }
    },
    maxItems(val) {
      this.maxItemsx = val
      this.loadData()
    },
    maxItemsx() {
      this.$emit('max-items', this.maxItemsx)
      this.loadData()
    },
    data() {
      this.loadData()
      this.$nextTick(() => {
        if (this.datax.length > 0) {
          this.changeTdsWidth()
        }
      })
    },
    searchx() {
      if (this.sst) {
        this.$emit('search', this.searchx)
        this.currentx = 1
      } else {
        this.loadData()
        this.currentx = 1
      }
    },
  },
  mounted() {
    window.addEventListener('resize', this.listenerChangeWidth)
    this.maxItemsx = this.maxItems
    this.currentx = this.currentPage
    this.loadData()

    this.hasExpadableData = this.expendedInMobile && this.expendHeads.length > 0 && KTUtil.isInResponsiveRange('mobile')

    // this.$nextTick(() => {
    //   if(this.datax.length > 0) {
    //     this.changeTdsWidth()
    //   }
    // })
  },
  destroyed() {
    window.removeEventListener('resize', this.listenerChangeWidth)
  },
  methods: {
    loadData() {
      let max = Math.ceil(this.currentx * this.maxItemsx)
      let min = max - this.maxItemsx
      if (this.sst) {
        this.datax = this.data
      } else if (!this.searchx) {
        this.datax = this.pagination ? this.getItems(min, max) : this.sortItems(this.data || [])
      } else {
        // this.datax = this.pagination ? this.getItemsSearch(true ,min, max) : this.getItemsSearch(false ,min, max) || []
        this.datax = this.getItemsSearch(min, max)
      }
    },
    getItems(min, max) {
      let dataBase = this.sortItems(this.data)

      let items = []
      dataBase.forEach((item, index) => {
        if (index >= min && index < max) {
          items.push(item)
        }
      })
      return items
    },
    sortItems(data) {
      const { currentSortKey, currentSortType } = this
      function compare(a, b) {
        if (a[currentSortKey] < b[currentSortKey]) return currentSortType == 'desc' ? 1 : -1
        if (a[currentSortKey] > b[currentSortKey]) return currentSortType == 'desc' ? -1 : 1
        return 0
      }
      return currentSortType !== null ? [...data].sort(compare) : [...data]
    },
    getItemsSearch(min, max) {
      const search = this.normalize(this.searchx)
      return this.sortItems(this.data)
        .filter(tr => {
          return this.normalize(this.getValues(tr).toString()).indexOf(search) != -1
        })
        .filter((_, index) => {
          return index >= min && index < max
        })
    },
    sort(key, sortType) {
      this.currentSortKey = key
      this.currentSortType = sortType
      if (this.sst) {
        this.$emit('sort', key, sortType)
        return
      }
      this.loadData()
    },
    normalize(string) {
      return string
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
    },
    getValues: function getValues(obj) {
      function flattenDeep(val) {
        return Object.values(val || []).reduce(
          (acc, val) => (typeof val === 'object' ? acc.concat(flattenDeep(val)) : acc.concat(val)),
          []
        )
      }

      return flattenDeep(obj).filter(function (item) {
        return typeof item === 'string' || typeof item === 'number'
      })
    },
    changeCheckedMultiple() {
      let lengthx = this.data.length
      let lengthSelected = this.value.length
      let selectedx = lengthx - lengthSelected
      if (selectedx == 0) {
        this.$emit('input', [])
      } else {
        this.$emit('input', this.data)
      }
    },
    clicktr(tr, isTr) {
      if (this.multiple && isTr) {
        let val = this.value.slice(0)
        if (val.includes(tr)) {
          val.splice(val.indexOf(tr), 1)
        } else {
          val.push(tr)
        }

        this.$emit('input', val)
        this.$emit('selected', tr)
      } else if (isTr) {
        this.$emit('input', tr)
        this.$emit('selected', tr)
      }
    },
    dblclicktr(tr, isTr) {
      if (isTr) {
        this.$emit('dblSelection', tr)
      }
    },
    listenerChangeWidth() {
      this.headerWidth = `${this.$refs.table.offsetWidth}px`
      this.changeTdsWidth()
      this.hasExpadableData =
        this.expendedInMobile && this.expendHeads.length > 0 && KTUtil.isInResponsiveRange('mobile')
    },
    changeTdsWidth() {
      if (!this.value) return

      let tbody = this.$refs.table.querySelector('tbody')

      // Adding condition removes querySelector none error - if tbody isnot present
      if (tbody) {
        let trvs = tbody.querySelector('.tr-values')
        if (trvs === undefined || trvs === null) return
        let tds = trvs.querySelectorAll('.td')

        let tdsx = []

        tds.forEach((td, index) => {
          tdsx.push({ index: index, widthx: td.offsetWidth })
        })

        let colgrouptable = this.$refs.colgrouptable
        if (colgrouptable !== undefined && colgrouptable !== null) {
          let colsTable = colgrouptable.querySelectorAll('.col')
          colsTable.forEach((col, index) => {
            col.setAttribute('width', tdsx[index].widthx)
          })
        }
      }
    },
    changeMaxItems(index) {
      this.maxItemsx = index
    },
  },
  // created() {
  //   this.searchx = this.$route.query.search || null
  // }
}
</script>

<style lang="scss">
.kt-datatable {
  width: 100%;
  min-height: 30vh;
  overflow: auto;

  &__table {
    overflow: visible !important;

    display: table !important;

    thead {
      display: table-header-group !important;
    }

    tbody {
      display: table-row-group !important;
    }

    tr {
      display: table-row !important;
    }
  }

  &__head {
    .kt-datatable__cell {
      white-space: nowrap;
      color: #595d6e;
      background-color: #ebedf2 !important;
      border-color: #ebedf2 !important;

      >span {
        overflow: visible !important;
      }
    }
  }

  &__cell {
    >span {
      overflow: visible !important;
    }
  }

  &__detail {
    display: table-cell !important;
    padding: 0 20px !important;
  }

  &__cell--check,
  &__toggle-detail {
    width: 20px;
  }
}
</style>
