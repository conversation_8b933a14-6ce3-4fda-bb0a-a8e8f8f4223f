<template>
    <!--    kt-datatable__row-active-->
    <tr
        ref="tableTr"
        :class="[
            'kt-datatable__row',
            {
                'is-selected': isSelected,
            },
        ]"
        @click="clicktr"
    >
        <td v-if="$parent.multiple" class="kt-datatable__cell--center kt-datatable__cell kt-datatable__cell--check">
            <span>
                <label class="kt-checkbox kt-checkbox--single kt-checkbox--solid">
                    <input type="checkbox" @click="clicktr" :checked="isSelected" />
                    &nbsp;<span></span>
                </label>
            </span>
        </td>
        <td v-if="hasExpended" class="kt-datatable__cell kt-datatable__toggle-detail cursor-pointer">
            <span class="kt-datatable__toggle-detail">
                <i :class="['fa', expanded ? 'fa-caret-down' : 'fa-caret-left']"></i>
            </span>
        </td>

        <slot></slot>
    </tr>
</template>

<script>
import Vue from 'vue'
import VTrExpand from '@/components/datatable/VTrExpand'

export default {
    name: 'VTr',
    props: {
        state: {
            type: String,
            default: null,
        },
        data: {
            default: null,
        },
    },
    data: () => ({
        colspan: 0,
        expanded: false,
        maxHeight: '0px',
        activeEdit: false,
    }),
    watch: {
        '$parent.datax'() {
            this.collapseExpandedData()
        },
    },
    computed: {
        styleExpand() {
            return {
                maxHeight: this.maxHeight,
            }
        },
        getColspanExpand() {
            let lengthx = this.$parent.$refs.colgroup.querySelectorAll('th').length
            return lengthx
        },
        isSelected() {
            if (this.$parent.multiple && this.$parent.value) {
                return this.data ? this.$parent.value.includes(this.data) : false
            } else {
                return this.data ? this.$parent.value == this.data : false
            }
        },
        hasExpended() {
            return this.$parent.hasExpadableData
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.colspan = this.$parent.$refs.thead.querySelectorAll('th').length
            if (this.$slots.expand) {
                this.colspan++
            }
        })
    },
    created() {
        if (this.$slots.expand) this.$parent.hasExpadableData = true
    },
    methods: {
        insertAfter(e, i) {
            if (e.nextSibling) {
                e.parentNode.insertBefore(i, e.nextSibling)
            } else {
                e.parentNode.appendChild(i)
            }
        },
        clicktr(evt) {
            this.$parent.clicktr(this.data, true)

            if (this.$slots.expand) {
                this.clicktd(evt)
            }
        },
        dblclicktr() {
            this.$parent.dblclicktr(this.data, true)
        },
        clicktd(evt) {
            if (this.$parent.multiple || !this.$slots.expand) return
            let tr = evt.target.closest('tr')
            if (this.expanded) {
                tr.parentNode.removeChild(tr.nextSibling)
                tr.classList.remove('tr-expanded')
                this.expanded = false
            } else {
                tr.classList.add('tr-expanded')
                let trx = Vue.extend(VTrExpand)
                let instance = new trx()
                instance.$props.colspan = this.colspan
                instance.$slots.default = this.$slots.expand
                instance.vm = instance.$mount()
                let newTR = document.createElement('tr').appendChild(instance.vm.$el)
                this.insertAfter(tr, newTR)
                this.expanded = true
            }
        },
        collapseExpandedData() {
            if (this.expanded) {
                const tr = this.$refs.tableTr
                tr.parentNode.removeChild(tr.nextSibling)
                tr.classList.remove('tr-expandedx')
                this.expanded = false
            }
        },
    },
}
</script>
