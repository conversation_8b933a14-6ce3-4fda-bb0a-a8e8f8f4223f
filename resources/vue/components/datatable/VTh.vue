<template>
    <th
        @click="sortValue"
        class="kt-datatable__cell"
        :class="{
            'kt-datatable__cell--sort': sortKey,
            'kt-datatable__cell--sorted': isColumnSelectedForSort && currentSort != 0,
        }"
    >
        <span>
            <slot></slot>
            <template v-if="isColumnSelectedForSort && currentSort != 0">
                <i :class="currentSort === 1 ? 'flaticon2-arrow-up' : 'flaticon2-arrow-down'"></i>
            </template>
        </span>
    </th>
</template>

<script>
export default {
    name: 'VTh',
    props: {
        sortKey: {
            default: null,
            type: String,
        },
    },
    data: () => ({
        thIndex: 0,
        thwidth: '100%',
        currentSort: 0,
        sortStatuses: [null, 'asc', 'desc'],
    }),
    computed: {
        styleth() {
            return {
                width: this.thwidth,
            }
        },
        isColumnSelectedForSort() {
            if (!this.sortKey) {
                return false
            }
            if (this.$parent.currentSortKey != this.sortKey) {
                this.resetSort()
            }
            return this.$parent.currentSortKey == this.sortKey
        },
        parentSortStatus() {
            return this.$parent.currentSortType
        },
    },
    methods: {
        sortValue() {
            if (!this.sortKey) return
            this.currentSort = this.currentSort !== 2 ? this.currentSort + 1 : 0
            this.$parent.sort(this.sortKey, this.sortStatuses[this.currentSort])
        },
        resetSort() {
            this.currentSort = 0
        },
    },
}
</script>
