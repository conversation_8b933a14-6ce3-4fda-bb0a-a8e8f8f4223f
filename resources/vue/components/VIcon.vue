<template>
    <i :style="iconStyle" :class="[icon, iconClass]"> </i>
</template>
<script>
export default {
    name: 'VIcon',
    props: {
        icon: {
            default: null,
            type: String,
        },
        variant: {
            default: null,
            type: String,
        },
        size: {
            default: null,
            type: String,
        },
    },
    computed: {
        iconClass() {
            const classes = {}
            if (this.size) classes[this.size] = true
            if (this.variant) classes[`kt-font-${this.variant}`] = true

            return classes
        },
        iconStyle() {
            return {
                width: /(px)/.test(this.size) ? this.size : /(em)/.test(this.size) ? this.size : null,
                height: /(px)/.test(this.size) ? this.size : /(em)/.test(this.size) ? this.size : null,
                'font-size': /(px)/.test(this.size) ? this.size : /(em)/.test(this.size) ? this.size : null,
            }
        },
    },
}
</script>
