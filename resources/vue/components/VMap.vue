<template>
  <div class="v-map">
    <vl-map
      :load-tiles-while-animating="true"
      :load-tiles-while-interacting="true"
      @singleclick="onMapClick"
      data-projection="EPSG:4326"
      :style="`height: ${height}`"
    >
      <vl-view :zoom="zoom" :center.sync="centerCoordinate"></vl-view>

      <vl-feature v-if="coordinate.length > 1">
        <vl-geom-point :coordinates="coordinate"></vl-geom-point>
        <vl-style-box :z-index="2">
          <vl-style-icon src="/assets/media/marker-icon.png" :anchor="[0.5, 1]"></vl-style-icon>
        </vl-style-box>
        <vl-style-box :z-index="1">
          <vl-style-icon src="/assets/media/marker-shadow.png" :anchor="[0.32, 1]"></vl-style-icon>
        </vl-style-box>
      </vl-feature>

      <vl-layer-tile>
        <vl-source-osm></vl-source-osm>
      </vl-layer-tile>
    </vl-map>
    <div class="v-map__input col-lg-5 col-md-7 col-9">
      <b-input-group size="sm">
        <b-input-group-prepend>
          <v-button @click="setCoordinate" variant="brand" icon-only icon="fa fa-map-marked-alt" />
        </b-input-group-prepend>
        <b-form-input
          @keydown.enter.prevent="setCoordinate"
          dir="ltr"
          placeholder="0.0000, 0.0000"
          v-model="inputValue"
        ></b-form-input>
      </b-input-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VMap',
  props: {
    height: {
      type: String,
      default: '400px',
    },
    value: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    value() {
      this.inputValue = this.stringCoordinate
    },
  },
  data() {
    return {
      centerCoordinate: [],
      zoom: 15,
      inputValue: null,
    }
  },
  computed: {
    coordinate() {
      return this.value.filter(v => v)
    },
    stringCoordinate() {
      return this.coordinate
        .slice()
        .reverse()
        .join(', ')
    },
  },
  methods: {
    onMapClick({ coordinate }) {
      this.$emit('input', this.coordinate.length > 1 ? [] : coordinate)
    },
    setCoordinate() {
      if (!this.inputValue) {
        this.$emit('input', [])
        return
      }

      const newCoordinate = this.inputValue
        .split(',')
        .reverse()
        .map(c => parseFloat(c))

      const isNumbers = newCoordinate.every(c => !isNaN(c))
      if (newCoordinate.length > 1 && isNumbers) {
        this.$emit('input', newCoordinate)
        this.centerCoordinate = newCoordinate
      } else {
        this.inputValue = this.stringCoordinate
      }
    },
  },
  created() {
    const mapLocation = process.env.MIX_MAP_LOCATION
    this.centerCoordinate = mapLocation
      ? mapLocation
          .split(',')
          .map(x => +x)
          .reverse()
      : []
    if (this.coordinate.length > 1) {
      this.centerCoordinate = this.coordinate
      this.inputValue = this.stringCoordinate
    } else {
      this.$emit('input', [])
    }
  },
}
</script>

<style lang="scss">
.v-map {
  position: relative;

  &__input {
    position: absolute;
    top: 5px;
    right: 5px;
  }
}
</style>
