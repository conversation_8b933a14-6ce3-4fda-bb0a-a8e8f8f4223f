import Vue from 'vue'
import App from './App.vue'
import router from './router/index'
import store from './store'
import ApiService from './common/api.service'

Vue.config.productionTip = false

// Global 3rd party plugins
import 'bootstrap'
import 'popper.js'
import 'tooltip.js'
import 'perfect-scrollbar'

// Vue 3rd party plugins
import i18n from './common/plugins/vue-i18n'
import './common/plugins/bootstrap-vue'
import './common/plugins/vee-validate'
import './common/plugins/perfect-scrollbar'
import './common/plugins/highlight-js'
import './common/plugins/vue-sweetalert2'
import './common/plugins/vuelayers'
// import '@babel/polyfill'
import './common/plugins/dayjs'
// import '@mdi/font/css/materialdesignicons.css'

// Import global components
import './globalComponents'

// Import plugins
import './common/plugins/bugsnag'
import utils from './common/plugins/utils'
Vue.use(utils, router)

// TODO: i think i should refactor this but i'm not sure
Vue.prototype.$dayjs.locale(Vue.prototype.$lang)

import NProgress from 'nprogress'

// API service init
ApiService.init()

import AclService from '@/common/acl.service'
Vue.prototype.$acl = AclService

router.beforeEach((to, from, next) => {
  NProgress.configure({ speed: 3000 })
  NProgress.set(0.7)
  NProgress.configure({ speed: 200 })

  const initQueries = [store.dispatch('verifyAuth'), store.dispatch('getCurrentEntity'), store.dispatch('getLanguages')]
  Promise.all(initQueries)
    .then(async () => {
      if (['sms', 'lecturer-panel'].includes(to.matched[0].path.split('/')[1])) await store.dispatch('getActiveTerms')
      if (AclService.has('student')) await store.dispatch('getNotices')

      const canAccess =
        (!to.meta.level || AclService.middleware(to.meta.level)) &&
        (!to.meta.permissions || AclService.can(to.meta.permissions)) &&
        (!to.meta.roles || AclService.has(to.meta.roles)) &&
        (!to.meta.type || to.meta.type === store.getters.auth.type)

      canAccess ? next() : next(to.matched[0].path + '/404')
    })
    .catch(err => console.log(err))

  // Scroll page to top on every route change
  setTimeout(() => window.scrollTo(0, 0), 100)
})

router.afterEach(to => {
  NProgress.done()

  let last = to.meta.breadcrumbs && to.meta.breadcrumbs[to.meta.breadcrumbs.length - 1]
  if (last && last.translate) {
    document.title = i18n.t(last.translate) + ' | ' + store.getters.entity.title
  } else {
    document.title = store.getters.entity.title
  }
})

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount('#app')
