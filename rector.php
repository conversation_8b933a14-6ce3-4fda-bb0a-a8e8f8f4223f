<?php

declare(strict_types=1);

use Rector\Core\Configuration\Option;
use <PERSON>\Set\ValueObject\SetList;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    // get parameters
    $parameters = $containerConfigurator->parameters();

    // Define what rule sets will be applied
    $parameters->set(Option::SETS, [
        SetList::DEAD_CODE,
    ]);

    // get services (needed for register a single rule)
     $services = $containerConfigurator->services();

    // register a single rule
     $services->set(<PERSON>\Php74\Rector\Closure\ClosureToArrowFunctionRector::class);
//     $services->set(<PERSON>\Php74\Rector\Property\RestoreDefaultNullToNullableTypePropertyRector::class);
};
